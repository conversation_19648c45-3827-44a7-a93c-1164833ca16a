/*******************************************************************************/
/*  This file is auto-generated by Azure RTOS GUIX Studio. Do not edit this    */
/*  file by hand. Modifications to this file should only be made by running    */
/*  the Azure RTOS GUIX Studio application and re-generating the application   */
/*  resource file(s). For more information please refer to the Azure RTOS GUIX */
/*  Studio User Guide, or visit our web site at azure.com/rtos                 */
/*                                                                             */
/*  GUIX Studio Revision 6.1.0.0                                               */
/*  Date (dd.mm.yyyy): 15. 8.2023   Time (hh:mm): 16:26                        */
/*******************************************************************************/


#ifndef _DEMO_GUIX_CAR_INFOTAINMENT_MAIN_DISPLAY_RESOURCES_H_
#define _DEMO_GUIX_CAR_INFOTAINMENT_MAIN_DISPLAY_RESOURCES_H_

#include "gx_api.h"
#include "board.h"

/* Display and theme definitions                                               */

#define MAIN_DISPLAY 0
#define MAIN_DISPLAY_COLOR_FORMAT GX_COLOR_FORMAT_565RGB
#define MAIN_DISPLAY_X_RESOLUTION BOARD_LCD_WIDTH
#define MAIN_DISPLAY_Y_RESOLUTION BOARD_LCD_HEIGHT
#define MAIN_DISPLAY_THEME_1 0
#define MAIN_DISPLAY_THEME_TABLE_SIZE 1

/* Language definitions                                                        */

#define LANGUAGE_ENGLISH 0
#define MAIN_DISPLAY_LANGUAGE_TABLE_SIZE 1

/* Color ID definitions                                                        */

#define GX_COLOR_ID_LIGHT_GREEN 29
#define GX_COLOR_ID_WHITE 30
#define GX_COLOR_ID_GREEN 31
#define GX_COLOR_ID_BLUE 32
#define GX_COLOR_ID_GRAY 33
#define GX_COLOR_ID_RED 34
#define MAIN_DISPLAY_COLOR_TABLE_SIZE 35

/* Font ID definitions                                                         */

#define GX_FONT_ID_MIDDLE 4
#define GX_FONT_ID_LARGE_NUMBER_60 5
#define GX_FONT_ID_LARGE 6
#define GX_FONT_ID_SMALL 7
#define GX_FONT_ID_NORMAL 8
#define MAIN_DISPLAY_FONT_TABLE_SIZE 9

/* Pixelmap ID definitions                                                     */

#define GX_PIXELMAP_ID_BG_IMAGE 5
#define GX_PIXELMAP_ID_BG_IMAGEX 6
#define GX_PIXELMAP_ID_BG_OFF 7
#define GX_PIXELMAP_ID_BTN 8
#define GX_PIXELMAP_ID_BTN_ACTIVE 9
#define GX_PIXELMAP_ID_BTN_HOME 10
#define GX_PIXELMAP_ID_BTN_HOME_ACTIVE 11
#define GX_PIXELMAP_ID_BTN_ICON_AUDIO 12
#define GX_PIXELMAP_ID_BTN_ICON_CLIMATE 13
#define GX_PIXELMAP_ID_BTN_ICON_PHONE 14
#define GX_PIXELMAP_ID_BTN_ICON_POWER 15
#define GX_PIXELMAP_ID_BTN_ICON_VEHICLE 16
#define GX_PIXELMAP_ID_BTN_ICON_VEHICLE_ACTIVE 17
#define GX_PIXELMAP_ID_BTN_ICON_VIDEO 18
#define GX_PIXELMAP_ID_CLIMATE_GRADIENT_LEFT 19
#define GX_PIXELMAP_ID_CLIMATE_GRADIENT_LEFT_ACTIVE 20
#define GX_PIXELMAP_ID_CLIMATE_GRADIENT_LEFT_BOTTOM 21
#define GX_PIXELMAP_ID_CLIMATE_GRADIENT_LEFT_BOTTOM_ACTIVE 22
#define GX_PIXELMAP_ID_CLIMATE_GRADIENT_RIGHT 23
#define GX_PIXELMAP_ID_CLIMATE_GRADIENT_RIGHT_ACTIVE 24
#define GX_PIXELMAP_ID_CLIMATE_GRADIENT_RIGHT_BOTTOM 25
#define GX_PIXELMAP_ID_CLIMATE_GRADIENT_RIGHT_BOTTOM_ACTIVE 26
#define GX_PIXELMAP_ID_CLIMATE_LINES_LEFT 27
#define GX_PIXELMAP_ID_CLIMATE_LINES_RIGHT 28
#define GX_PIXELMAP_ID_CLIMATE_LINE_CENTER_DEVIDER 29
#define GX_PIXELMAP_ID_C_ICON_FAN 30
#define GX_PIXELMAP_ID_C_ICON_FAN_ACTIVE 31
#define GX_PIXELMAP_ID_C_ICON_FAN_AUTO 32
#define GX_PIXELMAP_ID_C_ICON_FAN_SMALL 33
#define GX_PIXELMAP_ID_C_ICON_FAN_SMALL_ACTIVE 34
#define GX_PIXELMAP_ID_C_ICON_FAN_SMALL_AUTO 35
#define GX_PIXELMAP_ID_DOT 36
#define GX_PIXELMAP_ID_HOME_LEFT_BATTERY 37
#define GX_PIXELMAP_ID_HOME_LEFT_BATTERY_LEVEL 38
#define GX_PIXELMAP_ID_HOME_LEFT_LINE 39
#define GX_PIXELMAP_ID_HOME_MODE_BTN 40
#define GX_PIXELMAP_ID_HOME_MODE_BTN_ICON_COMFORT 41
#define GX_PIXELMAP_ID_HOME_MODE_BTN_ICON_ECO 42
#define GX_PIXELMAP_ID_HOME_MODE_BTN_ICON_SPORT 43
#define GX_PIXELMAP_ID_HOME_MODE_BTN_OVER 44
#define GX_PIXELMAP_ID_ICON_ARTIST 45
#define GX_PIXELMAP_ID_ICON_LIST 46
#define GX_PIXELMAP_ID_ICON_LIST_ACTIVE 47
#define GX_PIXELMAP_ID_ICON_LOOP 48
#define GX_PIXELMAP_ID_ICON_LOOP_ACTIVE 49
#define GX_PIXELMAP_ID_ICON_PHONE 50
#define GX_PIXELMAP_ID_ICON_PHONE_DIALS 51
#define GX_PIXELMAP_ID_ICON_PHONE_OFF 52
#define GX_PIXELMAP_ID_ICON_RADIO 53
#define GX_PIXELMAP_ID_ICON_RANDOM 54
#define GX_PIXELMAP_ID_ICON_RANDOM_ACTIVE 55
#define GX_PIXELMAP_ID_ICON_SOUND_OFF 56
#define GX_PIXELMAP_ID_ICON_SOUND_ON 57
#define GX_PIXELMAP_ID_INSTRUMENT_CLUSTER_FRAME 58
#define GX_PIXELMAP_ID_INSTRUMENT_CLUSTER_SPEED_INDICATOR_BLUE 59
#define GX_PIXELMAP_ID_INSTRUMENT_CLUSTER_SPEED_LETTER_DRIVE 60
#define GX_PIXELMAP_ID_LLIGHT01 61
#define GX_PIXELMAP_ID_LLIGHT02 62
#define GX_PIXELMAP_ID_LLIGHT03 63
#define GX_PIXELMAP_ID_LLIGHT04 64
#define GX_PIXELMAP_ID_LLIGHT05 65
#define GX_PIXELMAP_ID_LLIGHT06 66
#define GX_PIXELMAP_ID_LLIGHT07 67
#define GX_PIXELMAP_ID_LLIGHT08 68
#define GX_PIXELMAP_ID_LLIGHT09 69
#define GX_PIXELMAP_ID_LLIGHT10 70
#define GX_PIXELMAP_ID_MICROSOFT_AZURE_LOGO 71
#define GX_PIXELMAP_ID_MICROSOFT_LOGO_OFF 72
#define GX_PIXELMAP_ID_PAUSE 73
#define GX_PIXELMAP_ID_PHONE_PHOTO 74
#define GX_PIXELMAP_ID_PHONE_PHOTO_FRAME 75
#define GX_PIXELMAP_ID_PLAY 76
#define GX_PIXELMAP_ID_PLAYER_BACK 77
#define GX_PIXELMAP_ID_PLAYER_BACK_HOVER 78
#define GX_PIXELMAP_ID_PLAYER_CIRCLE 79
#define GX_PIXELMAP_ID_PLAYER_FORWARD 80
#define GX_PIXELMAP_ID_PLAYER_FORWARD_HOVER 81
#define GX_PIXELMAP_ID_RANGE_GREEN_SINGLE_LINE 82
#define GX_PIXELMAP_ID_RANGE_GREY_SINGLE_LINE 83
#define GX_PIXELMAP_ID_SEAT_LEFT 84
#define GX_PIXELMAP_ID_SEAT_RIGHT 85
#define GX_PIXELMAP_ID_SEAT_WARMER_LEFT_SINGLE 86
#define GX_PIXELMAP_ID_SEAT_WARMER_LEFT_SINGLE_ACTIVE 87
#define GX_PIXELMAP_ID_SEAT_WARMER_RIGHT_SINGLE 88
#define GX_PIXELMAP_ID_SEAT_WARMER_RIGHT_SINGLE_ACTIVE 89
#define GX_PIXELMAP_ID_VIDEO_BG_BOTTOM 90
#define GX_PIXELMAP_ID_VIDEO_BG_BOTTOMX 91
#define GX_PIXELMAP_ID_VIDEO_BG_BOTTOMY 92
#define GX_PIXELMAP_ID_VIDEO_BIG_ROUND_BUBBLE 93
#define GX_PIXELMAP_ID_VIDEO_ICON_FILL_SCREEN 94
#define GX_PIXELMAP_ID_VIDEO_ICON_RETURN 95
#define GX_PIXELMAP_ID_VIDEO_ICON_SOUND_OFF 96
#define GX_PIXELMAP_ID_VIDEO_PALY_BIG 97
#define GX_PIXELMAP_ID_VIDEO_PALY_BIG_HOVER 98
#define GX_PIXELMAP_ID_VIDEO_PALY_SMALL 99
#define GX_PIXELMAP_ID_VIDEO_PAUSE_SMALL 100
#define GX_PIXELMAP_ID_VIDEO_PROGRESS_ACTIVE 101
#define GX_PIXELMAP_ID_VIDEO_PROGRESS_ACTIVEX 102
#define GX_PIXELMAP_ID_VIDEO_PROGRESS_BG 103
#define GX_PIXELMAP_ID_VIDEO_PROGRESS_BGX 104
#define GX_PIXELMAP_ID_V_CAR_WITH_CHECK_MARK 105
#define GX_PIXELMAP_ID_V_ICON_AIR_FILTER 106
#define GX_PIXELMAP_ID_V_ICON_BATTERY 107
#define GX_PIXELMAP_ID_V_ICON_BRAKE 108
#define GX_PIXELMAP_ID_V_ICON_LIGHTS 109
#define GX_PIXELMAP_ID_V_ICON_TIRE 110
#define GX_PIXELMAP_ID_V_ICON_WINDSHILED 111
#define GX_PIXELMAP_ID_V_MODE_BTN 112
#define GX_PIXELMAP_ID_V_MODE_BTN_ACTIVE 113
#define GX_PIXELMAP_ID_V_MODE_BTN_ICON_COMFORT 114
#define GX_PIXELMAP_ID_V_MODE_BTN_ICON_ECO 115
#define GX_PIXELMAP_ID_V_MODE_BTN_ICON_SPORT 116
#define GX_PIXELMAP_ID_V_VERTICAL_LINE 117
#define GX_PIXELMAP_ID_WEATHER_ICON_PARTLY_CLOUDY 118
#define GX_PIXELMAP_ID_WEATHER_ICON_PARTLY_CLOUDY_OFF 119
#define GX_PIXELMAP_ID_WEATHER_ICON_PARTLY_CLOUDY_SMALL 120
#define GX_PIXELMAP_ID_WEATHER_ICON_PARTLY_CLOUDY_SMALL_OFF 121
#define MAIN_DISPLAY_PIXELMAP_TABLE_SIZE 122

/* String Ids                                                                  */

#define GX_STRING_ID_STRING_2 1
#define GX_STRING_ID_STRING_3 2
#define GX_STRING_ID_STRING_4 3
#define GX_STRING_ID_STRING_6 4
#define GX_STRING_ID_STRING_7 5
#define GX_STRING_ID_STRING_8 6
#define GX_STRING_ID_STRING_9 7
#define GX_STRING_ID_STRING_10 8
#define GX_STRING_ID_STRING_11 9
#define GX_STRING_ID_OFF 10
#define GX_STRING_ID_STRING_13 11
#define GX_STRING_ID_STRING_14 12
#define GX_STRING_ID_STRING_15 13
#define GX_STRING_ID_STRING_16 14
#define GX_STRING_ID_STRING_17 15
#define GX_STRING_ID_STRING_18 16
#define GX_STRING_ID_STRING_19 17
#define GX_STRING_ID_STRING_20 18
#define GX_STRING_ID_STRING_21 19
#define GX_STRING_ID_STRING_23 20
#define GX_STRING_ID_STRING_24 21
#define GX_STRING_ID_STRING_25 22
#define GX_STRING_ID_STRING_26 23
#define GX_STRING_ID_STRING_27 24
#define GX_STRING_ID_STRING_28 25
#define GX_STRING_ID_STRING_29 26
#define GX_STRING_ID_STRING_30 27
#define GX_STRING_ID_STRING_31 28
#define GX_STRING_ID_STRING_32 29
#define GX_STRING_ID_STRING_33 30
#define GX_STRING_ID_STRING_34 31
#define GX_STRING_ID_STRING_36 32
#define GX_STRING_ID_STRING_38 33
#define GX_STRING_ID_STRING_39 34
#define GX_STRING_ID_STRING_40 35
#define GX_STRING_ID_STRING_41 36
#define GX_STRING_ID_STRING_42 37
#define GX_STRING_ID_STRING_43 38
#define GX_STRING_ID_STRING_44 39
#define GX_STRING_ID_STRING_45 40
#define GX_STRING_ID_STRING_47 41
#define GX_STRING_ID_STRING_49 42
#define GX_STRING_ID_STRING_50 43
#define GX_STRING_ID_STRING_51 44
#define GX_STRING_ID_STRING_52 45
#define GX_STRING_ID_STRING_53 46
#define GX_STRING_ID_STRING_55 47
#define GX_STRING_ID_STRING_56 48
#define GX_STRING_ID_STRING_58 49
#define GX_STRING_ID_STRING_59 50
#define GX_STRING_ID_STRING_60 51
#define GX_STRING_ID_STRING_61 52
#define GX_STRING_ID_STRING_62 53
#define GX_STRING_ID_STRING_63 54
#define GX_STRING_ID_STRING_64 55
#define GX_STRING_ID_STRING_67 56
#define GX_STRING_ID_STRING_68 57
#define GX_STRING_ID_STRING_71 58
#define GX_STRING_ID_STRING_74 59
#define GX_STRING_ID_STRING_75 60
#define GX_STRING_ID_STRING_76 61
#define GX_STRING_ID_STRING_77 62
#define GX_STRING_ID_STRING_78 63
#define GX_STRING_ID_STRING_82 64
#define GX_STRING_ID_STRING_86 65
#define GX_STRING_ID_STRING_87 66
#define GX_STRING_ID_STRING_88 67
#define GX_STRING_ID_STRING_89 68
#define GX_STRING_ID_STRING_90 69
#define GX_STRING_ID_STRING_91 70
#define GX_STRING_ID_STRING_92 71
#define GX_STRING_ID_STRING_93 72
#define GX_STRING_ID_STRING_94 73
#define GX_STRING_ID_STRING_12 74
#define GX_STRING_ID_STRING_95 75
#define GX_STRING_ID_STRING_99 76
#define GX_STRING_ID_ON 77
#define GX_STRING_ID_STRING_1 78
#define GX_STRING_ID_STRING_5 79
#define GX_STRING_ID_STRING_22 80
#define GX_STRING_ID_STRING_35 81
#define GX_STRING_ID_STRING_37 82
#define GX_STRING_ID_STRING_46 83
#define GX_STRING_ID_STRING_48 84
#define GX_STRING_ID_STRING_54 85
#define GX_STRING_ID_STRING_57 86
#define GX_STRING_ID_STRING_65 87
#define GX_STRING_ID_STRING_66 88
#define GX_STRING_ID_STRING_69 89
#define GX_STRING_ID_ECO 90
#define GX_STRING_ID_STRING_72 91
#define GX_STRING_ID_STRING_73 92
#define GX_STRING_ID_STRING_79 93
#define GX_STRING_ID_STRING_80 94
#define GX_STRING_ID_STRING_81 95
#define GX_STRING_ID_STRING_83 96
#define GX_STRING_ID_STRING_84 97
#define GX_STRING_ID_STRING_85 98
#define GX_STRING_ID_STRING_96 99
#define GX_STRING_ID_COMFORT 100
#define GX_STRING_ID_SPORT 101
#define GX_STRING_ID_STRING_100 102
#define GX_STRING_ID_STRING_101 103
#define GX_STRING_ID_STRING_102 104
#define GX_STRING_ID_STRING_103 105
#define GX_STRING_ID_STRING_104 106
#define GX_STRING_ID_STRING_105 107
#define GX_STRING_ID_STRING_106 108
#define GX_STRING_ID_STRING_107 109
#define GX_STRING_ID_STRING_108 110
#define GX_STRING_ID_HI 111
#define GX_STRING_ID_AUTO 112
#define GX_STRING_ID_LOW 113
#define GX_STRING_ID_MED 114
#define GX_STRING_ID_STRING_109 115
#define GX_STRING_ID_STRING_110 116
#define GX_STRING_ID_STRING_111 117
#define GX_STRING_ID_STRING_112 118
#define GX_STRING_ID_STRING_113 119
#define GX_STRING_ID_STRING_114 120
#define GX_STRING_ID_STRING_115 121
#define GX_STRING_ID_STRING_116 122
#define GX_STRING_ID_STRING_117 123
#define MAIN_DISPLAY_STRING_TABLE_SIZE 124

#endif                                       /* sentry                         */
