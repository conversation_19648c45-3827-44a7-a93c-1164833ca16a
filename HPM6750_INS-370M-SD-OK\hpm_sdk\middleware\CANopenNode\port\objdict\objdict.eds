[FileInfo]
FileName=objdict.eds
FileVersion=1
FileRevision=1
LastEDS=
EDSVersion=4.0
Description=
CreationTime=2:52PM
CreationDate=09-18-2019
CreatedBy=
ModificationTime=12:28PM
ModificationDate=02-20-2020
ModifiedBy=

[DeviceInfo]
VendorName=
VendorNumber=0
ProductName=Zephyr RTOS CANopen sample
ProductNumber=0
RevisionNumber=0
BaudRate_10=1
BaudRate_20=1
BaudRate_50=1
BaudRate_125=1
BaudRate_250=1
BaudRate_500=1
BaudRate_800=1
BaudRate_1000=1
SimpleBootUpMaster=0
SimpleBootUpSlave=0
Granularity=0
DynamicChannelsSupported=0
CompactPDO=0
GroupMessaging=0
NrOfRXPDO=4
NrOfTXPDO=4
LSS_Supported=0
;LSS_Type=Server

[DummyUsage]
Dummy0001=0
Dummy0002=0
Dummy0003=0
Dummy0004=0
Dummy0005=0
Dummy0006=0
Dummy0007=0

[Comments]
Lines=0

[MandatoryObjects]
SupportedObjects=3
1=0x1000
2=0x1001
3=0x1018

[1000]
ParameterName=Device type
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=ro
DefaultValue=0x00000000
PDOMapping=0

[1001]
ParameterName=Error register
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0005
AccessType=ro
DefaultValue=0
PDOMapping=1

[1018]
ParameterName=Identity
ObjectType=0x9
;StorageLocation=ROM
SubNumber=0x5

[1018sub0]
ParameterName=max sub-index
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=ro
DefaultValue=4
PDOMapping=0

[1018sub1]
ParameterName=Vendor-ID
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=ro
DefaultValue=0x00000000
PDOMapping=0

[1018sub2]
ParameterName=Product code
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=ro
DefaultValue=0x00000000
PDOMapping=0

[1018sub3]
ParameterName=Revision number
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=ro
DefaultValue=0x00000000
PDOMapping=0

[1018sub4]
ParameterName=Serial number
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=ro
DefaultValue=0x00000000
PDOMapping=0

[OptionalObjects]
SupportedObjects=39
1=0x1002
2=0x1003
3=0x1005
4=0x1006
5=0x1007
6=0x1008
7=0x1009
8=0x100A
9=0x1010
10=0x1011
11=0x1012
12=0x1014
13=0x1015
14=0x1016
15=0x1017
16=0x1019
17=0x1029
18=0x1200
19=0x1400
20=0x1401
21=0x1402
22=0x1403
23=0x1600
24=0x1601
25=0x1602
26=0x1603
27=0x1800
28=0x1801
29=0x1802
30=0x1803
31=0x1A00
32=0x1A01
33=0x1A02
34=0x1A03
35=0x1F50
36=0x1F51
37=0x1F56
38=0x1F57
39=0x1F80

[1002]
ParameterName=Manufacturer status register
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=1

[1003]
ParameterName=Pre-defined error field
ObjectType=0x8
;StorageLocation=RAM
SubNumber=0x9

[1003sub0]
ParameterName=Number of errors
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0005
AccessType=rw
DefaultValue=0x00
PDOMapping=0

[1003sub1]
ParameterName=Standard error field
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub2]
ParameterName=Standard error field
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub3]
ParameterName=Standard error field
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub4]
ParameterName=Standard error field
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub5]
ParameterName=Standard error field
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub6]
ParameterName=Standard error field
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub7]
ParameterName=Standard error field
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub8]
ParameterName=Standard error field
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1005]
ParameterName=COB-ID SYNC message
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000080
PDOMapping=0

[1006]
ParameterName=Communication cycle period
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1007]
ParameterName=Synchronous window length
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1008]
ParameterName=Manufacturer device name
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0009
AccessType=const
DefaultValue=Zephyr RTOS/CANopenNode
PDOMapping=0

[1009]
ParameterName=Manufacturer hardware version
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0009
AccessType=const
DefaultValue=3.00
PDOMapping=0

[100A]
ParameterName=Manufacturer software version
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0009
AccessType=const
DefaultValue=3.00
PDOMapping=0

[1010]
ParameterName=Store parameters
ObjectType=0x8
;StorageLocation=RAM
SubNumber=0x2

[1010sub0]
ParameterName=max sub-index
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0005
AccessType=ro
DefaultValue=1
PDOMapping=0

[1010sub1]
ParameterName=save all parameters
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000003
PDOMapping=0

[1011]
ParameterName=Restore default parameters
ObjectType=0x8
;StorageLocation=RAM
SubNumber=0x2

[1011sub0]
ParameterName=max sub-index
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0005
AccessType=ro
DefaultValue=1
PDOMapping=0

[1011sub1]
ParameterName=restore all default parameters
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000001
PDOMapping=0

[1012]
ParameterName=COB-ID TIME
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=ro
DefaultValue=
PDOMapping=0

[1014]
ParameterName=COB-ID EMCY
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=ro
DefaultValue=$NODEID+0x80
PDOMapping=0

[1015]
ParameterName=inhibit time EMCY
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0006
AccessType=rw
DefaultValue=100
PDOMapping=0

[1016]
ParameterName=Consumer heartbeat time
ObjectType=0x8
;StorageLocation=ROM
SubNumber=0x5

[1016sub0]
ParameterName=max sub-index
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=ro
DefaultValue=4
PDOMapping=0

[1016sub1]
ParameterName=Consumer heartbeat time
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1016sub2]
ParameterName=Consumer heartbeat time
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1016sub3]
ParameterName=Consumer heartbeat time
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1016sub4]
ParameterName=Consumer heartbeat time
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1017]
ParameterName=Producer heartbeat time
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0006
AccessType=rw
DefaultValue=1000
PDOMapping=0

[1019]
ParameterName=Synchronous counter overflow value
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1029]
ParameterName=Error behavior
ObjectType=0x8
;StorageLocation=ROM
SubNumber=0x7

[1029sub0]
ParameterName=max sub-index
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=ro
DefaultValue=6
PDOMapping=0

[1029sub1]
ParameterName=Communication
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=0x00
PDOMapping=0

[1029sub2]
ParameterName=Communication other
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=0x00
PDOMapping=0

[1029sub3]
ParameterName=Communication passive
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=0x01
PDOMapping=0

[1029sub4]
ParameterName=Generic
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=0x00
PDOMapping=0

[1029sub5]
ParameterName=Device profile
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=0x00
PDOMapping=0

[1029sub6]
ParameterName=Manufacturer specific
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=0x00
PDOMapping=0

[1200]
ParameterName=SDO server parameter
ObjectType=0x9
;StorageLocation=ROM
SubNumber=0x3

[1200sub0]
ParameterName=max sub-index
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[1200sub1]
ParameterName=COB-ID client to server
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=ro
DefaultValue=$NODEID+0x600
PDOMapping=0

[1200sub2]
ParameterName=COB-ID server to client
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=ro
DefaultValue=$NODEID+0x580
PDOMapping=0

[1400]
ParameterName=RPDO communication parameter
ObjectType=0x9
;StorageLocation=ROM
SubNumber=0x3

[1400sub0]
ParameterName=max sub-index
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[1400sub1]
ParameterName=COB-ID used by RPDO
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x200
PDOMapping=0

[1400sub2]
ParameterName=transmission type
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=254
PDOMapping=0

[1401]
ParameterName=RPDO communication parameter
ObjectType=0x9
;StorageLocation=ROM
SubNumber=0x3

[1401sub0]
ParameterName=max sub-index
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[1401sub1]
ParameterName=COB-ID used by RPDO
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x300
PDOMapping=0

[1401sub2]
ParameterName=transmission type
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=254
PDOMapping=0

[1402]
ParameterName=RPDO communication parameter
ObjectType=0x9
;StorageLocation=ROM
SubNumber=0x3

[1402sub0]
ParameterName=max sub-index
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[1402sub1]
ParameterName=COB-ID used by RPDO
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x400
PDOMapping=0

[1402sub2]
ParameterName=transmission type
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=254
PDOMapping=0

[1403]
ParameterName=RPDO communication parameter
ObjectType=0x9
;StorageLocation=ROM
SubNumber=0x3

[1403sub0]
ParameterName=max sub-index
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[1403sub1]
ParameterName=COB-ID used by RPDO
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x500
PDOMapping=0

[1403sub2]
ParameterName=transmission type
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=254
PDOMapping=0

[1600]
ParameterName=RPDO mapping parameter
ObjectType=0x9
;StorageLocation=ROM
SubNumber=0x9

[1600sub0]
ParameterName=Number of mapped objects
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1600sub1]
ParameterName=mapped object 1
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1600sub2]
ParameterName=mapped object 2
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1600sub3]
ParameterName=mapped object 3
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1600sub4]
ParameterName=mapped object 4
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1600sub5]
ParameterName=mapped object 5
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1600sub6]
ParameterName=mapped object 6
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1600sub7]
ParameterName=mapped object 7
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1600sub8]
ParameterName=mapped object 8
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1601]
ParameterName=RPDO mapping parameter
ObjectType=0x9
;StorageLocation=ROM
SubNumber=0x9

[1601sub0]
ParameterName=Number of mapped objects
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1601sub1]
ParameterName=mapped object 1
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1601sub2]
ParameterName=mapped object 2
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1601sub3]
ParameterName=mapped object 3
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1601sub4]
ParameterName=mapped object 4
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1601sub5]
ParameterName=mapped object 5
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1601sub6]
ParameterName=mapped object 6
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1601sub7]
ParameterName=mapped object 7
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1601sub8]
ParameterName=mapped object 8
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1602]
ParameterName=RPDO mapping parameter
ObjectType=0x9
;StorageLocation=ROM
SubNumber=0x9

[1602sub0]
ParameterName=Number of mapped objects
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1602sub1]
ParameterName=mapped object 1
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1602sub2]
ParameterName=mapped object 2
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1602sub3]
ParameterName=mapped object 3
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1602sub4]
ParameterName=mapped object 4
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1602sub5]
ParameterName=mapped object 5
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1602sub6]
ParameterName=mapped object 6
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1602sub7]
ParameterName=mapped object 7
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1602sub8]
ParameterName=mapped object 8
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1603]
ParameterName=RPDO mapping parameter
ObjectType=0x9
;StorageLocation=ROM
SubNumber=0x9

[1603sub0]
ParameterName=Number of mapped objects
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1603sub1]
ParameterName=mapped object 1
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1603sub2]
ParameterName=mapped object 2
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1603sub3]
ParameterName=mapped object 3
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1603sub4]
ParameterName=mapped object 4
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1603sub5]
ParameterName=mapped object 5
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1603sub6]
ParameterName=mapped object 6
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1603sub7]
ParameterName=mapped object 7
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1603sub8]
ParameterName=mapped object 8
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1800]
ParameterName=TPDO communication parameter
ObjectType=0x9
;StorageLocation=ROM
SubNumber=0x7

[1800sub0]
ParameterName=max sub-index
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=ro
DefaultValue=6
PDOMapping=0

[1800sub1]
ParameterName=COB-ID used by TPDO
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x180
PDOMapping=0

[1800sub2]
ParameterName=transmission type
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=254
PDOMapping=0

[1800sub3]
ParameterName=inhibit time
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1800sub4]
ParameterName=compatibility entry
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=ro
DefaultValue=0
PDOMapping=0

[1800sub5]
ParameterName=event timer
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1800sub6]
ParameterName=SYNC start value
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1801]
ParameterName=TPDO communication parameter
ObjectType=0x9
;StorageLocation=ROM
SubNumber=0x7

[1801sub0]
ParameterName=max sub-index
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=ro
DefaultValue=6
PDOMapping=0

[1801sub1]
ParameterName=COB-ID used by TPDO
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x280
PDOMapping=0

[1801sub2]
ParameterName=transmission type
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=254
PDOMapping=0

[1801sub3]
ParameterName=inhibit time
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1801sub4]
ParameterName=compatibility entry
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=ro
DefaultValue=0
PDOMapping=0

[1801sub5]
ParameterName=event timer
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1801sub6]
ParameterName=SYNC start value
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1802]
ParameterName=TPDO communication parameter
ObjectType=0x9
;StorageLocation=ROM
SubNumber=0x7

[1802sub0]
ParameterName=max sub-index
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=ro
DefaultValue=6
PDOMapping=0

[1802sub1]
ParameterName=COB-ID used by TPDO
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x380
PDOMapping=0

[1802sub2]
ParameterName=transmission type
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=254
PDOMapping=0

[1802sub3]
ParameterName=inhibit time
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1802sub4]
ParameterName=compatibility entry
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=ro
DefaultValue=0
PDOMapping=0

[1802sub5]
ParameterName=event timer
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1802sub6]
ParameterName=SYNC start value
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1803]
ParameterName=TPDO communication parameter
ObjectType=0x9
;StorageLocation=ROM
SubNumber=0x7

[1803sub0]
ParameterName=max sub-index
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=ro
DefaultValue=6
PDOMapping=0

[1803sub1]
ParameterName=COB-ID used by TPDO
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x480
PDOMapping=0

[1803sub2]
ParameterName=transmission type
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=254
PDOMapping=0

[1803sub3]
ParameterName=inhibit time
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1803sub4]
ParameterName=compatibility entry
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=ro
DefaultValue=0
PDOMapping=0

[1803sub5]
ParameterName=event timer
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1803sub6]
ParameterName=SYNC start value
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A00]
ParameterName=TPDO mapping parameter
ObjectType=0x9
;StorageLocation=ROM
SubNumber=0x9

[1A00sub0]
ParameterName=Number of mapped objects
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A00sub1]
ParameterName=mapped object 1
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A00sub2]
ParameterName=mapped object 2
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A00sub3]
ParameterName=mapped object 3
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A00sub4]
ParameterName=mapped object 4
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A00sub5]
ParameterName=mapped object 5
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A00sub6]
ParameterName=mapped object 6
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A00sub7]
ParameterName=mapped object 7
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A00sub8]
ParameterName=mapped object 8
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A01]
ParameterName=TPDO mapping parameter
ObjectType=0x9
;StorageLocation=ROM
SubNumber=0x9

[1A01sub0]
ParameterName=Number of mapped objects
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A01sub1]
ParameterName=mapped object 1
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A01sub2]
ParameterName=mapped object 2
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A01sub3]
ParameterName=mapped object 3
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A01sub4]
ParameterName=mapped object 4
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A01sub5]
ParameterName=mapped object 5
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A01sub6]
ParameterName=mapped object 6
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A01sub7]
ParameterName=mapped object 7
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A01sub8]
ParameterName=mapped object 8
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A02]
ParameterName=TPDO mapping parameter
ObjectType=0x9
;StorageLocation=ROM
SubNumber=0x9

[1A02sub0]
ParameterName=Number of mapped objects
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A02sub1]
ParameterName=mapped object 1
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A02sub2]
ParameterName=mapped object 2
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A02sub3]
ParameterName=mapped object 3
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A02sub4]
ParameterName=mapped object 4
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A02sub5]
ParameterName=mapped object 5
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A02sub6]
ParameterName=mapped object 6
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A02sub7]
ParameterName=mapped object 7
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A02sub8]
ParameterName=mapped object 8
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A03]
ParameterName=TPDO mapping parameter
ObjectType=0x9
;StorageLocation=ROM
SubNumber=0x9

[1A03sub0]
ParameterName=Number of mapped objects
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A03sub1]
ParameterName=mapped object 1
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A03sub2]
ParameterName=mapped object 2
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A03sub3]
ParameterName=mapped object 3
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A03sub4]
ParameterName=mapped object 4
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A03sub5]
ParameterName=mapped object 5
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A03sub6]
ParameterName=mapped object 6
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A03sub7]
ParameterName=mapped object 7
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1A03sub8]
ParameterName=mapped object 8
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1F50]
ParameterName=Program data
ObjectType=0x8
;StorageLocation=RAM
SubNumber=0x2

[1F50sub0]
ParameterName=max sub-index
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0005
AccessType=ro
DefaultValue=1
PDOMapping=0

[1F50sub1]
ParameterName=
ObjectType=0x7
;StorageLocation=RAM
DataType=0x000F
AccessType=wo
DefaultValue=
PDOMapping=0

[1F51]
ParameterName=Program control
ObjectType=0x8
;StorageLocation=RAM
SubNumber=0x2

[1F51sub0]
ParameterName=max sub-index
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0005
AccessType=ro
DefaultValue=1
PDOMapping=0

[1F51sub1]
ParameterName=
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0005
AccessType=rw
DefaultValue=
PDOMapping=0

[1F56]
ParameterName=Program software identification
ObjectType=0x8
;StorageLocation=RAM
SubNumber=0x2

[1F56sub0]
ParameterName=max sub-index
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0005
AccessType=ro
DefaultValue=1
PDOMapping=0

[1F56sub1]
ParameterName=
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0007
AccessType=ro
DefaultValue=
PDOMapping=0

[1F57]
ParameterName=Flash status identification
ObjectType=0x8
;StorageLocation=RAM
SubNumber=0x2

[1F57sub0]
ParameterName=max sub-index
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0005
AccessType=ro
DefaultValue=1
PDOMapping=0

[1F57sub1]
ParameterName=
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0007
AccessType=ro
DefaultValue=
PDOMapping=0

[1F80]
ParameterName=NMT startup
ObjectType=0x7
;StorageLocation=ROM
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[ManufacturerObjects]
SupportedObjects=3
1=0x2100
2=0x2101
3=0x2102

[2100]
ParameterName=Error status bits
ObjectType=0x7
;StorageLocation=RAM
DataType=0x000A
AccessType=ro
DefaultValue=00000000000000000000
PDOMapping=1

[2101]
ParameterName=Power-on counter
ObjectType=0x7
;StorageLocation=EEPROM
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=1

[2102]
ParameterName=Button press counter
ObjectType=0x7
;StorageLocation=RAM
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

