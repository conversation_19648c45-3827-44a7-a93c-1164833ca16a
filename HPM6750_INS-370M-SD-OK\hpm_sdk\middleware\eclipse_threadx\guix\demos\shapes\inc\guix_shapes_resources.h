/*******************************************************************************/
/*  This file is auto-generated by Azure RTOS GUIX Studio. Do not edit this    */
/*  file by hand. Modifications to this file should only be made by running    */
/*  the Azure RTOS GUIX Studio application and re-generating the application   */
/*  resource file(s). For more information please refer to the Azure RTOS GUIX */
/*  Studio User Guide, or visit our web site at azure.com/rtos                 */
/*                                                                             */
/*  GUIX Studio Revision 6.1.0.0                                               */
/*  Date (dd.mm.yyyy): 21. 8.2023   Time (hh:mm): 16:16                        */
/*******************************************************************************/


#ifndef _GUIX_SHAPES_DISPLAY_1_RESOURCES_H_
#define _GUIX_SHAPES_DISPLAY_1_RESOURCES_H_

#include "gx_api.h"
#include "board.h"

/* Display and theme definitions                                               */

#define DISPLAY_1 0
#define DISPLAY_1_COLOR_FORMAT GX_COLOR_FORMAT_565RGB
#define DISPLAY_1_X_RESOLUTION BOARD_LCD_WIDTH
#define DISPLAY_1_Y_RESOLUTION BOARD_LCD_HEIGHT
#define DISPLAY_1_DEFAULT_THEME 0
#define DISPLAY_1_THEME_TABLE_SIZE 1

/* Language definitions                                                        */

#define LANGUAGE_ENGLISH 0
#define DISPLAY_1_LANGUAGE_TABLE_SIZE 1

/* Color ID definitions                                                        */

#define GX_COLOR_ID_INDIAN_RED 29
#define GX_COLOR_ID_YELLOW 30
#define GX_COLOR_ID_PURPLE 31
#define GX_COLOR_ID_GRAY 32
#define GX_COLOR_ID_WHITE 33
#define GX_COLOR_ID_BROWN 34
#define GX_COLOR_ID_DARK_BROWN 35
#define GX_COLOR_ID_GREEN 36
#define GX_COLOR_ID_BLACK 37
#define GX_COLOR_ID_DARK_GRAY 38
#define GX_COLOR_ID_LIGHT_BROWN 39
#define GX_COLOR_ID_LIGHT_GRAY 40
#define GX_COLOR_ID_LIGHT_TEAL 41
#define GX_COLOR_ID_DARK_TEAL 42
#define GX_COLOR_ID_TEAL 43
#define GX_COLOR_ID_SKY_BLUE 44
#define GX_COLOR_ID_RED 45
#define GX_COLOR_ID_BLUE 46
#define DISPLAY_1_COLOR_TABLE_SIZE 47

/* Font ID definitions                                                         */

#define GX_FONT_ID_TITLE 4
#define GX_FONT_ID_MID_NUMBER 5
#define GX_FONT_ID_MONO_FONT 6
#define GX_FONT_ID_SMALL_BOLD 7
#define GX_FONT_ID_SCREEN_LABEL 8
#define DISPLAY_1_FONT_TABLE_SIZE 9

/* Pixelmap ID definitions                                                     */

#define GX_PIXELMAP_ID_FISH 5
#define GX_PIXELMAP_ID_GX_RADIO_ICON_DOT 6
#define GX_PIXELMAP_ID_GX_SLIDER_THIN_ACTIVE_HORIZONTAL 7
#define GX_PIXELMAP_ID_GX_SLIDER_THIN_HORIZONTAL 8
#define GX_PIXELMAP_ID_GX_SLIDER_THIN_NUB_HORIZONTAL 9
#define GX_PIXELMAP_ID_MS_AZURE_LOGO_SMALL 10
#define GX_PIXELMAP_ID_VAL_BACKGROUND 11
#define DISPLAY_1_PIXELMAP_TABLE_SIZE 12

/* String Ids                                                                  */

#define GX_STRING_ID_STRING_1 1
#define GX_STRING_ID_STRING_2 2
#define GX_STRING_ID_STRING_3 3
#define GX_STRING_ID_STRING_4 4
#define GX_STRING_ID_STRING_5 5
#define GX_STRING_ID_STRING_6 6
#define GX_STRING_ID_STRING_7 7
#define GX_STRING_ID_STRING_8 8
#define GX_STRING_ID_STRING_9 9
#define GX_STRING_ID_STRING_10 10
#define GX_STRING_ID_STRING_11 11
#define GX_STRING_ID_STRING_12 12
#define GX_STRING_ID_STRING_13 13
#define GX_STRING_ID_STRING_14 14
#define GX_STRING_ID_STRING_15 15
#define GX_STRING_ID_STRING_16 16
#define GX_STRING_ID_STRING_17 17
#define GX_STRING_ID_STRING_18 18
#define GX_STRING_ID_STRING_19 19
#define GX_STRING_ID_STRING_20 20
#define GX_STRING_ID_STRING_21 21
#define GX_STRING_ID_STRING_22 22
#define GX_STRING_ID_STRING_23 23
#define GX_STRING_ID_STRING_24 24
#define GX_STRING_ID_STRING_25 25
#define GX_STRING_ID_STRING_26 26
#define GX_STRING_ID_STRING_27 27
#define GX_STRING_ID_STRING_28 28
#define GX_STRING_ID_STRING_29 29
#define GX_STRING_ID_STRING_30 30
#define GX_STRING_ID_STRING_31 31
#define GX_STRING_ID_STRING_32 32
#define GX_STRING_ID_STRING_33 33
#define GX_STRING_ID_STRING_34 34
#define GX_STRING_ID_STRING_35 35
#define GX_STRING_ID_STRING_36 36
#define GX_STRING_ID_STRING_37 37
#define GX_STRING_ID_STRING_38 38
#define GX_STRING_ID_STRING_39 39
#define GX_STRING_ID_STRING_40 40
#define GX_STRING_ID_STRING_41 41
#define GX_STRING_ID_STRING_42 42
#define GX_STRING_ID_STRING_43 43
#define GX_STRING_ID_STRING_44 44
#define GX_STRING_ID_STRING_45 45
#define GX_STRING_ID_STRING_46 46
#define GX_STRING_ID_STRING_47 47
#define GX_STRING_ID_STRING_48 48
#define GX_STRING_ID_STRING_49 49
#define GX_STRING_ID_STRING_50 50
#define GX_STRING_ID_STRING_51 51
#define GX_STRING_ID_STRING_52 52
#define GX_STRING_ID_STRING_53 53
#define GX_STRING_ID_STRING_54 54
#define GX_STRING_ID_STRING_55 55
#define GX_STRING_ID_STRING_56 56
#define GX_STRING_ID_STRING_57 57
#define GX_STRING_ID_STRING_58 58
#define GX_STRING_ID_STRING_59 59
#define GX_STRING_ID_STRING_60 60
#define GX_STRING_ID_STRING_61 61
#define GX_STRING_ID_STRING_62 62
#define GX_STRING_ID_STRING_63 63
#define GX_STRING_ID_STRING_64 64
#define GX_STRING_ID_STRING_65 65
#define GX_STRING_ID_STRING_66 66
#define GX_STRING_ID_STRING_67 67
#define GX_STRING_ID_STRING_68 68
#define GX_STRING_ID_STRING_69 69
#define GX_STRING_ID_STRING_70 70
#define GX_STRING_ID_STRING_71 71
#define GX_STRING_ID_STRING_72 72
#define GX_STRING_ID_STRING_73 73
#define GX_STRING_ID_STRING_74 74
#define GX_STRING_ID_STRING_75 75
#define GX_STRING_ID_STRING_76 76
#define GX_STRING_ID_STRING_77 77
#define GX_STRING_ID_STRING_78 78
#define GX_STRING_ID_STRING_79 79
#define GX_STRING_ID_STRING_80 80
#define GX_STRING_ID_STRING_81 81
#define GX_STRING_ID_STRING_82 82
#define GX_STRING_ID_STRING_83 83
#define GX_STRING_ID_STRING_84 84
#define GX_STRING_ID_HELLO_WORLD 85
#define GX_STRING_ID_FONT_4BPP 86
#define GX_STRING_ID_FONT_1BPP 87
#define DISPLAY_1_STRING_TABLE_SIZE 88

#endif                                       /* sentry                         */
