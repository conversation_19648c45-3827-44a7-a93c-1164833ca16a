#include "bsp_uart.h"
#include "fpgad.h"

void bsp_systick_init(uint32_t com)
{

	rcu_periph_clock_enable( RCU_GPIOA);

	/* enable USART clock */
	rcu_periph_clock_enable(RCU_USART0);

	/* connect port to USARTx_Tx */
	gpio_af_set(GPIOA, GPIO_AF_7, GPIO_PIN_9);

	/* connect port to USARTx_Rx */
	gpio_af_set(GPIOA, GPIO_AF_7, GPIO_PIN_10);

	/* configure USART Tx as alternate function push-pull */
	gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_9);
	gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_9);

	/* configure USART Rx as alternate function push-pull */
	gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_10);
	gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_10);

	/* USART configure */
	usart_deinit(com);
	usart_baudrate_set(com,115200U);
	usart_receive_config(USART0, USART_RECEIVE_ENABLE);
	usart_transmit_config(USART0, USART_TRANSMIT_ENABLE);
	usart_enable(USART0);
}

void bsp_systick_init01(uint32_t com)
{

	rcu_periph_clock_enable( RCU_GPIOC);
	rcu_periph_clock_enable( RCU_GPIOD);

	/* enable USART clock */
	rcu_periph_clock_enable(RCU_UART4);

	/* connect port to USARTx_Tx */
	gpio_af_set(GPIOC, GPIO_AF_8, GPIO_PIN_12);

	/* connect port to USARTx_Rx */
	gpio_af_set(GPIOD, GPIO_AF_8, GPIO_PIN_2);

	/* configure USART Tx as alternate function push-pull */
	gpio_mode_set(GPIOC, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_12);
	gpio_output_options_set(GPIOC, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_12);

	/* configure USART Rx as alternate function push-pull */
	gpio_mode_set(GPIOD, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_2);
	gpio_output_options_set(GPIOD, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_2);

	/* USART configure */
	usart_deinit(UART4);
	usart_baudrate_set(UART4,115200U);
	usart_receive_config(UART4, USART_RECEIVE_ENABLE);
	usart_transmit_config(UART4, USART_TRANSMIT_ENABLE);

	/* enable UART4 receive interrupt */
	usart_interrupt_enable(UART4, USART_INT_RBNE);

	/* enable UART4 interrupt */
	nvic_irq_enable(UART4_IRQn, 0, 0);

	usart_enable(UART4);
}



/*!
    \brief      this function handles USART0 exception
    \param[in]  none
    \param[out] none
    \retval     none
*/

void USART4_IRQHandler(void)
{
    if((RESET != usart_interrupt_flag_get(UART4, USART_INT_FLAG_RBNE)) &&
            (RESET != usart_flag_get(UART4, USART_FLAG_RBNE))) {
        /* receive data */
        // 修复：条件应该是 < 而不是 >=，并且要更新 grxlen
        if (grxlen < U4RX_MAXCOUNT) {
            grxbuffer[(grxst + grxlen) % U4RX_MAXCOUNT] = usart_data_receive(UART4);
            grxlen++;  // 关键修复：增加接收数据计数器
        } else {
            // 缓冲区满，丢弃数据
            usart_data_receive(UART4);  // 读取数据以清除中断标志
        }
    }
    if((RESET != usart_flag_get(UART4, USART_FLAG_TBE)) &&
            (RESET != usart_interrupt_flag_get(UART4, USART_INT_FLAG_TBE))) {
				
		//gtxcount++;
        /* transmit data */
//        usart_data_transmit(USART0, txbuffer[txcount++]);
//        if(txcount == tx_size) {
//            //usart_interrupt_disable(USART0, USART_INT_TBE);
//        }
    }
}
