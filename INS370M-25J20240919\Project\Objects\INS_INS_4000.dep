Dependencies for Project 'INS', Target 'INS_4000': (DO NOT MODIFY !)
CompilerVersion: 5060960::V5.06 update 7 (build 960)::.\ARMCC
F (..\Source\src\gd32f4xx_it.c)(0x66949A7A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_it.o --omf_browse .\objects\gd32f4xx_it.crf --depend .\objects\gd32f4xx_it.d)
I (..\Source\inc\gd32f4xx_it.h)(0x5CCFA4FE)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Source\inc\main.h)(0x6489701A)
I (..\Source\inc\systick.h)(0x624D71D8)
I (..\bsp\inc\bsp_fmc.h)(0x62B91682)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Source\inc\INS_Data.h)(0x6552E6AA)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x653B9376)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FC)
I (..\Source\inc\tlhtype.h)(0x650CEC14)
I (..\Source\inc\can_data.h)(0x64780368)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x658EB91C)
I (..\Source\src\INS912AlgorithmEntry.h)(0x65811410)
I (..\bsp\inc\bsp_tim.h)(0x6500098E)
I (..\bsp\inc\bsp_rtc.h)(0x62A81D02)
I (..\Source\inc\Time_unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\bsp\inc\Logger.h)(0x62A197B0)
I (..\bsp\inc\CH378_HAL.h)(0x62BE4516)
I (..\bsp\inc\CH378INC.H)(0x62A31FD8)
I (..\bsp\inc\FILE_SYS.h)(0x62C2910A)
I (..\bsp\inc\bsp_gpio.h)(0x6388787C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdarg.h)(0x60252376)
I (..\Source\inc\fpgad.h)(0x6846ABB0)
I (..\Source\src\appdefine.h)(0x65766CD4)
I (..\Source\inc\deviceconfig.h)(0x66EBBD3E)
F (..\Source\src\main.c)(0x68469F83)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\main.o --omf_browse .\objects\main.crf --depend .\objects\main.d)
I (..\Source\inc\appmain.h)(0x668B9CFE)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Source\inc\systick.h)(0x624D71D8)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Source\inc\main.h)(0x6489701A)
I (..\bsp\inc\bsp_gpio.h)(0x6388787C)
I (..\bsp\inc\bsp_flash.h)(0x68469E16)
I (..\Source\inc\INS_Data.h)(0x6552E6AA)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x653B9376)
I (..\Common\inc\data_convert.h)(0x62A843FC)
I (..\Source\inc\tlhtype.h)(0x650CEC14)
I (..\Source\inc\can_data.h)(0x64780368)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x658EB91C)
I (..\Source\src\INS912AlgorithmEntry.h)(0x65811410)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\bsp\inc\bsp_rtc.h)(0x62A81D02)
I (..\Source\inc\Time_unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\bsp\inc\bsp_can.h)(0x653B9376)
I (..\bsp\inc\bsp_fwdgt.h)(0x6254D09C)
I (..\bsp\inc\CH395SPI.H)(0x62BAD73A)
I (..\bsp\inc\CH395INC.H)(0x60C2CB7E)
I (..\bsp\inc\CH395CMD.H)(0x62B95964)
I (..\Source\inc\TCPServer.h)(0x654CC6F8)
I (..\bsp\inc\bsp_fmc.h)(0x62B91682)
I (..\bsp\inc\bsp_exti.h)(0x64ACAAA6)
I (..\bsp\inc\bmp280.h)(0x62A311AA)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\bsp\inc\bmp2.h)(0x60B09D02)
I (..\bsp\inc\bmp2_defs.h)(0x60B09D02)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\bsp\inc\common.h)(0x60B09D02)
I (..\bsp\inc\CH378_HAL.h)(0x62BE4516)
I (..\bsp\inc\CH378INC.H)(0x62A31FD8)
I (..\bsp\inc\logger.h)(0x62A197B0)
I (..\bsp\inc\FILE_SYS.h)(0x62C2910A)
I (..\bsp\inc\bsp_tim.h)(0x6500098E)
I (..\Source\inc\fpgad.h)(0x6846ABB0)
I (..\Source\src\appdefine.h)(0x65766CD4)
I (..\Protocol\serial.h)(0x6576E71A)
I (..\Protocol\insdef.h)(0x63B3A700)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\Protocol\computerFrameParse.h)(0x6846AFE3)
I (..\Source\src\gdtypedefine.h)(0x66E23E06)
I (..\Protocol\frame_analysis.h)(0x657BF0F6)
I (..\Source\inc\deviceconfig.h)(0x66EBBD3E)
I (..\Source\src\datado.h)(0x66A70052)
I (..\INAV\ins.h)(0x66535980)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\ctype.h)(0x60252376)
I (..\INAV\DATASTRUCT.h)(0x6660751A)
I (..\INAV\CONST.h)(0x66987428)
I (..\INAV\TYPEDEFINE.h)(0x665374EE)
I (..\INAV\FUNCTION.h)(0x66B5255C)
I (..\INAV\GLOBALDATA.h)(0x665375FC)
I (..\Protocol\insTestingEntry.h)(0x68469E00)
I (..\Source\inc\INS_App_Init.h)(0x6846867E)
I (..\Protocol\SetParaBao.h)(0x684685EF)
I (..\bsp\inc\bsp_sd_fatfs.h)(0x684685A1)
F (..\Source\src\systick.c)(0x62AAE42C)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\systick.o --omf_browse .\objects\systick.crf --depend .\objects\systick.d)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Source\inc\systick.h)(0x624D71D8)
F (..\Source\src\can_data.c)(0x64780368)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\can_data.o --omf_browse .\objects\can_data.crf --depend .\objects\can_data.d)
I (..\Source\inc\can_data.h)(0x64780368)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Common\inc\data_convert.h)(0x62A843FC)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
F (..\Source\src\imu_data.c)(0x6256891A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\imu_data.o --omf_browse .\objects\imu_data.crf --depend .\objects\imu_data.d)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Common\inc\data_convert.h)(0x62A843FC)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
F (..\Source\src\INS_Data.c)(0x6684A222)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\ins_data.o --omf_browse .\objects\ins_data.crf --depend .\objects\ins_data.d)
I (..\Source\inc\INS_Data.h)(0x6552E6AA)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x653B9376)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FC)
I (..\Source\inc\tlhtype.h)(0x650CEC14)
I (..\Source\inc\can_data.h)(0x64780368)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x658EB91C)
I (..\Source\src\INS912AlgorithmEntry.h)(0x65811410)
I (..\bsp\inc\bsp_flash.h)(0x68469E16)
I (..\Source\inc\fpgad.h)(0x6846ABB0)
I (..\Source\src\appdefine.h)(0x65766CD4)
F (..\Source\src\INS_Sys.c)(0x6497EC52)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\ins_sys.o --omf_browse .\objects\ins_sys.crf --depend .\objects\ins_sys.d)
I (..\Source\inc\INS_Sys.h)(0x658EB91C)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
F (..\Source\src\Time_Unify.c)(0x62C3A0A2)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\time_unify.o --omf_browse .\objects\time_unify.crf --depend .\objects\time_unify.d)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\Source\inc\Time_Unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
F (..\Source\src\fpgad.c)(0x66A70110)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\fpgad.o --omf_browse .\objects\fpgad.crf --depend .\objects\fpgad.d)
I (..\Source\inc\fpgad.h)(0x6846ABB0)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Source\src\appdefine.h)(0x65766CD4)
I (..\Source\inc\deviceconfig.h)(0x66EBBD3E)
I (..\INAV\ins.h)(0x66535980)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\ctype.h)(0x60252376)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (..\Source\inc\appmain.h)(0x668B9CFE)
I (..\Source\inc\systick.h)(0x624D71D8)
I (..\Source\inc\main.h)(0x6489701A)
I (..\bsp\inc\bsp_gpio.h)(0x6388787C)
I (..\bsp\inc\bsp_flash.h)(0x68469E16)
I (..\Source\inc\INS_Data.h)(0x6552E6AA)
I (..\Source\inc\gnss.h)(0x653B9376)
I (..\Common\inc\data_convert.h)(0x62A843FC)
I (..\Source\inc\tlhtype.h)(0x650CEC14)
I (..\Source\inc\can_data.h)(0x64780368)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x658EB91C)
I (..\Source\src\INS912AlgorithmEntry.h)(0x65811410)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\bsp\inc\bsp_rtc.h)(0x62A81D02)
I (..\Source\inc\Time_unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\bsp\inc\bsp_can.h)(0x653B9376)
I (..\bsp\inc\bsp_fwdgt.h)(0x6254D09C)
I (..\bsp\inc\CH395SPI.H)(0x62BAD73A)
I (..\bsp\inc\CH395INC.H)(0x60C2CB7E)
I (..\bsp\inc\CH395CMD.H)(0x62B95964)
I (..\Source\inc\TCPServer.h)(0x654CC6F8)
I (..\bsp\inc\bsp_fmc.h)(0x62B91682)
I (..\bsp\inc\bsp_exti.h)(0x64ACAAA6)
I (..\bsp\inc\bmp280.h)(0x62A311AA)
I (..\bsp\inc\bmp2.h)(0x60B09D02)
I (..\bsp\inc\bmp2_defs.h)(0x60B09D02)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\bsp\inc\common.h)(0x60B09D02)
I (..\bsp\inc\CH378_HAL.h)(0x62BE4516)
I (..\bsp\inc\CH378INC.H)(0x62A31FD8)
I (..\bsp\inc\logger.h)(0x62A197B0)
I (..\bsp\inc\FILE_SYS.h)(0x62C2910A)
I (..\bsp\inc\bsp_tim.h)(0x6500098E)
I (..\Protocol\serial.h)(0x6576E71A)
I (..\Protocol\insdef.h)(0x63B3A700)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\Protocol\computerFrameParse.h)(0x6846AFE3)
I (..\Source\src\gdtypedefine.h)(0x66E23E06)
I (..\Protocol\frame_analysis.h)(0x657BF0F6)
I (..\Source\src\datado.h)(0x66A70052)
F (..\Source\src\clock.c)(0x653B9354)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\clock.o --omf_browse .\objects\clock.crf --depend .\objects\clock.d)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\Source\src\clock.h)(0x653B95A6)
I (..\Protocol\insdef.h)(0x63B3A700)
F (..\Source\src\Data_shift.c)(0x653CCC52)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\data_shift.o --omf_browse .\objects\data_shift.crf --depend .\objects\data_shift.d)
I (..\Source\inc\data_shift.h)(0x628B00B6)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Source\inc\ins_data.h)(0x6552E6AA)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x653B9376)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FC)
I (..\Source\inc\tlhtype.h)(0x650CEC14)
I (..\Source\inc\can_data.h)(0x64780368)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x658EB91C)
I (..\Source\src\INS912AlgorithmEntry.h)(0x65811410)
F (..\Source\src\gdwatch.c)(0x657BF126)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gdwatch.o --omf_browse .\objects\gdwatch.crf --depend .\objects\gdwatch.d)
I (..\Source\src\gdtypedefine.h)(0x66E23E06)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Source\inc\fpgad.h)(0x6846ABB0)
I (..\Source\src\appdefine.h)(0x65766CD4)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\Protocol\frame_analysis.h)(0x657BF0F6)
I (..\Source\inc\INS_Data.h)(0x6552E6AA)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x653B9376)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FC)
I (..\Source\inc\tlhtype.h)(0x650CEC14)
I (..\Source\inc\can_data.h)(0x64780368)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x658EB91C)
I (..\Source\src\INS912AlgorithmEntry.h)(0x65811410)
I (..\Protocol\insdef.h)(0x63B3A700)
F (..\Source\src\INS_Output.c)(0x668CFE98)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\ins_output.o --omf_browse .\objects\ins_output.crf --depend .\objects\ins_output.d)
I (..\Source\inc\appmain.h)(0x668B9CFE)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Source\inc\systick.h)(0x624D71D8)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Source\inc\main.h)(0x6489701A)
I (..\bsp\inc\bsp_gpio.h)(0x6388787C)
I (..\bsp\inc\bsp_flash.h)(0x68469E16)
I (..\Source\inc\INS_Data.h)(0x6552E6AA)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x653B9376)
I (..\Common\inc\data_convert.h)(0x62A843FC)
I (..\Source\inc\tlhtype.h)(0x650CEC14)
I (..\Source\inc\can_data.h)(0x64780368)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x658EB91C)
I (..\Source\src\INS912AlgorithmEntry.h)(0x65811410)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\bsp\inc\bsp_rtc.h)(0x62A81D02)
I (..\Source\inc\Time_unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\bsp\inc\bsp_can.h)(0x653B9376)
I (..\bsp\inc\bsp_fwdgt.h)(0x6254D09C)
I (..\bsp\inc\CH395SPI.H)(0x62BAD73A)
I (..\bsp\inc\CH395INC.H)(0x60C2CB7E)
I (..\bsp\inc\CH395CMD.H)(0x62B95964)
I (..\Source\inc\TCPServer.h)(0x654CC6F8)
I (..\bsp\inc\bsp_fmc.h)(0x62B91682)
I (..\bsp\inc\bsp_exti.h)(0x64ACAAA6)
I (..\bsp\inc\bmp280.h)(0x62A311AA)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\bsp\inc\bmp2.h)(0x60B09D02)
I (..\bsp\inc\bmp2_defs.h)(0x60B09D02)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\bsp\inc\common.h)(0x60B09D02)
I (..\bsp\inc\CH378_HAL.h)(0x62BE4516)
I (..\bsp\inc\CH378INC.H)(0x62A31FD8)
I (..\bsp\inc\logger.h)(0x62A197B0)
I (..\bsp\inc\FILE_SYS.h)(0x62C2910A)
I (..\bsp\inc\bsp_tim.h)(0x6500098E)
I (..\Source\inc\fpgad.h)(0x6846ABB0)
I (..\Source\src\appdefine.h)(0x65766CD4)
I (..\Protocol\serial.h)(0x6576E71A)
I (..\Protocol\insdef.h)(0x63B3A700)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\Protocol\computerFrameParse.h)(0x6846AFE3)
I (..\Source\src\gdtypedefine.h)(0x66E23E06)
I (..\Protocol\frame_analysis.h)(0x657BF0F6)
I (..\Source\inc\deviceconfig.h)(0x66EBBD3E)
I (..\Source\src\datado.h)(0x66A70052)
I (..\Source\inc\INS_Output.h)(0x653B963A)
F (..\Source\src\INS_Init.c)(0x6846ADE2)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\ins_init.o --omf_browse .\objects\ins_init.crf --depend .\objects\ins_init.d)
I (..\Source\inc\appmain.h)(0x668B9CFE)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Source\inc\systick.h)(0x624D71D8)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Source\inc\main.h)(0x6489701A)
I (..\bsp\inc\bsp_gpio.h)(0x6388787C)
I (..\bsp\inc\bsp_flash.h)(0x68469E16)
I (..\Source\inc\INS_Data.h)(0x6552E6AA)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x653B9376)
I (..\Common\inc\data_convert.h)(0x62A843FC)
I (..\Source\inc\tlhtype.h)(0x650CEC14)
I (..\Source\inc\can_data.h)(0x64780368)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x658EB91C)
I (..\Source\src\INS912AlgorithmEntry.h)(0x65811410)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\bsp\inc\bsp_rtc.h)(0x62A81D02)
I (..\Source\inc\Time_unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\bsp\inc\bsp_can.h)(0x653B9376)
I (..\bsp\inc\bsp_fwdgt.h)(0x6254D09C)
I (..\bsp\inc\CH395SPI.H)(0x62BAD73A)
I (..\bsp\inc\CH395INC.H)(0x60C2CB7E)
I (..\bsp\inc\CH395CMD.H)(0x62B95964)
I (..\Source\inc\TCPServer.h)(0x654CC6F8)
I (..\bsp\inc\bsp_fmc.h)(0x62B91682)
I (..\bsp\inc\bsp_exti.h)(0x64ACAAA6)
I (..\bsp\inc\bmp280.h)(0x62A311AA)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\bsp\inc\bmp2.h)(0x60B09D02)
I (..\bsp\inc\bmp2_defs.h)(0x60B09D02)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\bsp\inc\common.h)(0x60B09D02)
I (..\bsp\inc\CH378_HAL.h)(0x62BE4516)
I (..\bsp\inc\CH378INC.H)(0x62A31FD8)
I (..\bsp\inc\logger.h)(0x62A197B0)
I (..\bsp\inc\FILE_SYS.h)(0x62C2910A)
I (..\bsp\inc\bsp_tim.h)(0x6500098E)
I (..\Source\inc\fpgad.h)(0x6846ABB0)
I (..\Source\src\appdefine.h)(0x65766CD4)
I (..\Protocol\serial.h)(0x6576E71A)
I (..\Protocol\insdef.h)(0x63B3A700)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\Protocol\computerFrameParse.h)(0x6846AFE3)
I (..\Source\src\gdtypedefine.h)(0x66E23E06)
I (..\Protocol\frame_analysis.h)(0x657BF0F6)
I (..\Source\inc\deviceconfig.h)(0x66EBBD3E)
I (..\Source\src\datado.h)(0x66A70052)
I (..\Source\inc\api_ch392.h)(0x6549A820)
I (..\INAV\ins.h)(0x66535980)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\ctype.h)(0x60252376)
F (..\Source\src\api_ch392.c)(0x655173E6)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\api_ch392.o --omf_browse .\objects\api_ch392.crf --depend .\objects\api_ch392.d)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\bsp\inc\drv_spi.h)(0x6549EAE0)
I (..\Protocol\insdef.h)(0x63B3A700)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\bsp\inc\drv_timer.h)(0x63159104)
I (..\bsp\inc\HAL.H)(0x62EB59C4)
I (..\bsp\inc\CH378INC.H)(0x62A31FD8)
I (..\bsp\inc\pin_numbers_def.h)(0x654D80B8)
I (..\Source\inc\ch392.h)(0x5E4CDEE0)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Source\inc\CH392INC.H)(0x62F224CA)
I (..\Source\inc\CH392CMD.H)(0x654CC444)
I (..\Source\inc\api_ch392.h)(0x6549A820)
I (..\Source\inc\systick.h)(0x624D71D8)
I (..\Protocol\config.h)(0x64AB57C0)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\Source\src\CH392CMD.C)(0x654CC444)
I (..\bsp\inc\drv_gpio.h)(0x62CD2FD0)
F (..\Source\inc\deviceconfig.h)(0x66EBBD3E)()
F (..\Source\src\datado.c)(0x66A7018E)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\datado.o --omf_browse .\objects\datado.crf --depend .\objects\datado.d)
I (..\Source\src\INS912ALGORITHMENTRY.h)(0x65811410)
I (..\Source\inc\deviceconfig.h)(0x66EBBD3E)
I (..\Source\inc\appmain.h)(0x668B9CFE)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Source\inc\systick.h)(0x624D71D8)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Source\inc\main.h)(0x6489701A)
I (..\bsp\inc\bsp_gpio.h)(0x6388787C)
I (..\bsp\inc\bsp_flash.h)(0x68469E16)
I (..\Source\inc\INS_Data.h)(0x6552E6AA)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x653B9376)
I (..\Common\inc\data_convert.h)(0x62A843FC)
I (..\Source\inc\tlhtype.h)(0x650CEC14)
I (..\Source\inc\can_data.h)(0x64780368)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x658EB91C)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\bsp\inc\bsp_rtc.h)(0x62A81D02)
I (..\Source\inc\Time_unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\bsp\inc\bsp_can.h)(0x653B9376)
I (..\bsp\inc\bsp_fwdgt.h)(0x6254D09C)
I (..\bsp\inc\CH395SPI.H)(0x62BAD73A)
I (..\bsp\inc\CH395INC.H)(0x60C2CB7E)
I (..\bsp\inc\CH395CMD.H)(0x62B95964)
I (..\Source\inc\TCPServer.h)(0x654CC6F8)
I (..\bsp\inc\bsp_fmc.h)(0x62B91682)
I (..\bsp\inc\bsp_exti.h)(0x64ACAAA6)
I (..\bsp\inc\bmp280.h)(0x62A311AA)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\bsp\inc\bmp2.h)(0x60B09D02)
I (..\bsp\inc\bmp2_defs.h)(0x60B09D02)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\bsp\inc\common.h)(0x60B09D02)
I (..\bsp\inc\CH378_HAL.h)(0x62BE4516)
I (..\bsp\inc\CH378INC.H)(0x62A31FD8)
I (..\bsp\inc\logger.h)(0x62A197B0)
I (..\bsp\inc\FILE_SYS.h)(0x62C2910A)
I (..\bsp\inc\bsp_tim.h)(0x6500098E)
I (..\Source\inc\fpgad.h)(0x6846ABB0)
I (..\Source\src\appdefine.h)(0x65766CD4)
I (..\Protocol\serial.h)(0x6576E71A)
I (..\Protocol\insdef.h)(0x63B3A700)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\Protocol\computerFrameParse.h)(0x6846AFE3)
I (..\Source\src\gdtypedefine.h)(0x66E23E06)
I (..\Protocol\frame_analysis.h)(0x657BF0F6)
I (..\Source\src\datado.h)(0x66A70052)
F (..\Source\src\datado.h)(0x66A70052)()
F (..\bsp\src\bmp2.c)(0x62A3063A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\bmp2.o --omf_browse .\objects\bmp2.crf --depend .\objects\bmp2.d)
I (..\bsp\inc\bmp2.h)(0x60B09D02)
I (..\bsp\inc\bmp2_defs.h)(0x60B09D02)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
F (..\bsp\src\bmp280.c)(0x653CCE84)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\bmp280.o --omf_browse .\objects\bmp280.crf --depend .\objects\bmp280.d)
I (..\bsp\inc\bmp280.h)(0x62A311AA)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\bsp\inc\bmp2.h)(0x60B09D02)
I (..\bsp\inc\bmp2_defs.h)(0x60B09D02)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\bsp\inc\common.h)(0x60B09D02)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
F (..\bsp\src\bsp_adc.c)(0x628B6228)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\bsp_adc.o --omf_browse .\objects\bsp_adc.crf --depend .\objects\bsp_adc.d)
I (..\bsp\inc\bsp_adc.h)(0x628B4F68)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Source\inc\systick.h)(0x624D71D8)
F (..\bsp\src\bsp_can.c)(0x6684AE6E)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\bsp_can.o --omf_browse .\objects\bsp_can.crf --depend .\objects\bsp_can.d)
I (..\bsp\inc\bsp_can.h)(0x653B9376)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\Source\inc\INS_Data.h)(0x6552E6AA)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x653B9376)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FC)
I (..\Source\inc\tlhtype.h)(0x650CEC14)
I (..\Source\inc\can_data.h)(0x64780368)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x658EB91C)
I (..\Source\src\INS912AlgorithmEntry.h)(0x65811410)
I (..\bsp\inc\bsp_gpio.h)(0x6388787C)
I (..\bsp\inc\bsp_tim.h)(0x6500098E)
I (..\Source\inc\Time_Unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\Source\inc\fpgad.h)(0x6846ABB0)
I (..\Source\src\appdefine.h)(0x65766CD4)
F (..\bsp\src\bsp_exti.c)(0x65376CB0)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\bsp_exti.o --omf_browse .\objects\bsp_exti.crf --depend .\objects\bsp_exti.d)
I (..\bsp\inc\bsp_exti.h)(0x64ACAAA6)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\bsp\src\bsp_flash.c)(0x6846857A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\bsp_flash.o --omf_browse .\objects\bsp_flash.crf --depend .\objects\bsp_flash.d)
I (..\bsp\inc\bsp_flash.h)(0x68469E16)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\Source\inc\main.h)(0x6489701A)
F (..\bsp\src\bsp_fmc.c)(0x6694993C)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\bsp_fmc.o --omf_browse .\objects\bsp_fmc.crf --depend .\objects\bsp_fmc.d)
I (..\bsp\inc\bsp_fmc.h)(0x62B91682)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Source\inc\systick.h)(0x624D71D8)
I (..\Source\inc\fpgad.h)(0x6846ABB0)
I (..\Source\src\appdefine.h)(0x65766CD4)
F (..\bsp\src\bsp_fwdgt.c)(0x62CD2532)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\bsp_fwdgt.o --omf_browse .\objects\bsp_fwdgt.crf --depend .\objects\bsp_fwdgt.d)
I (..\bsp\inc\bsp_fwdgt.h)(0x6254D09C)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\bsp\src\bsp_gpio.c)(0x647D474C)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\bsp_gpio.o --omf_browse .\objects\bsp_gpio.crf --depend .\objects\bsp_gpio.d)
I (..\bsp\inc\bsp_gpio.h)(0x6388787C)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\bsp\src\bsp_rtc.c)(0x62A9516C)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\bsp_rtc.o --omf_browse .\objects\bsp_rtc.crf --depend .\objects\bsp_rtc.d)
I (..\bsp\inc\bsp_rtc.h)(0x62A81D02)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\Source\inc\Time_unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
F (..\bsp\src\bsp_sys.c)(0x6264C948)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\bsp_sys.o --omf_browse .\objects\bsp_sys.crf --depend .\objects\bsp_sys.d)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\bsp\src\bsp_tim.c)(0x65000B52)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\bsp_tim.o --omf_browse .\objects\bsp_tim.crf --depend .\objects\bsp_tim.d)
I (..\bsp\inc\bsp_tim.h)(0x6500098E)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\bsp\src\bsp_uart.c)(0x64891A98)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\bsp_uart.o --omf_browse .\objects\bsp_uart.crf --depend .\objects\bsp_uart.d)
I (..\bsp\inc\bsp_uart.h)(0x6245473A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Source\inc\fpgad.h)(0x6846ABB0)
I (..\Source\src\appdefine.h)(0x65766CD4)
F (..\bsp\src\CH378_HAL.C)(0x62958CE2)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\ch378_hal.o --omf_browse .\objects\ch378_hal.crf --depend .\objects\ch378_hal.d)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\bsp\inc\TYPE.H)(0x628CAEC2)
I (..\bsp\inc\CH378_HAL.H)(0x62BE4516)
I (..\bsp\inc\CH378INC.H)(0x62A31FD8)
I (..\Source\inc\systick.h)(0x624D71D8)
F (..\bsp\src\CH395CMD.C)(0x62CCD952)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\ch395cmd.o --omf_browse .\objects\ch395cmd.crf --depend .\objects\ch395cmd.d)
I (..\bsp\inc\CH395INC.H)(0x60C2CB7E)
I (..\bsp\inc\ch395cmd.h)(0x62B95964)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\bsp\inc\CH395SPI.H)(0x62BAD73A)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Source\inc\systick.h)(0x624D71D8)
F (..\bsp\src\CH395SPI.C)(0x62CD237A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\ch395spi.o --omf_browse .\objects\ch395spi.crf --depend .\objects\ch395spi.d)
I (..\bsp\inc\CH395SPI.H)(0x62BAD73A)
I (..\bsp\inc\CH395INC.H)(0x60C2CB7E)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Source\inc\systick.h)(0x624D71D8)
F (..\bsp\src\common.c)(0x62C52124)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\common.o --omf_browse .\objects\common.crf --depend .\objects\common.d)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Source\inc\systick.h)(0x624D71D8)
I (..\bsp\inc\bmp2_defs.h)(0x60B09D02)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\bsp\inc\bsp_soft_i2c_master.h)(0x624D41E2)
I (..\bsp\inc\soft_i2c.h)(0x624CFB4C)
F (..\bsp\src\FILE_SYS.C)(0x62BED492)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\file_sys.o --omf_browse .\objects\file_sys.crf --depend .\objects\file_sys.d)
I (..\bsp\inc\FILE_SYS.H)(0x62C2910A)
I (..\bsp\inc\CH378_HAL.H)(0x62BE4516)
I (..\bsp\inc\CH378INC.H)(0x62A31FD8)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\bsp\src\Logger.c)(0x62CBD092)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\logger.o --omf_browse .\objects\logger.crf --depend .\objects\logger.d)
I (..\bsp\inc\logger.h)(0x62A197B0)
I (..\bsp\inc\CH378_HAL.h)(0x62BE4516)
I (..\bsp\inc\CH378INC.H)(0x62A31FD8)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\bsp\inc\FILE_SYS.h)(0x62C2910A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\bsp\inc\bsp_rtc.h)(0x62A81D02)
I (..\Source\inc\Time_unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\INS_Data.h)(0x6552E6AA)
I (..\Source\inc\gnss.h)(0x653B9376)
I (..\Common\inc\data_convert.h)(0x62A843FC)
I (..\Source\inc\tlhtype.h)(0x650CEC14)
I (..\Source\inc\can_data.h)(0x64780368)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x658EB91C)
I (..\Source\src\INS912AlgorithmEntry.h)(0x65811410)
F (..\bsp\src\TCP_Server.c)(0x6295E26C)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\tcp_server.o --omf_browse .\objects\tcp_server.crf --depend .\objects\tcp_server.d)
I (..\bsp\inc\TCP_Server.h)(0x6295E258)
F (..\bsp\src\CH378_SPI_HW.C)(0x62CBD0BC)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\ch378_spi_hw.o --omf_browse .\objects\ch378_spi_hw.crf --depend .\objects\ch378_spi_hw.d)
I (..\bsp\inc\CH378_HAL.h)(0x62BE4516)
I (..\bsp\inc\CH378INC.H)(0x62A31FD8)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Source\inc\systick.h)(0x624D71D8)
I (..\bsp\inc\FILE_SYS.h)(0x62C2910A)
F (..\bsp\src\bsp_soft_i2c_master.c)(0x62C4FA22)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\bsp_soft_i2c_master.o --omf_browse .\objects\bsp_soft_i2c_master.crf --depend .\objects\bsp_soft_i2c_master.d)
I (..\bsp\inc\bsp_soft_i2c_master.h)(0x624D41E2)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\bsp\inc\soft_i2c.h)(0x624CFB4C)
F (..\bsp\src\drv_spi.c)(0x6549F4E4)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\drv_spi.o --omf_browse .\objects\drv_spi.crf --depend .\objects\drv_spi.d)
I (..\bsp\inc\drv_spi.h)(0x6549EAE0)
I (..\Protocol\insdef.h)(0x63B3A700)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\bsp\inc\drv_gpio.h)(0x62CD2FD0)
I (..\bsp\inc\pin_numbers_def.h)(0x654D80B8)
I (..\Source\inc\systick.h)(0x624D71D8)
I (..\bsp\inc\HAL.H)(0x62EB59C4)
I (..\bsp\inc\CH378INC.H)(0x62A31FD8)
F (..\bsp\src\drv_gpio.c)(0x654C2ED4)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\drv_gpio.o --omf_browse .\objects\drv_gpio.crf --depend .\objects\drv_gpio.d)
I (..\bsp\inc\drv_gpio.h)(0x62CD2FD0)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Protocol\insdef.h)(0x63B3A700)
F (..\Common\src\data_convert.c)(0x62A853B6)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\data_convert.o --omf_browse .\objects\data_convert.crf --depend .\objects\data_convert.d)
I (..\Common\inc\data_convert.h)(0x62A843FC)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
F (..\RTT\SEGGER_RTT.c)(0x60DDE3EC)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\segger_rtt.o --omf_browse .\objects\segger_rtt.crf --depend .\objects\segger_rtt.d)
I (..\RTT\SEGGER_RTT.h)(0x60DDE3EC)
I (..\RTT\SEGGER_RTT_Conf.h)(0x60DDE3EC)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdarg.h)(0x60252376)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
F (..\RTT\SEGGER_RTT_printf.c)(0x60DDE3EC)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\segger_rtt_printf.o --omf_browse .\objects\segger_rtt_printf.crf --depend .\objects\segger_rtt_printf.d)
I (..\RTT\SEGGER_RTT.h)(0x60DDE3EC)
I (..\RTT\SEGGER_RTT_Conf.h)(0x60DDE3EC)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdarg.h)(0x60252376)
F (..\NAV\nav_cli.c)(0x653B9E2E)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\nav_cli.o --omf_browse .\objects\nav_cli.crf --depend .\objects\nav_cli.d)
F (..\INAV\adxl355.c)(0x657C3E40)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\adxl355.o --omf_browse .\objects\adxl355.crf --depend .\objects\adxl355.d)
I (..\Source\inc\appmain.h)(0x668B9CFE)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Source\inc\systick.h)(0x624D71D8)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Source\inc\main.h)(0x6489701A)
I (..\bsp\inc\bsp_gpio.h)(0x6388787C)
I (..\bsp\inc\bsp_flash.h)(0x68469E16)
I (..\Source\inc\INS_Data.h)(0x6552E6AA)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x653B9376)
I (..\Common\inc\data_convert.h)(0x62A843FC)
I (..\Source\inc\tlhtype.h)(0x650CEC14)
I (..\Source\inc\can_data.h)(0x64780368)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x658EB91C)
I (..\Source\src\INS912AlgorithmEntry.h)(0x65811410)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\bsp\inc\bsp_rtc.h)(0x62A81D02)
I (..\Source\inc\Time_unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\bsp\inc\bsp_can.h)(0x653B9376)
I (..\bsp\inc\bsp_fwdgt.h)(0x6254D09C)
I (..\bsp\inc\CH395SPI.H)(0x62BAD73A)
I (..\bsp\inc\CH395INC.H)(0x60C2CB7E)
I (..\bsp\inc\CH395CMD.H)(0x62B95964)
I (..\Source\inc\TCPServer.h)(0x654CC6F8)
I (..\bsp\inc\bsp_fmc.h)(0x62B91682)
I (..\bsp\inc\bsp_exti.h)(0x64ACAAA6)
I (..\bsp\inc\bmp280.h)(0x62A311AA)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\bsp\inc\bmp2.h)(0x60B09D02)
I (..\bsp\inc\bmp2_defs.h)(0x60B09D02)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\bsp\inc\common.h)(0x60B09D02)
I (..\bsp\inc\CH378_HAL.h)(0x62BE4516)
I (..\bsp\inc\CH378INC.H)(0x62A31FD8)
I (..\bsp\inc\logger.h)(0x62A197B0)
I (..\bsp\inc\FILE_SYS.h)(0x62C2910A)
I (..\bsp\inc\bsp_tim.h)(0x6500098E)
I (..\Source\inc\fpgad.h)(0x6846ABB0)
I (..\Source\src\appdefine.h)(0x65766CD4)
I (..\Protocol\serial.h)(0x6576E71A)
I (..\Protocol\insdef.h)(0x63B3A700)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\Protocol\computerFrameParse.h)(0x6846AFE3)
I (..\Source\src\gdtypedefine.h)(0x66E23E06)
I (..\Protocol\frame_analysis.h)(0x657BF0F6)
I (..\Source\inc\deviceconfig.h)(0x66EBBD3E)
I (..\Source\src\datado.h)(0x66A70052)
I (..\INAV\ins.h)(0x66535980)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\ctype.h)(0x60252376)
F (..\INAV\private_math.c)(0x658D8E64)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\private_math.o --omf_browse .\objects\private_math.crf --depend .\objects\private_math.d)
I (..\INAV\ins.h)(0x66535980)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\ctype.h)(0x60252376)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
F (..\INAV\readpaoche.c)(0x669B6BFE)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\readpaoche.o --omf_browse .\objects\readpaoche.crf --depend .\objects\readpaoche.d)
I (..\Source\inc\appmain.h)(0x668B9CFE)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Source\inc\systick.h)(0x624D71D8)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Source\inc\main.h)(0x6489701A)
I (..\bsp\inc\bsp_gpio.h)(0x6388787C)
I (..\bsp\inc\bsp_flash.h)(0x68469E16)
I (..\Source\inc\INS_Data.h)(0x6552E6AA)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x653B9376)
I (..\Common\inc\data_convert.h)(0x62A843FC)
I (..\Source\inc\tlhtype.h)(0x650CEC14)
I (..\Source\inc\can_data.h)(0x64780368)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x658EB91C)
I (..\Source\src\INS912AlgorithmEntry.h)(0x65811410)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\bsp\inc\bsp_rtc.h)(0x62A81D02)
I (..\Source\inc\Time_unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\bsp\inc\bsp_can.h)(0x653B9376)
I (..\bsp\inc\bsp_fwdgt.h)(0x6254D09C)
I (..\bsp\inc\CH395SPI.H)(0x62BAD73A)
I (..\bsp\inc\CH395INC.H)(0x60C2CB7E)
I (..\bsp\inc\CH395CMD.H)(0x62B95964)
I (..\Source\inc\TCPServer.h)(0x654CC6F8)
I (..\bsp\inc\bsp_fmc.h)(0x62B91682)
I (..\bsp\inc\bsp_exti.h)(0x64ACAAA6)
I (..\bsp\inc\bmp280.h)(0x62A311AA)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\bsp\inc\bmp2.h)(0x60B09D02)
I (..\bsp\inc\bmp2_defs.h)(0x60B09D02)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\bsp\inc\common.h)(0x60B09D02)
I (..\bsp\inc\CH378_HAL.h)(0x62BE4516)
I (..\bsp\inc\CH378INC.H)(0x62A31FD8)
I (..\bsp\inc\logger.h)(0x62A197B0)
I (..\bsp\inc\FILE_SYS.h)(0x62C2910A)
I (..\bsp\inc\bsp_tim.h)(0x6500098E)
I (..\Source\inc\fpgad.h)(0x6846ABB0)
I (..\Source\src\appdefine.h)(0x65766CD4)
I (..\Protocol\serial.h)(0x6576E71A)
I (..\Protocol\insdef.h)(0x63B3A700)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\Protocol\computerFrameParse.h)(0x6846AFE3)
I (..\Source\src\gdtypedefine.h)(0x66E23E06)
I (..\Protocol\frame_analysis.h)(0x657BF0F6)
I (..\Source\inc\deviceconfig.h)(0x66EBBD3E)
I (..\Source\src\datado.h)(0x66A70052)
I (..\INAV\ins.h)(0x66535980)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\ctype.h)(0x60252376)
F (..\INAV\navi.c)(0x66B526E6)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\navi.o --omf_browse .\objects\navi.crf --depend .\objects\navi.d)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\INAV\DATASTRUCT.h)(0x6660751A)
I (..\INAV\CONST.h)(0x66987428)
I (..\Source\inc\deviceconfig.h)(0x66EBBD3E)
I (..\INAV\TYPEDEFINE.h)(0x665374EE)
I (..\INAV\FUNCTION.h)(0x66B5255C)
I (..\INAV\ins.h)(0x66535980)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\ctype.h)(0x60252376)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\INAV\EXTERNGLOBALDATA.h)(0x664F5574)
F (..\INAV\align.c)(0x6514E4B4)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\align.o --omf_browse .\objects\align.crf --depend .\objects\align.d)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\INAV\DATASTRUCT.h)(0x6660751A)
I (..\INAV\CONST.h)(0x66987428)
I (..\Source\inc\deviceconfig.h)(0x66EBBD3E)
I (..\INAV\TYPEDEFINE.h)(0x665374EE)
I (..\INAV\FUNCTION.h)(0x66B5255C)
I (..\INAV\ins.h)(0x66535980)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\ctype.h)(0x60252376)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
F (..\INAV\AnnTempCompen.c)(0x6503CFAC)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\anntempcompen.o --omf_browse .\objects\anntempcompen.crf --depend .\objects\anntempcompen.d)
I (..\INAV\DATASTRUCT.h)(0x6660751A)
I (..\INAV\CONST.h)(0x66987428)
I (..\Source\inc\deviceconfig.h)(0x66EBBD3E)
I (..\INAV\TYPEDEFINE.h)(0x665374EE)
I (..\INAV\FUNCTION.h)(0x66B5255C)
I (..\INAV\ins.h)(0x66535980)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\ctype.h)(0x60252376)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\INAV\EXTERNGLOBALDATA.h)(0x664F5574)
F (..\INAV\kalman.c)(0x66A34DEC)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\kalman.o --omf_browse .\objects\kalman.crf --depend .\objects\kalman.d)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\INAV\DATASTRUCT.h)(0x6660751A)
I (..\INAV\CONST.h)(0x66987428)
I (..\Source\inc\deviceconfig.h)(0x66EBBD3E)
I (..\INAV\TYPEDEFINE.h)(0x665374EE)
I (..\INAV\EXTERNGLOBALDATA.h)(0x664F5574)
I (..\INAV\FUNCTION.h)(0x66B5255C)
I (..\INAV\ins.h)(0x66535980)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\ctype.h)(0x60252376)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
F (..\INAV\matvecmath.c)(0x66835EDE)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\matvecmath.o --omf_browse .\objects\matvecmath.crf --depend .\objects\matvecmath.d)
I (..\INAV\const.h)(0x66987428)
I (..\Source\inc\deviceconfig.h)(0x66EBBD3E)
I (..\INAV\typedefine.h)(0x665374EE)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
F (..\INAV\dynamic_align.c)(0x6503E988)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\dynamic_align.o --omf_browse .\objects\dynamic_align.crf --depend .\objects\dynamic_align.d)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\INAV\DATASTRUCT.h)(0x6660751A)
I (..\INAV\CONST.h)(0x66987428)
I (..\Source\inc\deviceconfig.h)(0x66EBBD3E)
I (..\INAV\TYPEDEFINE.h)(0x665374EE)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\INAV\FUNCTION.h)(0x66B5255C)
I (..\INAV\ins.h)(0x66535980)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\ctype.h)(0x60252376)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
F (..\INAV\read_and_check_gnss_data.c)(0x66B5255C)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\read_and_check_gnss_data.o --omf_browse .\objects\read_and_check_gnss_data.crf --depend .\objects\read_and_check_gnss_data.d)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\INAV\DATASTRUCT.h)(0x6660751A)
I (..\INAV\CONST.h)(0x66987428)
I (..\Source\inc\deviceconfig.h)(0x66EBBD3E)
I (..\INAV\TYPEDEFINE.h)(0x665374EE)
I (..\INAV\EXTERNGLOBALDATA.h)(0x664F5574)
I (..\INAV\FUNCTION.h)(0x66B5255C)
I (..\INAV\ins.h)(0x66535980)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\ctype.h)(0x60252376)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
F (..\Protocol\computerFrameParse.c)(0x6846AFD5)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\computerframeparse.o --omf_browse .\objects\computerframeparse.crf --depend .\objects\computerframeparse.d)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Protocol\computerFrameParse.h)(0x6846AFE3)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Protocol\config.h)(0x64AB57C0)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\Source\inc\tlhtype.h)(0x650CEC14)
I (..\Source\inc\INS_Data.h)(0x6552E6AA)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\Source\inc\gnss.h)(0x653B9376)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FC)
I (..\Source\inc\can_data.h)(0x64780368)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x658EB91C)
I (..\Source\src\INS912AlgorithmEntry.h)(0x65811410)
I (..\Protocol\serial.h)(0x6576E71A)
I (..\Protocol\insdef.h)(0x63B3A700)
I (..\Protocol\uartadapter.h)(0x63B3A700)
I (..\Protocol\UartDefine.h)(0x63B3A700)
I (..\Source\inc\systick.h)(0x624D71D8)
I (..\Protocol\frame_analysis.h)(0x657BF0F6)
I (..\Source\inc\fpgad.h)(0x6846ABB0)
I (..\Source\src\appdefine.h)(0x65766CD4)
I (..\Protocol\calibration.h)(0x63B3A700)
I (..\Protocol\fmc_operation.h)(0x64AA8C5C)
F (..\Protocol\protocol.c)(0x65781DFE)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\protocol.o --omf_browse .\objects\protocol.crf --depend .\objects\protocol.d)
I (..\Protocol\computerFrameParse.h)(0x6846AFE3)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Protocol\config.h)(0x64AB57C0)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\Source\inc\tlhtype.h)(0x650CEC14)
I (..\Source\inc\INS_Data.h)(0x6552E6AA)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x653B9376)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FC)
I (..\Source\inc\can_data.h)(0x64780368)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x658EB91C)
I (..\Source\src\INS912AlgorithmEntry.h)(0x65811410)
I (..\Protocol\protocol.h)(0x65546778)
I (..\Protocol\serial.h)(0x6576E71A)
I (..\Protocol\insdef.h)(0x63B3A700)
I (..\Protocol\uartadapter.h)(0x63B3A700)
I (..\Protocol\UartDefine.h)(0x63B3A700)
I (..\Source\inc\fpgad.h)(0x6846ABB0)
I (..\Source\src\appdefine.h)(0x65766CD4)
I (..\Source\src\gdtypedefine.h)(0x66E23E06)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
F (..\Protocol\frame_analysis.c)(0x669498D0)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\frame_analysis.o --omf_browse .\objects\frame_analysis.crf --depend .\objects\frame_analysis.d)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Protocol\frame_analysis.h)(0x657BF0F6)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Source\inc\INS_Data.h)(0x6552E6AA)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (..\Source\inc\gnss.h)(0x653B9376)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FC)
I (..\Source\inc\tlhtype.h)(0x650CEC14)
I (..\Source\inc\can_data.h)(0x64780368)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x658EB91C)
I (..\Source\src\INS912AlgorithmEntry.h)(0x65811410)
I (..\Protocol\insdef.h)(0x63B3A700)
I (..\Protocol\serial.h)(0x6576E71A)
I (..\Protocol\uartadapter.h)(0x63B3A700)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\Protocol\UartDefine.h)(0x63B3A700)
I (..\Protocol\computerFrameParse.h)(0x6846AFE3)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\bsp\inc\bsp_can.h)(0x653B9376)
I (..\Source\src\gdtypedefine.h)(0x66E23E06)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Source\src\appdefine.h)(0x65766CD4)
F (..\Protocol\InsTestingEntry.c)(0x6846ACA5)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\instestingentry.o --omf_browse .\objects\instestingentry.crf --depend .\objects\instestingentry.d)
I (..\Protocol\InsTestingEntry.h)(0x68469E00)
I (..\Source\src\gdtypedefine.h)(0x66E23E06)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Source\src\INS912AlgorithmEntry.h)(0x65811410)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\Protocol\frame_analysis.h)(0x657BF0F6)
I (..\Source\inc\INS_Data.h)(0x6552E6AA)
I (..\Library\CMSIS\arm_math.h)(0x658D9236)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x653B9376)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FC)
I (..\Source\inc\tlhtype.h)(0x650CEC14)
I (..\Source\inc\can_data.h)(0x64780368)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x658EB91C)
I (..\Protocol\insdef.h)(0x63B3A700)
I (..\INAV\EXTERNGLOBALDATA.h)(0x664F5574)
I (..\INAV\DATASTRUCT.h)(0x6660751A)
I (..\INAV\CONST.h)(0x66987428)
I (..\Source\inc\deviceconfig.h)(0x66EBBD3E)
I (..\INAV\TYPEDEFINE.h)(0x665374EE)
I (..\INAV\ins.h)(0x66535980)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\ctype.h)(0x60252376)
I (..\INAV\FUNCTION.h)(0x66B5255C)
I (..\Source\src\datado.h)(0x66A70052)
I (..\Source\Edwoy\app_tool.h)(0x667BEF7E)
I (..\Source\Edwoy\pjt_glb_head.h)(0x664AFB4E)
I (..\Source\Edwoy\types.h)(0x65EFFA3A)
I (..\Source\Edwoy\convert.h)(0x66052AEE)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\Protocol\serial.h)(0x6576E71A)
I (..\Source\Edwoy\pjt_board.h)(0x667CE2D4)
I (..\Source\inc\systick.h)(0x624D71D8)
I (..\Source\inc\appmain.h)(0x668B9CFE)
I (..\Source\inc\main.h)(0x6489701A)
I (..\bsp\inc\bsp_gpio.h)(0x6388787C)
I (..\bsp\inc\bsp_flash.h)(0x68469E16)
I (..\bsp\inc\bsp_rtc.h)(0x62A81D02)
I (..\Source\inc\Time_unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\bsp\inc\bsp_can.h)(0x653B9376)
I (..\bsp\inc\bsp_fwdgt.h)(0x6254D09C)
I (..\bsp\inc\CH395SPI.H)(0x62BAD73A)
I (..\bsp\inc\CH395INC.H)(0x60C2CB7E)
I (..\bsp\inc\CH395CMD.H)(0x62B95964)
I (..\Source\inc\TCPServer.h)(0x654CC6F8)
I (..\bsp\inc\bsp_fmc.h)(0x62B91682)
I (..\bsp\inc\bsp_exti.h)(0x64ACAAA6)
I (..\bsp\inc\bmp280.h)(0x62A311AA)
I (..\bsp\inc\bmp2.h)(0x60B09D02)
I (..\bsp\inc\bmp2_defs.h)(0x60B09D02)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\bsp\inc\common.h)(0x60B09D02)
I (..\bsp\inc\CH378_HAL.h)(0x62BE4516)
I (..\bsp\inc\CH378INC.H)(0x62A31FD8)
I (..\bsp\inc\logger.h)(0x62A197B0)
I (..\bsp\inc\FILE_SYS.h)(0x62C2910A)
I (..\bsp\inc\bsp_tim.h)(0x6500098E)
I (..\Source\inc\fpgad.h)(0x6846ABB0)
I (..\Source\src\appdefine.h)(0x65766CD4)
I (..\Protocol\computerFrameParse.h)(0x6846AFE3)
I (..\Source\Edwoy\sensor_misc.h)(0x6698CBB2)
F (..\Source\Edwoy\pjt_board.c)(0x667CE2DC)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\pjt_board.o --omf_browse .\objects\pjt_board.crf --depend .\objects\pjt_board.d)
I (..\Source\Edwoy\pjt_glb_head.h)(0x664AFB4E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\Source\Edwoy\types.h)(0x65EFFA3A)
I (..\Source\Edwoy\convert.h)(0x66052AEE)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Protocol\serial.h)(0x6576E71A)
I (..\Protocol\insdef.h)(0x63B3A700)
I (..\Source\Edwoy\pjt_board.h)(0x667CE2D4)
I (..\Source\inc\systick.h)(0x624D71D8)
I (..\Source\Edwoy\app_tool.h)(0x667BEF7E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdarg.h)(0x60252376)
F (..\Source\Edwoy\pjt_board.h)(0x667CE2D4)()
F (..\Source\Edwoy\sensor_misc.c)(0x6684A296)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\sensor_misc.o --omf_browse .\objects\sensor_misc.crf --depend .\objects\sensor_misc.d)
I (..\Source\Edwoy\sensor_misc.h)(0x6698CBB2)
I (..\Source\Edwoy\pjt_glb_head.h)(0x664AFB4E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\Source\Edwoy\types.h)(0x65EFFA3A)
I (..\Source\Edwoy\convert.h)(0x66052AEE)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Protocol\serial.h)(0x6576E71A)
I (..\Protocol\insdef.h)(0x63B3A700)
F (..\Source\Edwoy\app_tool.c)(0x667BEFDA)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\app_tool.o --omf_browse .\objects\app_tool.crf --depend .\objects\app_tool.d)
I (..\Source\Edwoy\app_tool.h)(0x667BEF7E)
I (..\Source\Edwoy\pjt_glb_head.h)(0x664AFB4E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\Source\Edwoy\types.h)(0x65EFFA3A)
I (..\Source\Edwoy\convert.h)(0x66052AEE)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Protocol\serial.h)(0x6576E71A)
I (..\Protocol\insdef.h)(0x63B3A700)
I (..\bsp\inc\bsp_uart.h)(0x6245473A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdarg.h)(0x60252376)
F (..\Source\Edwoy\app_tool.h)(0x667BEF7E)()
F (..\Source\Edwoy\sensor_misc.h)(0x6698CBB2)()
F (..\Source\Edwoy\pjt_glb_head.h)(0x664AFB4E)()
F (..\Library\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s)(0x658EB580)(--cpu Cortex-M4.fp.sp -g --apcs=interwork --pd "__MICROLIB SETA 1"

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

--pd "__UVISION_VERSION SETA 536" --pd "GD32F470 SETA 1"

--list .\listings\startup_gd32f450_470.lst --xref -o .\objects\startup_gd32f450_470.o --depend .\objects\startup_gd32f450_470.d)
F (..\Library\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c)(0x668CD5E2)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\system_gd32f4xx.o --omf_browse .\objects\system_gd32f4xx.crf --depend .\objects\system_gd32f4xx.d)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\CMSIS\arm_cortexM4lf_math.lib)(0x581CC65E)()
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_adc.o --omf_browse .\objects\gd32f4xx_adc.crf --depend .\objects\gd32f4xx_adc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c)(0x6507C602)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_can.o --omf_browse .\objects\gd32f4xx_can.crf --depend .\objects\gd32f4xx_can.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_crc.o --omf_browse .\objects\gd32f4xx_crc.crf --depend .\objects\gd32f4xx_crc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_ctc.o --omf_browse .\objects\gd32f4xx_ctc.crf --depend .\objects\gd32f4xx_ctc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_dac.o --omf_browse .\objects\gd32f4xx_dac.crf --depend .\objects\gd32f4xx_dac.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_dbg.o --omf_browse .\objects\gd32f4xx_dbg.crf --depend .\objects\gd32f4xx_dbg.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_dci.o --omf_browse .\objects\gd32f4xx_dci.crf --depend .\objects\gd32f4xx_dci.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_dma.o --omf_browse .\objects\gd32f4xx_dma.crf --depend .\objects\gd32f4xx_dma.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_enet.o --omf_browse .\objects\gd32f4xx_enet.crf --depend .\objects\gd32f4xx_enet.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_exmc.o --omf_browse .\objects\gd32f4xx_exmc.crf --depend .\objects\gd32f4xx_exmc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_exti.o --omf_browse .\objects\gd32f4xx_exti.crf --depend .\objects\gd32f4xx_exti.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_fmc.o --omf_browse .\objects\gd32f4xx_fmc.crf --depend .\objects\gd32f4xx_fmc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_fwdgt.o --omf_browse .\objects\gd32f4xx_fwdgt.crf --depend .\objects\gd32f4xx_fwdgt.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c)(0x6481A692)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_gpio.o --omf_browse .\objects\gd32f4xx_gpio.crf --depend .\objects\gd32f4xx_gpio.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_i2c.o --omf_browse .\objects\gd32f4xx_i2c.crf --depend .\objects\gd32f4xx_i2c.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_ipa.o --omf_browse .\objects\gd32f4xx_ipa.crf --depend .\objects\gd32f4xx_ipa.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_iref.o --omf_browse .\objects\gd32f4xx_iref.crf --depend .\objects\gd32f4xx_iref.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_misc.o --omf_browse .\objects\gd32f4xx_misc.crf --depend .\objects\gd32f4xx_misc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_pmu.o --omf_browse .\objects\gd32f4xx_pmu.crf --depend .\objects\gd32f4xx_pmu.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_rcu.o --omf_browse .\objects\gd32f4xx_rcu.crf --depend .\objects\gd32f4xx_rcu.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_rtc.o --omf_browse .\objects\gd32f4xx_rtc.crf --depend .\objects\gd32f4xx_rtc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_sdio.o --omf_browse .\objects\gd32f4xx_sdio.crf --depend .\objects\gd32f4xx_sdio.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_spi.o --omf_browse .\objects\gd32f4xx_spi.crf --depend .\objects\gd32f4xx_spi.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_syscfg.o --omf_browse .\objects\gd32f4xx_syscfg.crf --depend .\objects\gd32f4xx_syscfg.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_timer.o --omf_browse .\objects\gd32f4xx_timer.crf --depend .\objects\gd32f4xx_timer.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_tli.o --omf_browse .\objects\gd32f4xx_tli.crf --depend .\objects\gd32f4xx_tli.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_trng.o --omf_browse .\objects\gd32f4xx_trng.crf --depend .\objects\gd32f4xx_trng.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_usart.o --omf_browse .\objects\gd32f4xx_usart.crf --depend .\objects\gd32f4xx_usart.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c)(0x6229735A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol -I ..\INAV -I ..\Source\Edwoy

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS -DCMPL_CODE_EDWOY

-o .\objects\gd32f4xx_wwdgt.o --omf_browse .\objects\gd32f4xx_wwdgt.crf --depend .\objects\gd32f4xx_wwdgt.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735A)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735A)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735A)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453246)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
