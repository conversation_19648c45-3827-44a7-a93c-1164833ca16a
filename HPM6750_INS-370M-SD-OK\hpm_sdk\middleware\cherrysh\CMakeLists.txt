## Script for hpmicro ##
sdk_inc(.)
sdk_src(chry_shell.c)

sdk_src(builtin/help.c)
sdk_src(builtin/shsize.c)
sdk_src(builtin/login.c)

if("${CONFIG_CHERRYSH_INTERFACE}" STREQUAL "uart")
if(CONFIG_FREERTOS)
    sdk_inc(port/hpm/freertos_uart/inc)
    sdk_src(port/hpm/freertos_uart/src/shell.c)
else()
    if (CONFIG_UCOS_III OR CONFIG_THREADX OR CONFIG_RTTHREAD)
        message(FATAL_ERROR "OS currently only supports freertos")
    else()
        sdk_inc(port/hpm/barebone_uart/inc)
        sdk_src(port/hpm/barebone_uart/src/shell.c)
    endif()
endif()
elseif("${CONFIG_CHERRYSH_INTERFACE}" STREQUAL "usb")
if(CONFIG_FREERTOS)
    sdk_inc(port/hpm/freertos_usb/inc)
    sdk_src(port/hpm/freertos_usb/src/shell.c)
else()
    if (CONFIG_UCOS_III OR CONFIG_THREADX OR CONFIG_RTTHREAD)
        message(FATAL_ERROR "OS currently only supports freertos")
    else()
        sdk_inc(port/hpm/barebone_usb/inc)
        sdk_src(port/hpm/barebone_usb/src/shell.c)
    endif()
endif()
else()
message(FATAL_ERROR "CONFIG_CHERRYSH_INTERFACE is empty, please assign it to uart or usb")
endif()
