# clang-format configuration file. Intended for clang-format >= 11.0
#
# For more information, see:
#
#   https://clang.llvm.org/docs/ClangFormat.html
#   https://clang.llvm.org/docs/ClangFormatStyleOptions.html
#
---
# 语言: None, Cpp, Java, JavaScript, ObjC, Proto, TableGen, TextProto
Language:	Cpp
# BasedOnStyle:	LLVM
# 访问说明符(public、private等)的偏移
AccessModifierOffset:	-4
# 开括号(开圆括号、开尖括号、开方括号)后的对齐: Align, DontAlign, AlwaysBreak(总是在开括号后换行)
AlignAfterOpenBracket:	Align
# 连续赋值时，对齐所有等号
AlignConsecutiveAssignments:	false
# 对齐位域
AlignConsecutiveBitFields: true
# 连续声明时，对齐所有声明的变量名
AlignConsecutiveDeclarations:	false
# 连续宏时，进行对齐
AlignConsecutiveMacros: true
# 左对齐逃脱换行(使用反斜杠换行)的反斜杠
AlignEscapedNewlines:	Left
# 水平对齐二元和三元表达式的操作数
AlignOperands:	true
# 对齐连续的尾随的注释
AlignTrailingComments:	true
# 允许函数声明的所有参数在放在下一行
AllowAllParametersOfDeclarationOnNextLine:	false
# 允许短的块放在同一行
AllowShortBlocksOnASingleLine:	false
# 允许短的case标签放在同一行
AllowShortCaseLabelsOnASingleLine:	false
# 允许短的函数放在同一行: None, InlineOnly(定义在类中), Empty(空函数), Inline(定义在类中，空函数), All
AllowShortFunctionsOnASingleLine:	None
# 允许短的if语句保持在同一行
AllowShortIfStatementsOnASingleLine:	false
# 允许短的循环保持在同一行
AllowShortLoopsOnASingleLine:	false
# 总是在定义返回类型后换行(deprecated)
AlwaysBreakAfterDefinitionReturnType:	None
# 总是在返回类型后换行: None, All, TopLevel(顶级函数，不包括在类中的函数),
#  AllDefinitions(所有的定义，不包括声明), TopLevelDefinitions(所有的顶级函数的定义)
AlwaysBreakAfterReturnType:	None
# 总是在多行string字面量前换行
AlwaysBreakBeforeMultilineStrings:	false
# 总是在template声明后换行
AlwaysBreakTemplateDeclarations:	false
# false表示函数实参要么都在同一行，要么都各自一行
BinPackArguments:	true
# false表示所有形参要么都在同一行，要么都各自一行
BinPackParameters:	true
# 大括号换行，只有当BreakBeforeBraces设置为Custom时才有效
BraceWrapping:
    AfterClass: false
    AfterControlStatement: false
    AfterEnum: false
    AfterFunction: true
    AfterNamespace: false
    AfterObjCDeclaration: false
    AfterStruct: false
    AfterUnion: false
    AfterExternBlock: false # Unknown to clang-format-5.0
    BeforeCatch: false
    BeforeElse: false
    IndentBraces: false
    SplitEmptyFunction: true # Unknown to clang-format-4.0
    SplitEmptyRecord: true # Unknown to clang-format-4.0
    SplitEmptyNamespace: true # Unknown to clang-format-4.0
# 在二元运算符前换行: None(在操作符后换行), NonAssignment(在非赋值的操作符前换行), All(在操作符前换行)
BreakBeforeBinaryOperators:	None
BreakBeforeBraces:	Custom
#BreakBeforeInheritanceComma: false # Unknown to clang-format-4.0
# 在三元运算符前换行
BreakBeforeTernaryOperators:	false
# 在构造函数的初始化列表的逗号前换行
BreakConstructorInitializersBeforeComma:	false
BreakAfterJavaFieldAnnotations: false
BreakStringLiterals: false
# 每行字符的限制，0表示没有限制
ColumnLimit:	0
# 描述具有特殊意义的注释的正则表达式，它不应该被分割为多行或以其它方式改变
CommentPragmas:	'^ IWYU pragma:'
CompactNamespaces: false # Unknown to clang-format-4.0
# 构造函数的初始化列表要么都在同一行，要么都各自一行
ConstructorInitializerAllOnOneLineOrOnePerLine:	false
# 构造函数的初始化列表的缩进宽度
ConstructorInitializerIndentWidth:	4
# 延续的行的缩进宽度
ContinuationIndentWidth:	4
# 去除C++11的列表初始化的大括号{后和}前的空格
Cpp11BracedListStyle:	false
# 继承最常用的指针和引用的对齐方式
DerivePointerAlignment:	false
# 关闭格式化
DisableFormat:	false
ForEachMacros:
  - 'SHELL_EXPORT_CMD'

# 自动检测函数的调用和定义是否被格式为每行一个参数(Experimental)
ExperimentalAutoDetectBinPacking:	false
# 缩进case标签
IndentCaseLabels:	true
# 缩进宽度
IndentWidth:	4
# 函数返回类型换行时，缩进函数声明或函数定义的函数名
IndentWrappedFunctionNames:	false
# 保留在块开始处的空行
KeepEmptyLinesAtTheStartOfBlocks:	false
# 开始一个块的宏的正则表达式
MacroBlockBegin:	''
# 结束一个块的宏的正则表达式
MacroBlockEnd:	''
# 连续空行的最大数量
MaxEmptyLinesToKeep:	1
# 命名空间的缩进: None, Inner(缩进嵌套的命名空间中的内容), All
NamespaceIndentation:	None
# 使用ObjC块时缩进宽度
ObjCBlockIndentWidth:	4
# 在ObjC的@property后添加一个空格
ObjCSpaceAfterProperty:	false
# 在ObjC的protocol列表前添加一个空格
ObjCSpaceBeforeProtocolList:	true
# 在call(后对函数调用换行的penalty
PenaltyBreakBeforeFirstCallParameter:	30
# 在一个注释中引入换行的penalty
PenaltyBreakComment:	10
# 第一次在<<前换行的penalty
PenaltyBreakFirstLessLess:	0
# 在一个字符串字面量中引入换行的penalty
PenaltyBreakString:	10
# 对于每个在行字符数限制之外的字符的penalty
PenaltyExcessCharacter:	100
# 将函数的返回类型放到它自己的行的penalty
PenaltyReturnTypeOnItsOwnLine:	60
# 指针和引用的对齐: Left, Right, Middle
PointerAlignment:	Right
# 允许重新排版注释
ReflowComments:	false
# 允许排序#include
SortIncludes:	false
# 在C风格类型转换后添加空格
SpaceAfterCStyleCast:	false
# 在赋值运算符之前添加空格
SpaceBeforeAssignmentOperators:	true
# 开圆括号之前添加一个空格: Never, ControlStatements, Always
SpaceBeforeParens:	ControlStatements
# 在空的圆括号中添加空格
SpaceInEmptyParentheses:	false
# 在尾随的评论前添加的空格数(只适用于//)
SpacesBeforeTrailingComments:	1
# 在尖括号的<后和>前添加空格
SpacesInAngles:	false
# 在容器(ObjC和JavaScript的数组和字典等)字面量中添加空格
SpacesInContainerLiterals:	false
# 在C风格类型转换的括号中添加空格
SpacesInCStyleCastParentheses:	false
# 在圆括号的(后和)前添加空格
SpacesInParentheses:	false
# 在方括号的[后和]前添加空格，lamda表达式和未指明大小的数组的声明不受影响
SpacesInSquareBrackets:	false
# 标准: Cpp03, Cpp11, Auto
Standard:	Cpp03
# tab宽度
TabWidth:	4
# 使用tab字符: Never, ForIndentation, ForContinuationAndIndentation, Always
UseTab:	Never
...

