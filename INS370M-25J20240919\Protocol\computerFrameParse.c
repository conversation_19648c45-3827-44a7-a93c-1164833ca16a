#include <math.h>
#include "computerFrameParse.h"
#include "data_convert.h"
#include "serial.h"
#include "uartadapter.h"
//#include "drv_timer.h"
//#include "protocol.h"
//#include "MultiTimer.h"
#include "systick.h"
#include "frame_analysis.h"
#include "gnss.h"
//#include "algorithm.h"
//#include "nav.h"
#include "fpgad.h"
#include "bsp_flash.h"  // 包含Flash操作函数
// 升级相关全局变量定义（避免链接错误）
uint8_t g_StartUpdateFirm = 0;      // 开始升级固件标志
uint8_t g_UpdateBackFlag = 0;       // 升级反馈标志

uint8_t xCommStatus;

struct calib_t caliData;

AppSettingTypeDef hSetting;
AppSettingTypeDef hDefaultSetting =
{
    .report_en = 0,
//    .fm_update = 0,
    .ProductID = 0x0001,
    .DeviceID  = 0x0001,
    .ChipID    =
    {
        0, 0, 0
    },

    //data
	.settingData = 
    {
        //9,
        100,
//        {
//            {cfg_format_GIPOT, 9, 10},
//            {cfg_format_RAWIMU, 6, 100},
//            {cfg_format_RAWIMU, 6, 100}
//        },
        //系统工作状态
//        INS_BOOT_MODE_1,
//        INS_DATA_MODE_1,
        //GNSS臂杆参数 3
        {
            {0.0, 0.0, 0.0},
            {0.0, 0.0, 0.0},
            {0.0, 0.0, 0.0},
            {0.0, 0.0, 0.0},
            0
        },
        //航向角补偿 4
//        0.0,
        //用户设置的坐标轴类型 5
        INS_DATA_MODE_1,
        //时间补偿 6
        0,
        //GNSS基线长度 7
        0.0,
    },
    {'D', 'E', 'V', '_', 'T', 'Y', 'P', 'E', ':', 'N', 'S', '8', '1', '0', '2', 'D', '(', 'I', 'M', 'U', '4', '6', '0', ' ', ')'},
    {'A', 'R', 'M', '1', '_', 'V', 'E', 'R', ':', ' ', '1', '.', '1', '.', '2', '\r', '\n'},
    {'A', 'R', 'M', '2', '_', 'V', 'E', 'R', ':', ' ', '1', '.', '0', '.', '1', '\r', '\n'},
    {'F', 'P', 'G', 'A', '_', 'V', 'E', 'R', ':', ' ', '1', '.', '0', '.', '0'},
};

void comm_read_ver_rsp(uint16_t cmd);
void comm_read_dev_type_rsp(uint16_t cmd);
void comm_saveCaliData(void);

uint32_t hash32(uint8_t *buff, uint32_t len)
{
    uint32_t crc = 0xffffffff;
    uint32_t i = 0;

    while (len--)
    {
        uint8_t byte = buff[i];
        crc = crc ^ byte;

        for (uint8_t j = 8; j > 0; --j)
        {
            crc = (crc >> 1) ^ (0xEDB88320 & (-(crc & 1)));
        }

        i++;
    }

    return crc ^ 0xffffffff;
}

float comm_get_baseline(void)
{
    return hSetting.settingData.gnssBaselineLength;
}

void comm_param_setbits(uint8_t bitIndex)
{
    hSetting.settingData.param.flag |= (0x1 << bitIndex);
}

void comm_param_clrbits(uint8_t bitIndex)
{
    hSetting.settingData.param.flag &= ~(0x1 << bitIndex);
}

float* comm_readAccelCaliPtr(void)
{
    return &caliData.accelCali.S_ax;
}

float* comm_readGyroCaliPtr(void)
{
    return &caliData.gyroCali.S_ax;
}

void comm_send_end_frame(uint16_t cmd)
{
    uint8_t  frameEnd[6];
    frameEnd[0] = 0xFA;
    frameEnd[1] = 0x55;
    frameEnd[2] = cmd >> 8;
    frameEnd[3] = cmd;
    frameEnd[4] = 0x00;
    frameEnd[5] = 0xFF;
    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, 6, frameEnd);

}

void comm_write_rsp(uint16_t cmd)
{
    uint8_t  frameEnd[7];
    frameEnd[0] = 0xFA;
    frameEnd[1] = 0x55;
    frameEnd[2] = cmd >> 8;
    frameEnd[3] = cmd;
    frameEnd[4] = 0x01;
    frameEnd[5] = 0x00;
    frameEnd[6] = 0xFF;
    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, 7, frameEnd);

}

void comm_cali_ehco_rsp(uint16_t cmd)
{
    uint8_t  frame[100];
    uint16_t len = sizeof(__8k21_cali_t) + 7;

    frame[0] = 0xFA;
    frame[1] = 0x55;
    frame[2] = cmd >> 8;
    frame[3] = cmd;
    frame[4] = 0x00;

    if(CMD_ACCEL_ADJUST == cmd)
        memcpy((void*)&frame[5], (void*)&caliData.accelCali.S_ax, sizeof(__8k21_cali_t));
    else
        memcpy((void*)&frame[5], (void*)&caliData.gyroCali.S_ax, sizeof(__8k21_cali_t));

    frame[len - 2] = 0x00;
    frame[len - 1] = 0xFF;

    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, len, frame);
}

const struct
{
    uint8_t index;
    uint32_t baudrate;
} comm_baud_table[] =
{
    {0, 0},
    {1, 9600},
    {2, 19200},
    {3, 38400},
    {4, 57600},
    {5, 115200},
    {6, 230400},
    {7, 460800},
    {8, 614400}
};

uint8_t naviTestFlg = 0;

uint8_t navi_test_statusRd(void)
{
    return naviTestFlg;
}

uint8_t comm_axis_read(void)
{
    return hSetting.settingData.imuAxis;
}

void comm_output_disable(void)
{
    //combineData.outputType = 0x04;
}

void comm_output_enable(void)
{
    //combineData.outputType = 0x00;
}

void comm_nav_para_syn(void)
{
    //memcpy((void*)&combineData.Param, (void*)&hSetting.settingData.param, sizeof(Param_t));
}

#include "calibration.h"
double gyroTrans[6], accelTrans[5];
char calib_str[100] = {0,};
uint8_t calib_sendBuf[100];
uint8_t calibFlag = 0;
uint8_t comm_test_switch(uint8_t* pData)
{
    if(0 == strncmp("freedom", (char const *)pData, strlen("freedom")))
    {
        calibFlag = 1;
    }
    else if(0 == strncmp("democracy", (char const *)pData, strlen("democracy")))
    {
        calibFlag = 2;
    }
    else if(0 == strncmp("fm_update", (char const *)pData, strlen("fm_update")))
    {
        return 1;
    }
    else if(0 == strncmp("liberate", (char const *)pData, strlen("liberate")))
    {
        calibFlag = 0;
        //combineData.outputType = 0x0;
    }

    return 0;
}

void comm_calib(void)
{
    //uint16_t len;

    if (calibFlag == 1)
    {
        //combineData.outputType = 0x7;

//        if(calibAccel(accelTrans))
//        {
//            memset(calib_str, '\0', sizeof(calib_str));
//#if IMU_MODULE_SELECT == IMU_MODULE_SCHA634
//            sprintf(calib_str, "#accdata,%0.7f,%0.7f,%0.7f,%0.7f,%0.7f\r\n", accelTrans[0], accelTrans[1], accelTrans[2], accelTrans[3], accelTrans[4]);
//#else
//            sprintf(calib_str, "#accdata,%0.7f,%0.7f,%0.7f,%0.7f\r\n", accelTrans[0], accelTrans[1], accelTrans[2], accelTrans[3]);
//#endif
//            len = strlen(calib_str);
//            memcpy((void*)&calib_sendBuf[0], (void*)calib_str, len);
//            Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, len, calib_sendBuf);
//        }
    }
    else if (calibFlag == 2)
    {
        //combineData.outputType = 0x7;

//        if(calibGyro(gyroTrans))
//        {
//            memset(calib_str, '\0', sizeof(calib_str));
//#if IMU_MODULE_SELECT == IMU_MODULE_SCHA634
//            sprintf(calib_str, "#gyrodata,%0.7f,%0.7f,%0.7f,%0.7f,%0.7f\r\n", gyroTrans[0], gyroTrans[1], gyroTrans[2], gyroTrans[3], gyroTrans[4]);
//#else
//            sprintf(calib_str, "#gyrodata,%0.7f,%0.7f,%0.7f,%0.7f\r\n", gyroTrans[0], gyroTrans[1], gyroTrans[2], gyroTrans[3]);
//#endif
//            len = strlen(calib_str);
//			memcpy((void*)&calib_sendBuf[0], (void*)calib_str, len);
//            Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, len, calib_sendBuf);
//        }

    }
}

extern	void rym_fm_update(void);
uint8_t frameParse(uint8_t* pData, uint16_t len)
{
    union
    {
        float f_val;
        uint8_t u8_val[4];
    } u_f2u8;
    uint8_t i;
    uint8_t valid = 0;
    float *fp = NULL;
    uint8_t* pDat = pData;
    uint16_t tCmd = 0;
    uint16_t dataLen = len;
    uint8_t  frameHead[3] = {0xAF, 0x55, 0xFA};

    if(comm_test_switch(pData))return 1;
	//iPMV_protocolParse(pData, len);
    //找到帧头
    do
    {
        if(0 == memcmp(pDat, frameHead, 3))
        {
            valid = 1;
            break;
        }

        pDat++;
        dataLen--;
    }
    while(dataLen > 0);

    if(valid)
    {
        pDat += 3;
        tCmd = ((*pDat) << 8) + (*(pDat + 1));

        pDat ++;
        pDat ++;

        switch(tCmd)
        {
            case CMD_SET_FM_UPDATE: 				//固件升级
                if(*pDat == FRAME_END)
                {
                    //回读参数
                    comm_output_disable();

                    // 启用固件升级模式
                    g_StartUpdateFirm = 1;

                    // 发送升级确认响应
                    comm_write_rsp(CMD_SET_FM_UPDATE);
                }

                return 1;

            case CMD_ACCEL_ADJUST: 				//加计校准
            {
                if(*pDat++ == 0)//read
                {
                    comm_cali_ehco_rsp(tCmd);
                }
                else	//write
                {
                    fp = &caliData.accelCali.S_ax;

                    for(i = 0; i < 12; i++)
                    {
                        u_f2u8.u8_val[0] = *pDat++;
                        u_f2u8.u8_val[1] = *pDat++;
                        u_f2u8.u8_val[2] = *pDat++;
                        u_f2u8.u8_val[3] = *pDat++;
                        *fp++ = u_f2u8.f_val;
                    }

                    fp = NULL;
                    comm_write_rsp(tCmd);
                }
            }

            comm_saveCaliData();
            return 0;

            case CMD_GYRO_ADJUST:				//陀螺校准
            {
                if(*pDat++ == 0)//read
                {
                    comm_cali_ehco_rsp(tCmd);
                }
                else	//write
                {
                    fp = &caliData.gyroCali.S_ax;

                    for(i = 0; i < 12; i++)
                    {
                        u_f2u8.u8_val[0] = *pDat++;
                        u_f2u8.u8_val[1] = *pDat++;
                        u_f2u8.u8_val[2] = *pDat++;
                        u_f2u8.u8_val[3] = *pDat++;
                        *fp++ = u_f2u8.f_val;
                    }

                    fp = NULL;
                    comm_write_rsp(tCmd);
                }
            }

            comm_saveCaliData();
            return 0;

            case CMD_SET_OUTPUT_FREQ:				//设定输出频率
                hSetting.settingData.freq = *pDat;
                comm_send_end_frame(tCmd);
                return 0;

            case CMD_SET_SAVE_CONFIG:				//存储参数
                if(*pDat == FRAME_END)
                {
                    //保存参数
                    comm_set_customPara();
                }

                break;

            case CMD_SET_RESUME_DEFAULT:				//恢复默认
                if(*pDat == FRAME_END)
                {
                    //恢复默认参数
                    comm_resume_defaultPara();
                }

                break;

            case CMD_SET_SAVE_ALL:				//全部应用
                //if((*(pDat + 42)) == FRAME_END)
            {
                //恢复默认参数
                //新加入的参数上位机没有保存,使用以前结构体赋值
                Temp_SettingDataTypeDef *pTempSetting=(Temp_SettingDataTypeDef *)pDat;
                {
                	hSetting.settingData.freq 							=	pTempSetting->freq;
					hSetting.settingData.param.gnssArmLength[0] 		=	pTempSetting->param.gnssArmLength[0];
					hSetting.settingData.param.gnssArmLength[1] 		=	pTempSetting->param.gnssArmLength[1];
					hSetting.settingData.param.gnssArmLength[2] 		=	pTempSetting->param.gnssArmLength[2];

					hSetting.settingData.param.gnssAtt_from_vehicle[0] 	=	pTempSetting->param.gnssAtt_from_vehicle[0];
					hSetting.settingData.param.gnssAtt_from_vehicle[1] 	=	pTempSetting->param.gnssAtt_from_vehicle[1];
					hSetting.settingData.param.gnssAtt_from_vehicle[2] 	=	pTempSetting->param.gnssAtt_from_vehicle[2];

					hSetting.settingData.param.OBArmLength[0] 			=	pTempSetting->param.OBArmLength[0];
					hSetting.settingData.param.OBArmLength[1] 			=	pTempSetting->param.OBArmLength[1];
					hSetting.settingData.param.OBArmLength[2] 			=	pTempSetting->param.OBArmLength[2];

					hSetting.settingData.param.OBAtt_from_vehicle[0] 	=	pTempSetting->param.OBAtt_from_vehicle[0];
					hSetting.settingData.param.OBAtt_from_vehicle[1] 	=	pTempSetting->param.OBAtt_from_vehicle[1];
					hSetting.settingData.param.OBAtt_from_vehicle[2] 	=	pTempSetting->param.OBAtt_from_vehicle[2];

					hSetting.settingData.param.flag 					=	pTempSetting->param.flag;

					hSetting.settingData.imuAxis						=	pTempSetting->imuAxis;
					hSetting.settingData.timeCompensation				=	pTempSetting->timeCompensation;
					hSetting.settingData.gnssBaselineLength				=	pTempSetting->gnssBaselineLength;
                }
            }

            break;

            case CMD_SET_READ_PARA:					//参数回读
                if(*pDat == FRAME_END)
                {
                    //回读参数
                    comm_para_ehco_rsp(tCmd);
                }

                break;

            case CMD_READ_FIRMWARE_VER:					//读取固件版本
                if(*pDat == FRAME_END)
                {
                    //回读参数
                    comm_read_ver_rsp(tCmd);
                }

                return 0;

            case CMD_READ_DEV_TYPE: 				//读取设备型号
                if(*pDat == FRAME_END)
                {
                    //回读参数
                    comm_read_dev_type_rsp(tCmd);
                }

                return 0;
#if 0

            case CMD_SET_SYS_MODE:					//设定系统工作模式
                if((*(pDat + 1)) == FRAME_END)
                {
//                    hSetting.settingData.datamode = (INS_DATA_ENUMTypeDef)(*(pDat + 1));
                    hSetting.settingData.workmode = (INS_BOOT_MODE_ENUMTypeDef)(*(pDat));
                }

                break;
#endif

            case CMD_SET_REPORT_MODE:					//设定系统上报模式
                if((*(pDat + 1)) == FRAME_END)
                {
                    hSetting.report_en = *pDat;
                }

                break;

            case CMD_SET_MECHAN_MIGRA:				//设置GNSS装配参数与基准点的坐标偏移
                if((*(pDat + 12)) == FRAME_END)
                {
                    uint8_t tData[4] = {0};
                    tData[0] = *(pDat + 0);
                    tData[1] = *(pDat + 1);
                    tData[2] = *(pDat + 2);
                    tData[3] = *(pDat + 3);
                    hSetting.settingData.param.gnssArmLength[0] = hex2Float(tData);
                    tData[0] = *(pDat + 4);
                    tData[1] = *(pDat + 5);
                    tData[2] = *(pDat + 6);
                    tData[3] = *(pDat + 7);
                    hSetting.settingData.param.gnssArmLength[1] = hex2Float(tData);
                    tData[0] = *(pDat + 8);
                    tData[1] = *(pDat + 9);
                    tData[2] = *(pDat + 10);
                    tData[3] = *(pDat + 11);
                    hSetting.settingData.param.gnssArmLength[2] = hex2Float(tData);
                    comm_param_setbits(0);
                    //comm_set_customPara();
                    comm_nav_para_syn();
//                    gnss_set_leverArm(hSetting.settingData.gnssMechanicalMigration_x,
//                                      hSetting.settingData.gnssMechanicalMigration_x,
//                                      hSetting.settingData.gnssMechanicalMigration_x,
//                                      0.0, 0.0, 0.0);
                }

                break;

            case CMD_SET_GNSS_VEHI:				//设置GNSS装配参数与基准点的坐标偏移
                if((*(pDat + 12)) == FRAME_END)
                {
                    uint8_t tData[4] = {0};
                    tData[0] = *(pDat + 0);
                    tData[1] = *(pDat + 1);
                    tData[2] = *(pDat + 2);
                    tData[3] = *(pDat + 3);
                    hSetting.settingData.param.gnssAtt_from_vehicle[0] = hex2Float(tData);
                    tData[0] = *(pDat + 4);
                    tData[1] = *(pDat + 5);
                    tData[2] = *(pDat + 6);
                    tData[3] = *(pDat + 7);
                    hSetting.settingData.param.gnssAtt_from_vehicle[1] = hex2Float(tData);
                    tData[0] = *(pDat + 8);
                    tData[1] = *(pDat + 9);
                    tData[2] = *(pDat + 10);
                    tData[3] = *(pDat + 11);
                    hSetting.settingData.param.gnssAtt_from_vehicle[2] = hex2Float(tData);
                    comm_param_setbits(1);
                    //comm_set_customPara();
                    comm_nav_para_syn();
//                    gnss_set_leverArm(hSetting.settingData.gnssMechanicalMigration_x,
//                                      hSetting.settingData.gnssMechanicalMigration_x,
//                                      hSetting.settingData.gnssMechanicalMigration_x,
//                                      0.0, 0.0, 0.0);
                }

                break;

            case CMD_SET_MECHAN_OBARM:				//设置GNSS装配参数与基准点的坐标偏移
                if((*(pDat + 12)) == FRAME_END)
                {
                    uint8_t tData[4] = {0};
                    tData[0] = *(pDat + 0);
                    tData[1] = *(pDat + 1);
                    tData[2] = *(pDat + 2);
                    tData[3] = *(pDat + 3);
                    hSetting.settingData.param.OBArmLength[0] = hex2Float(tData);
                    tData[0] = *(pDat + 4);
                    tData[1] = *(pDat + 5);
                    tData[2] = *(pDat + 6);
                    tData[3] = *(pDat + 7);
                    hSetting.settingData.param.OBArmLength[1] = hex2Float(tData);
                    tData[0] = *(pDat + 8);
                    tData[1] = *(pDat + 9);
                    tData[2] = *(pDat + 10);
                    tData[3] = *(pDat + 11);
                    hSetting.settingData.param.OBArmLength[2] = hex2Float(tData);
                    comm_param_setbits(2);
                    //comm_set_customPara();
                    comm_nav_para_syn();
//                    gnss_set_leverArm(hSetting.settingData.gnssMechanicalMigration_x,
//                                      hSetting.settingData.gnssMechanicalMigration_x,
//                                      hSetting.settingData.gnssMechanicalMigration_x,
//                                      0.0, 0.0, 0.0);
                }

                break;

            case CMD_SET_MECHAN_OB_VEHI:				//设置GNSS装配参数与基准点的坐标偏移
                if((*(pDat + 12)) == FRAME_END)
                {
                    uint8_t tData[4] = {0};
                    tData[0] = *(pDat + 0);
                    tData[1] = *(pDat + 1);
                    tData[2] = *(pDat + 2);
                    tData[3] = *(pDat + 3);
                    hSetting.settingData.param.OBAtt_from_vehicle[0] = hex2Float(tData);
                    tData[0] = *(pDat + 4);
                    tData[1] = *(pDat + 5);
                    tData[2] = *(pDat + 6);
                    tData[3] = *(pDat + 7);
                    hSetting.settingData.param.OBAtt_from_vehicle[1] = hex2Float(tData);
                    tData[0] = *(pDat + 8);
                    tData[1] = *(pDat + 9);
                    tData[2] = *(pDat + 10);
                    tData[3] = *(pDat + 11);
                    hSetting.settingData.param.OBAtt_from_vehicle[2] = hex2Float(tData);
                    comm_param_setbits(3);
                    //comm_set_customPara();
                    comm_nav_para_syn();
//                    gnss_set_leverArm(hSetting.settingData.gnssMechanicalMigration_x,
//                                      hSetting.settingData.gnssMechanicalMigration_x,
//                                      hSetting.settingData.gnssMechanicalMigration_x,
//                                      0.0, 0.0, 0.0);
                }

                break;

            case CMD_SET_COURSE_ANGLE:				//设置航向角补偿
                if((*(pDat + 4)) == FRAME_END)
                {
//                    uint8_t tData[4] = {0};
//                    tData[0] = *(pDat + 0);
//                    tData[1] = *(pDat + 1);
//                    tData[2] = *(pDat + 2);
//                    tData[3] = *(pDat + 3);
//                    hSetting.settingData.courseAngleCompensation = hex2Float(tData);
//                    comm_set_customPara();
                }

                break;

            case CMD_SET_ZERO_OFFSET_TIME:			//设置静态测零偏时间
                if((*(pDat + 2)) == FRAME_END)
                {
                    uint8_t tData[2] = {0};
                    tData[0] = *(pDat + 0);
                    tData[1] = *(pDat + 1);
                    hSetting.settingData.timeCompensation = (tData[1] << 8) + tData[0];
                    //comm_set_customPara();
                }

                break;

            case CMD_SET_USER_AXIS:					//设置用户设置的坐标轴与基准坐标轴的校正方式
                if((*(pDat + 1)) == FRAME_END)
                {
                    hSetting.settingData.imuAxis = *pDat;
                }

                break;

            case CMD_SET_GNSS_BASELINE:				//设置GNSS基线长度
                if((*(pDat + 4)) == FRAME_END)
                {
                    uint8_t tData[4] = {0};
                    tData[0] = *(pDat + 0);
                    tData[1] = *(pDat + 1);
                    tData[2] = *(pDat + 2);
                    tData[3] = *(pDat + 3);
                    hSetting.settingData.gnssBaselineLength = hex2Float(tData);
                    //comm_set_customPara();
                    //((*(pDat+3)<<24)+(*(pDat+2)<<16)+(*(pDat+1)<<8)+(*pDat));
                }

                break;

            case CMD_SET_ALL:
                if((*(pDat + 61)) == FRAME_END)
                {

                }

                break;

            case CMD_PARAM_DEBUG_SET:		//DEBUG开关
                //combineData.debug = *pDat;
                break;

            case CMD_PARAM_IMU_SELECT:		//IMU数据切换开关
            	//combineData.imuSelect = *pDat;
                //caliData.imuSelect = combineData.imuSelect;
                comm_saveCaliData();
                break;

            case CMD_PARAM_GPS_TYPE_SET:		//GPS数据类型设置
                //combineData.fusion = *pDat;
                break;

            case CMD_PARAM_DATA_OUTPUT_TYPE_SET:		//数据输出类型设置
                //combineData.outputType = *pDat;
                break;

			case CMD_PARAM_CALIB_RATE_SET:		//
                hSetting.calibRate = *pDat;
                break;
            default:
                return 0;

        }

        if(CMD_SET_READ_PARA != tCmd)
            comm_send_end_frame(tCmd);
    }

    return 0;
}

uint16_t comm_read_currentFreq(void)
{
    return hSetting.settingData.freq;
}

uint8_t comm_read_currentFrameType(uint8_t channel)
{
    return 1;//hSetting.settingData.serialFrameSetting[channel].frameType;
}

uint8_t comm_read_dataMode(void)
{
    return 1;//hSetting.settingData.datamode;
}

//data save
#include "fmc_operation.h"
static uint32_t save_addr = 0;
//#define PARA_DEFAULT_ADDRESS			((uint32_t)0x08100000)
#define PARA_STORE_BASE_ADDRESS			((uint32_t)0x08104000)	//  bill 2023-0625
//#define PARA_STORE_BASE_ADDRESS			((uint32_t)0x08040000)	//0x08040000  bill 2023-0625
#define PARA_STORE_ADDRESS_OFFSET		(sizeof(AppSettingTypeDef))
#define PARA_STORE_ADDRESS				(PARA_STORE_BASE_ADDRESS)
#define FMC_SECTOR13_SIZE				(1024 * 16)
#define FMC_SAVE_SIZE_THRESHOLD			(PARA_STORE_BASE_ADDRESS + FMC_SECTOR13_SIZE)

static uint32_t imu_type_save_addr = 0;
#define EXTRA_PARA_STORE_BASE_ADDRESS			((uint32_t)0x08108000)
#define EXTRA_PARA_STORE_ADDRESS_OFFSET			( sizeof(CalibTypeDef) )
#define EXTRA_PARA_STORE_ADDRESS				(EXTRA_PARA_STORE_BASE_ADDRESS)
#define EXTRA_FMC_SECTOR14_SIZE					(1024 * 16)
#define EXTRA_FMC_SAVE_SIZE_THRESHOLD			(EXTRA_PARA_STORE_BASE_ADDRESS + EXTRA_FMC_SECTOR14_SIZE)

//#define	comm_read_addrOffsetCount()		RTC_BKP1
//#define	comm_write_addrOffsetCount()	RTC_BKP1 = addr_offset_count

void comm_saveCaliData(void)
{
    //if((EXTRA_PARA_STORE_ADDRESS_OFFSET + imu_type_save_addr) > EXTRA_FMC_SAVE_SIZE_THRESHOLD)
    {
        //擦除扇区
        fmc_erase_sector_by_address(EXTRA_PARA_STORE_BASE_ADDRESS);
        imu_type_save_addr = EXTRA_PARA_STORE_BASE_ADDRESS;
    }
    caliData.hash = hash32((uint8_t*)&caliData.imuSelect, sizeof(CalibTypeDef) - 4);
    fmc_write_8bit_data(imu_type_save_addr, sizeof(CalibTypeDef), (int8_t*)&caliData.imuSelect);
    imu_type_save_addr += EXTRA_PARA_STORE_ADDRESS_OFFSET;
}

void comm_set_customPara(void)
{
    //if((PARA_STORE_ADDRESS_OFFSET + save_addr) > FMC_SAVE_SIZE_THRESHOLD)
    {
        //擦除扇区
        fmc_erase_sector_by_address(PARA_STORE_BASE_ADDRESS);
        save_addr = PARA_STORE_ADDRESS;
    }

    hSetting.hash = hash32((uint8_t*)&hSetting, sizeof(AppSettingTypeDef) - 4);
    fmc_write_8bit_data(save_addr, sizeof(AppSettingTypeDef), (int8_t*)&hSetting);
    save_addr += PARA_STORE_ADDRESS_OFFSET;
}

void comm_resume_defaultPara(void)
{
    //memcpy((void*)&hSetting.report_en, (void*)&hDefaultSetting.report_en, sizeof(AppSettingTypeDef));	//bill
    memcpy((void*)&hSetting, (void*)&hDefaultSetting, sizeof(AppSettingTypeDef));
    comm_set_customPara();
}

void comm_fm_update(void)
{
    memcpy((void*)&hSetting.facilityType[0], (void*)&hDefaultSetting.facilityType[0], sizeof(hDefaultSetting.facilityType));
    memcpy((void*)&hSetting.ARM1_FW_Ver[0], (void*)&hDefaultSetting.ARM1_FW_Ver[0], sizeof(hDefaultSetting.ARM1_FW_Ver) * 3);

//    if(combineData.memsType == 1)
//    {
//        strncpy((void*)&hSetting.facilityType[17], "SCHA634", strlen("SCHA634"));
//    }
//	else if(combineData.memsType == 2)
//    {
//        strncpy((void*)&hSetting.facilityType[17], "ADIS16X", strlen("ADIS16X"));
//    }
    //comm_set_customPara();
}

/**
 * @brief 初始化版本信息
 */
void comm_init_version_info(void)
{
    // 确保版本信息从默认设置复制到当前设置
    memcpy((void*)&hSetting.facilityType[0], (void*)&hDefaultSetting.facilityType[0], sizeof(hDefaultSetting.facilityType));
    memcpy((void*)&hSetting.ARM1_FW_Ver[0], (void*)&hDefaultSetting.ARM1_FW_Ver[0], sizeof(hDefaultSetting.ARM1_FW_Ver));
    memcpy((void*)&hSetting.ARM2_FW_Ver[0], (void*)&hDefaultSetting.ARM2_FW_Ver[0], sizeof(hDefaultSetting.ARM2_FW_Ver));
    memcpy((void*)&hSetting.FPGA_FW_Ver[0], (void*)&hDefaultSetting.FPGA_FW_Ver[0], sizeof(hDefaultSetting.FPGA_FW_Ver));
}

/**
 * @brief 处理升级数据包
 * @param pData 数据指针
 * @param len 数据长度
 */
void comm_handle_update_packet(uint8_t *pData, uint16_t len)
{
    static uint16_t package_index = 0;
    static uint16_t total_packages = 0;

    // 检查是否在升级模式
    if (g_StartUpdateFirm != 1) {
        return;
    }

    // 解析升级数据包
    if (len >= 4) {
        // 假设前两个字节是包索引，后两个字节是总包数
        package_index = (pData[0] << 8) | pData[1];
        total_packages = (pData[2] << 8) | pData[3];

        // 调用升级处理函数
        ParaUpdateHandle(&pData[4], package_index, total_packages, len - 4);

        // 发送升级进度响应
        uint8_t response[8];
        response[0] = 0xFA;
        response[1] = 0x55;
        response[2] = 0x80;  // 升级响应命令
        response[3] = 0x01;
        response[4] = package_index >> 8;
        response[5] = package_index & 0xFF;
        response[6] = 0x00;  // 状态：成功
        response[7] = 0xFF;

        Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, 8, response);
    }
}

uint8_t  frame[512];	//bill modify 2023/6/12
void comm_para_ehco_rsp(uint16_t cmd)
{
    //uint8_t  frame[100];	//bill modify 2023/6/12
    uint16_t len = sizeof(SettingDataTypeDef) + 6;

    frame[0] = 0xFA;
    frame[1] = 0x55;
    frame[2] = cmd >> 8;
    frame[3] = cmd;
    memcpy((void*)&frame[4], (void*)&hSetting.settingData.freq, sizeof(SettingDataTypeDef));
    frame[len - 2] = 0x00;
    frame[len - 1] = 0xFF;

    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, len, frame);

}

/**
 * @brief 参数升级处理函数
 * @param pucBuf 数据缓冲区
 * @param usIndex 包索引
 * @param usTotalBao 总包数
 * @param ucLen 数据长度
 */
void ParaUpdateHandle(uint8_t *pucBuf, uint16_t usIndex, uint16_t usTotalBao, uint8_t ucLen)
{
    static uint8_t update_buffer[1024 * 16];  // 16KB升级缓冲区（减小内存占用）
    static uint16_t received_packages = 0;
    static uint16_t total_packages = 0;
    static uint32_t total_size = 0;

    // 第一个包，初始化
    if (usIndex == 0) {
        received_packages = 0;
        total_packages = usTotalBao;
        total_size = 0;
        memset(update_buffer, 0xFF, sizeof(update_buffer));  // Flash默认值0xFF
    }

    // 检查包索引是否有效
    if (usIndex >= total_packages || ucLen == 0) {
        return;  // 无效包索引或长度
    }

    // 简化处理：直接写入Flash（假设连续地址）
    uint32_t flash_offset = 0x20000 + (usIndex * ucLen);  // 固件存储偏移地址（扇区5开始）

    // 调用Flash写入函数
    extern int norflash_write(uint32_t offset, const void *buf, uint32_t size_bytes);
    int result = norflash_write(flash_offset, pucBuf, ucLen);

    if (result == 0) {
        received_packages++;
        total_size += ucLen;
    }

    // 检查是否接收完所有包
    if (received_packages >= total_packages) {
        // 所有数据包接收完成
        g_UpdateBackFlag = 1;  // 设置升级完成标志
        g_StartUpdateFirm = 0;  // 退出升级模式

        // 发送升级完成响应
        uint8_t response[8];
        response[0] = 0xFA;
        response[1] = 0x55;
        response[2] = 0x80;  // 升级完成命令
        response[3] = 0x02;
        response[4] = (total_size >> 24) & 0xFF;
        response[5] = (total_size >> 16) & 0xFF;
        response[6] = (total_size >> 8) & 0xFF;
        response[7] = total_size & 0xFF;

        Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, 8, response);

        // 重置状态
        received_packages = 0;
        total_packages = 0;
        total_size = 0;
    }
}




void comm_read_ver_rsp(uint16_t cmd)
{
    uint8_t  frame[100];
    uint16_t len;

    // 确保版本信息已初始化
    comm_init_version_info();

    frame[0] = 0xFA;
    frame[1] = 0x55;
    frame[2] = cmd >> 8;
    frame[3] = cmd;

    // 检查版本字符串是否有效
    uint16_t arm1_len = strlen((char*)hSetting.ARM1_FW_Ver);
    uint16_t arm2_len = strlen((char*)hSetting.ARM2_FW_Ver);
    uint16_t fpga_len = strlen((char*)hSetting.FPGA_FW_Ver);

    // 如果版本字符串为空，使用默认版本
    if (arm1_len == 0) {
        strcpy((char*)hSetting.ARM1_FW_Ver, "ARM1_VER: 1.1.2\r\n");
        arm1_len = strlen((char*)hSetting.ARM1_FW_Ver);
    }
    if (arm2_len == 0) {
        strcpy((char*)hSetting.ARM2_FW_Ver, "ARM2_VER: 1.0.1\r\n");
        arm2_len = strlen((char*)hSetting.ARM2_FW_Ver);
    }
    if (fpga_len == 0) {
        strcpy((char*)hSetting.FPGA_FW_Ver, "FPGA_VER: 1.0.0");
        fpga_len = strlen((char*)hSetting.FPGA_FW_Ver);
    }

    // 复制版本信息到响应帧
    memcpy((void*)&frame[4], (void*)&hSetting.ARM1_FW_Ver, arm1_len);
    len = arm1_len;
    memcpy((void*)&frame[4 + len], (void*)&hSetting.ARM2_FW_Ver, arm2_len);
    len += arm2_len;
    memcpy((void*)&frame[4 + len], (void*)&hSetting.FPGA_FW_Ver, fpga_len);
    len += fpga_len;
    len += 6;
    frame[len - 2] = 0x00;
    frame[len - 1] = 0xFF;

    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, len, frame);


}

void comm_read_dev_type_rsp(uint16_t cmd)
{
    //uint8_t  frame[30];
    //DW_TEST
    uint8_t  frame[64];
    uint16_t len = strlen(hSetting.facilityType) + 6;

    frame[0] = 0xFA;
    frame[1] = 0x55;
    frame[2] = cmd >> 8;
    frame[3] = cmd;
    memcpy((void*)&frame[4], (void*)&hSetting.facilityType, strlen(hSetting.facilityType));
    frame[len - 2] = 0x00;
    frame[len - 1] = 0xFF;

    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, len, frame);

}

#if 0
MultiTimer baselineSynTimer, protocolSendTimer;

void baseline_synTimerCallback(MultiTimer* timer, void* userData)
{
    if(0.0 == gnss_get_baseline())return;

    if(0.0 == hSetting.settingData.gnssBaselineLength)return;

    if(fabs(gnss_get_baseline() - hSetting.settingData.gnssBaselineLength) < 0.03f)
    {
        comm_param_setbits(4);
    }
    else
    {
        comm_param_clrbits(4);
    }

    comm_nav_para_syn();
}

void baseline_synTimerInstall(void)
{
    MultiTimerStart(&baselineSynTimer, 1000, baseline_synTimerCallback, NULL);
}

void protocol_TimerCallback(MultiTimer* timer, void* userData)
{
    static uint16_t freqCnt = 0;
    uint16_t reportCount;

    reportCount = TIM_BASE_PERIOD / hSetting.settingData.freq;

    if(0 == (freqCnt % reportCount))
    {
        if(0 == combineData.outputType)
        {
//            frame_pack_and_send(&NAV_Data_Out, &hGPSData);
        }
    }

    freqCnt++;

    if(freqCnt >= TIM_BASE_PERIOD)freqCnt = 0;
}

void protocolTimerInstall(void)
{
    MultiTimerStart(&protocolSendTimer, 1, protocol_TimerCallback, NULL);
}
#endif

void IMU_test_data_send(void* pnav)
{
//	char navi_test_str[600] = {0};
//	_NAV_Data_Out_t* result = (_NAV_Data_Out_t*)pnav;
//	uint32_t len;
//	sprintf(navi_test_str, "#imudata,%d,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f,%0.5f\r\n", \
//            result->gpssecond, result->gyroX, result->gyroY, result->gyroZ, \
//            result->accelX, result->accelY, result->accelZ);
//	len = strlen(navi_test_str);
//    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, len, navi_test_str);
}

void protocol_send(void)
{
    static uint8_t sendCnt = 0;
	hSetting.settingData.freq = 200;
    uint8_t freq = (200 / hSetting.settingData.freq);
    sendCnt++;

    if(sendCnt >= freq)
    {
        sendCnt = 0;
		#if c_outputmode == c_outputmode_gdw
		//combineData.outputType = 1;
		#endif
//        if(0 == combineData.outputType)
//        {
//			#if c_systemrunmode_cantooling
//			
//			//grs422_frameD.vn = 12.5;
//			//grs422_frameD.lon = 112.35678;
//			//grs422_frameD.lat = 43.12345;
//			
//			NAV_Data_Out.accelX = grs422_frameD.accelX;
//			NAV_Data_Out.accelY = grs422_frameD.accelY;
//			NAV_Data_Out.accelZ = grs422_frameD.accelZ;

//			NAV_Data_Out.gyroX = grs422_frameD.gyroX;
//			NAV_Data_Out.gyroY = grs422_frameD.gyroY;
//			NAV_Data_Out.gyroZ = grs422_frameD.gyroZ;

//			NAV_Data_Out.roll = grs422_frameD.roll;
//			NAV_Data_Out.pitch = grs422_frameD.pitch;
//			NAV_Data_Out.heading = grs422_frameD.azimuth;

//			NAV_Data_Out.vn = grs422_frameD.vn;
//			NAV_Data_Out.ve = grs422_frameD.ve;
//			NAV_Data_Out.vu = grs422_frameD.vu;

//			NAV_Data_Out.longitude = grs422_frameD.lon;
//			NAV_Data_Out.latitude = grs422_frameD.lat;
//			
//			NAV_Data_Out.altitude = grs422_frameD.alt;	
//			NAV_Data_Out.gpssecond = grs422_frameD.gpsSec;
//			
//			NAV_Data_Out.Sate_Num = grs422_frameD.ins_numsv;
//			
//			combineData.scha634Info.temp_uno = grs422_frameD.sensor_temp;
//			combineData.scha634Info.temp_due = grs422_frameD.sensor_temp;
//			hGPSData.StarNum = grs422_frameD.ins_numsv;
//			hGPSData.gpsweek = grs422_frameD.ins_gnss_week;
//			//hGPSData.gpsweek = grs422_frameD.gpsWeek;
//			hGPSData.rtkStatus = grs422_frameD.ins_gnssflag_pos;
//			combineData.canInfo.flag = grs422_frameD.ins_car_status;
//			#endif
            frame_pack_and_send(NULL, NULL);
//			
//        }
//		else if(1 == combineData.outputType)
//		{
//			frame_miscel_send(&combineData, &hGPSData,&NAV_Data_Out);
//		}
    }
}

void adj_paraSyn(void)
{
    //memcpy((void*)&combineData.Adj, (void*)&caliData.adj, sizeof(AdjPara_t));
}

void wheel_is_running(void)
{
	//static uint32_t lastCanCounter = 0;
	//static uint8_t canPeriod = 0;
//	if(lastCanCounter == combineData.canInfo.counter)
//	{
//		canPeriod++;
//		if(canPeriod > 100)
//		{
//			canPeriod = 0;
//			combineData.canInfo.flag = 0;
//		}
//	}
//	else
//	{
//		canPeriod = 0;
//		combineData.canInfo.flag = 1;
//	}
//	lastCanCounter = combineData.canInfo.counter;
}

void adj_paraSave(void)
{
//    static uint8_t lastFlag = 0;

//    if((NAV_Data_Out.Nav_Standard_flag == 2) && (lastFlag == 1))
//    {
//        memcpy((void*)&caliData.adj.gyro_off[0], (void*)&NAV_Data_Out.gyro_off[0], sizeof(AdjPara_t));
//        memcpy((void*)&combineData.Adj.gyro_off[0], (void*)&caliData.adj.gyro_off[0], sizeof(AdjPara_t));
//        comm_saveCaliData();
//    }

//    lastFlag = NAV_Data_Out.Nav_Standard_flag;
}

void miscell_handle(void)
{
	wheel_is_running();
	adj_paraSave();
}

void comm_store_init(void)
{
    //uint8_t 	baud;
    uint8_t 	i = 0;
    uint32_t 	hash;
    uint32_t 	addr = PARA_STORE_ADDRESS;

    //查找最新数据
    do
    {
        //fmc_read_8bit_data(addr, sizeof(AppSettingTypeDef), (int8_t*)&hSetting.report_en);	//bill
		fmc_read_8bit_data(addr, sizeof(AppSettingTypeDef), (int8_t*)&hSetting);

        if(hSetting.report_en == 0xff)
            break;

        addr += PARA_STORE_ADDRESS_OFFSET;
        i++;
	 //DW_TEST
	 if(addr>FMC_SAVE_SIZE_THRESHOLD)
	 	break;
    }
    while(1);

    save_addr = addr;

    if(i)
        addr -= PARA_STORE_ADDRESS_OFFSET;

    //fmc_read_8bit_data(addr, sizeof(AppSettingTypeDef), (int8_t*)&hSetting.report_en);	//bill
    //hash = hash32((uint8_t*)&hSetting.report_en, sizeof(AppSettingTypeDef) - 4);
	fmc_read_8bit_data(addr, sizeof(AppSettingTypeDef), (int8_t*)&hSetting);
    hash = hash32((uint8_t*)&hSetting, sizeof(AppSettingTypeDef) - 4);

    if(hSetting.hash != hash)//校验判断有效性
    {
        fmc_erase_sector_by_address(PARA_STORE_BASE_ADDRESS);
        comm_resume_defaultPara();
    }

    comm_fm_update();

    i = 0;
    addr = EXTRA_PARA_STORE_ADDRESS;

    do
    {
        fmc_read_8bit_data(addr, sizeof(CalibTypeDef), (int8_t*)&caliData.imuSelect);

        if(caliData.imuSelect == 0xff)
            break;

        addr += EXTRA_PARA_STORE_ADDRESS_OFFSET;
        i++;
	 if(addr>EXTRA_FMC_SAVE_SIZE_THRESHOLD)
	 	break;
    }
    while(1);

    imu_type_save_addr = addr;

    if(i)
        addr -= EXTRA_PARA_STORE_ADDRESS_OFFSET;

    fmc_read_8bit_data(addr, sizeof(CalibTypeDef), (int8_t*)&caliData.imuSelect);
    hash = hash32((uint8_t*)&caliData.imuSelect, sizeof(CalibTypeDef) - 4);

    if(caliData.hash != hash)//校验判断有效性
    {
        fmc_erase_sector_by_address(EXTRA_PARA_STORE_BASE_ADDRESS);
        memset((void*)&caliData.imuSelect, 0x0, sizeof(CalibTypeDef));
        comm_saveCaliData();
    }

    if(0xff == caliData.imuSelect)caliData.imuSelect = 0;

    //combineData.imuSelect = caliData.imuSelect;
    comm_nav_para_syn();//配置参数同步
    adj_paraSyn();//标定参数同步
    //设置波特率
#if  configProtocolUse == ProtocolCfg_iPMV
    Uart_TxInit(UART_TXPORT_COMPLEX_8, UART_BAUDRATE_230400BPS, UART_PARITY_NONE, UART_STOPBIT_ONE, UART_RS422, UART_ENABLE);
    Uart_RxInit(UART_RXPORT_COMPLEX_8, UART_BAUDRATE_230400BPS, UART_PARITY_NONE, UART_STOPBIT_ONE, UART_RS422, UART_ENABLE);
#else
	Uart_TxInit(UART_TXPORT_COMPLEX_8, UART_BAUDRATE_460800BPS, UART_PARITY_NONE, UART_STOPBIT_ONE, UART_RS422, UART_ENABLE);
    Uart_RxInit(UART_RXPORT_COMPLEX_8, UART_BAUDRATE_460800BPS, UART_PARITY_NONE, UART_STOPBIT_ONE, UART_RS422, UART_ENABLE);
#endif
//    baseline_synTimerInstall();
//    protocolTimerInstall();
}


