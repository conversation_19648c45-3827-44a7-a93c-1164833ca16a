//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：INS_App_Init.c
// 文件标识：
// 文件摘要：应用层初始化，整合HPM6750项目的功能
//
// 当前版本：V1.0
// 作    者：迁移自HPM6750项目
// 完成时间：2024.12.19
//---------------------------------------------------------

#include "INS_App_Init.h"
#include "bsp_flash.h"
#include "bsp_sd_fatfs.h"
#include "SetParaBao.h"
#include "InsTestingEntry_Compat.h"
#include "bsp_uart.h"
#include "bsp_gpio.h"
#include "bsp_tim.h"

// 应用层初始化状态
static uint8_t app_init_status = 0;

/**
 * @brief 应用层完整初始化
 * @return 0: 成功, -1: 失败
 */
int INS_App_Init(void)
{
    int ret = 0;
    
    // 1. Flash操作初始化
    ret = norflash_init();
    if (ret != 0) {
        return -1;
    }
    
    // 2. SD卡文件系统初始化
    ret = Fatfs_Init();
    if (ret != 0) {
        // SD卡初始化失败不影响系统运行，只记录状态
        // return -1;
    }
    
    // 3. 参数配置初始化
    SetParaInit();
    
    // 4. 串口通信初始化
    INS_UART_Init();
    
    // 5. 导航算法相关初始化
    INS_Algorithm_Init();
    
    // 6. 协议处理初始化
    INS_Protocol_Init();
    
    app_init_status = 1;
    return 0;
}

/**
 * @brief 串口通信初始化
 */
void INS_UART_Init(void)
{
    // 初始化UART4用于数据输出
    // 波特率根据参数设置
    uint32_t baudrate = stSetPara.Setbaud * 100;  // 参数中存储的是百倍值
    
    // 这里需要调用具体的UART初始化函数
    // 例如：uart_init(UART4, baudrate);
    
    // 初始化其他串口（如果需要）
    // uart_init(UART3, 19200);  // 用于参数配置
}

/**
 * @brief 导航算法初始化
 */
void INS_Algorithm_Init(void)
{
    // 初始化导航输出数据结构
    memset(&gnavout, 0, sizeof(navoutdata_t));
    
    // 初始化FPGA数据缓冲区
    memset(gfpgadata, 0, sizeof(gfpgadata));
    
    // 初始化系统变量
    g_SysVar.WorkPhase = 1;  // 设置为工作状态
    
    // 重置计数器
    fpga_syn_count = 0;
    fpga_loop_count = 0;
}

/**
 * @brief 协议处理初始化
 */
void INS_Protocol_Init(void)
{
    // 初始化数据发送结构体
    memset(&gfpgadataSend, 0, sizeof(gfpgadataSend));
    memset(&gfpgadataPredoSend, 0, sizeof(gfpgadataPredoSend));
    
    // 初始化升级相关变量
    g_UpdateBackFlag = 0;
    g_StartUpdateFirm = 0;
}

/**
 * @brief 应用层主循环处理
 */
void INS_App_MainLoop(void)
{
    static uint8_t fpga_syn = 0;  // FPGA同步标志
    
    // 模拟FPGA数据同步信号
    // 在实际应用中，这个信号应该来自外部中断或定时器
    static uint32_t sync_counter = 0;
    sync_counter++;
    if (sync_counter >= 1000) {  // 模拟200Hz的数据率
        sync_counter = 0;
        fpga_syn = 1;
    }
    
    if (fpga_syn) {
        fpga_syn = 0;
        
        // 1. SD卡读取操作
        SdFileReadOperate();
        
        // 2. 获取FPGA数据
        get_fpgadata();
        
        // 3. 算法处理
        AlgorithmDo();
        
        // 4. 数据输出
        INS912_Output(&gnavout);
        
        // 5. LED控制（可选）
        static uint32_t led_count = 0;
        led_count++;
        if (led_count >= 20) {
            led_count = 0;
            // Led_Control();
        }
    }
    
    // 循环中的其他处理
    loopDoOther();
    
    // 接收数据分析（参数设置和升级）
    analysisRxdata();
    
    // SD卡写操作
    SdFileWriteOperate();
    
    // 队列处理
    ff_handle_poll();
}

/**
 * @brief 获取应用层初始化状态
 * @return 初始化状态 (0: 未初始化, 1: 已初始化)
 */
uint8_t INS_App_GetInitStatus(void)
{
    return app_init_status;
}

/**
 * @brief 应用层软件复位
 */
void INS_App_SoftReset(void)
{
    // 保存重要参数
    SetParaSave();
    
    // 关闭所有文件
    CloseFileToSd(SD_FILE_TYPE_ALL);
    
    // 延时
    for (volatile int i = 0; i < 1000000; i++);
    
    // 系统复位
    Drv_SystemReset();
}

/**
 * @brief 应用层错误处理
 * @param error_code 错误代码
 */
void INS_App_ErrorHandler(uint32_t error_code)
{
    // 根据错误代码进行相应处理
    switch (error_code) {
        case 0x01:  // Flash错误
            // 重新初始化Flash
            norflash_init();
            break;
            
        case 0x02:  // SD卡错误
            // 重新初始化SD卡
            Fatfs_Init();
            break;
            
        case 0x03:  // 串口错误
            // 重新初始化串口
            INS_UART_Init();
            break;
            
        case 0x04:  // 算法错误
            // 重新初始化算法
            INS_Algorithm_Init();
            break;
            
        default:
            // 未知错误，执行软复位
            INS_App_SoftReset();
            break;
    }
}

/**
 * @brief 应用层状态监控
 */
void INS_App_StatusMonitor(void)
{
    static uint32_t monitor_count = 0;
    monitor_count++;
    
    // 每秒检查一次
    if (monitor_count >= 200) {  // 假设200Hz调用频率
        monitor_count = 0;
        
        // 检查SD卡状态
        uint8_t sd_status = sd_card_status_get();
        if (sd_status != SD_STATUS_OK) {
            // SD卡异常处理
        }
        
        // 检查升级状态
        if (g_StartUpdateFirm) {
            // 升级过程中的状态监控
        }
        
        // 检查数据输出状态
        static uint32_t last_fpga_count = 0;
        if (fpga_syn_count == last_fpga_count) {
            // 数据停止更新，可能有问题
        }
        last_fpga_count = fpga_syn_count;
    }
}

/**
 * @brief 坐标系转换初始化
 */
void INS_CoordinateTransform_Init(void)
{
    // 初始化坐标系转换参数
    // 这里可以添加坐标系转换相关的初始化代码
    
    // 例如：设置坐标系转换矩阵、偏移量等
}

/**
 * @brief 上位机通信初始化
 */
void INS_HostComm_Init(void)
{
    // 初始化与上位机的通信
    // 设置通信协议、数据格式等
    
    // 初始化通信缓冲区
    // 设置通信参数
}

/**
 * @brief 输入输出协议初始化
 */
void INS_IOProtocol_Init(void)
{
    // 初始化输入输出协议
    // 设置数据包格式、校验方式等
    
    // 初始化协议状态机
    // 设置协议参数
}
