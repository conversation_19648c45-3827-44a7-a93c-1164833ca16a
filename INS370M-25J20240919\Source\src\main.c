/*!
    \file  	main.c
    \brief 	ins912-3a project (Enhanced with HPM6750 features)
	\author	Bill / Enhanced by Migration
	\data	2023/10/27 / 2024/12/19
*/
#include "appmain.h"
#include "ins.h"
#include "DATASTRUCT.h"
#include "FUNCTION.h"
#include "GLOBALDATA.h"
#include "insTestingEntry.h"
#include "datado.h"

// 新增的应用层功能头文件
#include "INS_App_Init.h"
#include "SetParaBao.h"
#include "bsp_sd_fatfs.h"

// 外部变量声明（兼容原有代码）
extern uint8_t fpga_syn;

int main(void)
{
	// 原有系统初始化
	SysInit();				// Init inertial navigation device...

	// 新增应用层初始化
	if (INS_App_Init() != 0) {
		// 应用层初始化失败处理
		while(1) {
			// 错误指示，可以添加LED闪烁等
		}
	}

	SysInit_Over();		//系统初始化完成后，进入循环前的准备

	while(1)
	{
		// 使用新的应用层主循环处理
		INS_App_MainLoop();

		// 保留原有的FPGA同步处理逻辑（作为备用）
		if(fpga_syn)
		{	//每一帧FPGA数据产生，处理
			fpga_syn = 0;

			get_fpgadata_enhanced();				//1、获取当前帧FPGA数据，及相关（增强版）
			AlgorithmDo_enhanced();					//2、对获取的数据进行算法处理（增强版）
			INS912_Output_enhanced(&gnavout);		//4、算法处理完成的数据，进行打包、发送处理（增强版）
		}

		loopDoOther_enhanced();						//循环中，处理其它事宜（增强版）

		analysisRxdata_enhanced();					//分析接收数据（参数设置和升级）（增强版）

		// 新增的状态监控
		INS_App_StatusMonitor();
	}
}



