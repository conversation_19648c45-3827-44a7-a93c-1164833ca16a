/*******************************************************************************/
/*  This file is auto-generated by Azure RTOS GUIX Studio. Do not edit this    */
/*  file by hand. Modifications to this file should only be made by running    */
/*  the Azure RTOS GUIX Studio application and re-generating the application   */
/*  resource file(s). For more information please refer to the Azure RTOS GUIX */
/*  Studio User Guide, or visit our web site at azure.com/rtos                 */
/*                                                                             */
/*  GUIX Studio Revision 6.1.0.0                                               */
/*  Date (dd.mm.yyyy): 15. 8.2023   Time (hh:mm): 18:57                        */
/*******************************************************************************/


#ifndef _DEMO_GUIX_INDUSTRIAL_MAIN_DISPLAY_RESOURCES_H_
#define _DEMO_GUIX_INDUSTRIAL_MAIN_DISPLAY_RESOURCES_H_

#include "gx_api.h"
#include "board.h"

/* Display and theme definitions                                               */

#define MAIN_DISPLAY 0
#define MAIN_DISPLAY_COLOR_FORMAT GX_COLOR_FORMAT_32ARGB
#define MAIN_DISPLAY_X_RESOLUTION BOARD_LCD_WIDTH
#define MAIN_DISPLAY_Y_RESOLUTION BOARD_LCD_HEIGHT
#define MAIN_DISPLAY_THEME_1 0
#define MAIN_DISPLAY_THEME_TABLE_SIZE 1

/* Language definitions                                                        */

#define LANGUAGE_ENGLISH 0
#define MAIN_DISPLAY_LANGUAGE_TABLE_SIZE 1

/* Color ID definitions                                                        */

#define GX_COLOR_ID_WHITE 29
#define GX_COLOR_ID_BLACK 30
#define GX_COLOR_ID_GRAY 31
#define GX_COLOR_ID_DARK_GRAY 32
#define GX_COLOR_ID_LIGHT_GRAY 33
#define GX_COLOR_ID_PROGRESS_BG 34
#define GX_COLOR_ID_GREEN 35
#define GX_COLOR_ID_RADIAL_PROGRESS_BAR_FILL 36
#define GX_COLOR_ID_LIGHT_GREEN 37
#define MAIN_DISPLAY_COLOR_TABLE_SIZE 38

/* Font ID definitions                                                         */

#define GX_FONT_ID_NORMAL 4
#define GX_FONT_ID_BIG 5
#define GX_FONT_ID_MIDDLE 6
#define GX_FONT_ID_SMALL 7
#define GX_FONT_ID_MIDDLE_45 8
#define GX_FONT_ID_MIDDLE_30 9
#define GX_FONT_ID_BOLD 10
#define MAIN_DISPLAY_FONT_TABLE_SIZE 11

/* Pixelmap ID definitions                                                     */

#define GX_PIXELMAP_ID_BG_METAL_DARK 5
#define GX_PIXELMAP_ID_BIG_BUTTON 6
#define GX_PIXELMAP_ID_BIG_BUTTON_PUSHED 7
#define GX_PIXELMAP_ID_CIRCLE_ICON_ASSEMBLING 8
#define GX_PIXELMAP_ID_CIRCLE_ICON_INSPECTING 9
#define GX_PIXELMAP_ID_CIRCLE_ICON_PALLET 10
#define GX_PIXELMAP_ID_CIRCLE_ICON_WELDING 11
#define GX_PIXELMAP_ID_CIRCLE_SEQUENCE 12
#define GX_PIXELMAP_ID_ICON_ASSEMBLING 13
#define GX_PIXELMAP_ID_ICON_INSPECT 14
#define GX_PIXELMAP_ID_ICON_PALLET 15
#define GX_PIXELMAP_ID_ICON_POWER 16
#define GX_PIXELMAP_ID_ICON_SAFE_GUARD 17
#define GX_PIXELMAP_ID_ICON_THUMB_UP 18
#define GX_PIXELMAP_ID_ICON_TIMER 19
#define GX_PIXELMAP_ID_ICON_WELDING 20
#define GX_PIXELMAP_ID_INSPECTING_00 21
#define GX_PIXELMAP_ID_INSPECTING_01 22
#define GX_PIXELMAP_ID_INSPECTING_02 23
#define GX_PIXELMAP_ID_INSPECTING_03 24
#define GX_PIXELMAP_ID_INSPECTING_04 25
#define GX_PIXELMAP_ID_INSPECTING_05 26
#define GX_PIXELMAP_ID_INSPECTING_06 27
#define GX_PIXELMAP_ID_INSPECTING_07 28
#define GX_PIXELMAP_ID_INSPECTING_08 29
#define GX_PIXELMAP_ID_INSPECTING_09 30
#define GX_PIXELMAP_ID_INSPECTING_10 31
#define GX_PIXELMAP_ID_INSPECTING_11 32
#define GX_PIXELMAP_ID_INSPECTING_12 33
#define GX_PIXELMAP_ID_INSPECTING_13 34
#define GX_PIXELMAP_ID_INSPECTING_14 35
#define GX_PIXELMAP_ID_INSPECTING_15 36
#define GX_PIXELMAP_ID_INSPECTING_16 37
#define GX_PIXELMAP_ID_INSPECTING_17 38
#define GX_PIXELMAP_ID_INSPECTING_18 39
#define GX_PIXELMAP_ID_INSPECTING_19 40
#define GX_PIXELMAP_ID_INSPECTING_20 41
#define GX_PIXELMAP_ID_INSPECTING_21 42
#define GX_PIXELMAP_ID_INSPECTING_22 43
#define GX_PIXELMAP_ID_INSPECTING_23 44
#define GX_PIXELMAP_ID_LEFT_SIDE_CHECK_MARK 45
#define GX_PIXELMAP_ID_MENU_BUTTON_C 46
#define GX_PIXELMAP_ID_MENU_BUTTON_C_PUSHED 47
#define GX_PIXELMAP_ID_MENU_BUTTON_LONG 48
#define GX_PIXELMAP_ID_MENU_BUTTON_LONG_PUSHED 49
#define GX_PIXELMAP_ID_MICROSOFT_AZURE_LOGO 50
#define GX_PIXELMAP_ID_OUTLINED_BUTTON_HOLDER 51
#define GX_PIXELMAP_ID_OUTLINED_BUTTON_HOLDER_PUSHED 52
#define GX_PIXELMAP_ID_OUTLINED_BUTTON_INDICATOR 53
#define GX_PIXELMAP_ID_PROGRESS_BAR_BG 54
#define GX_PIXELMAP_ID_ROBOT_BG 55
#define GX_PIXELMAP_ID_ROBOT_UP 56
#define GX_PIXELMAP_ID_SEQUENCE_BIG_PROGRESS_ACTIVE 57
#define GX_PIXELMAP_ID_VERTICAL_DIVIDER_CONFIRMATION 58
#define MAIN_DISPLAY_PIXELMAP_TABLE_SIZE 59

/* String Ids                                                                  */

#define GX_STRING_ID_STRING_2 1
#define GX_STRING_ID_STRING_3 2
#define GX_STRING_ID_STRING_10 3
#define GX_STRING_ID_STRING_12 4
#define GX_STRING_ID_STRING_13 5
#define GX_STRING_ID_STRING_14 6
#define GX_STRING_ID_STRING_15 7
#define GX_STRING_ID_STRING_16 8
#define GX_STRING_ID_STRING_17 9
#define GX_STRING_ID_STRING_18 10
#define GX_STRING_ID_STRING_19 11
#define GX_STRING_ID_STRING_20 12
#define GX_STRING_ID_STRING_21 13
#define GX_STRING_ID_INSPECTING 14
#define GX_STRING_ID_ASSEMBLING 15
#define GX_STRING_ID_WELDING 16
#define GX_STRING_ID_PALLETIZING 17
#define GX_STRING_ID_TURN_ON 18
#define GX_STRING_ID_LEVEL_SURFACE 19
#define GX_STRING_ID_ARM_AUXILIARY 20
#define GX_STRING_ID_PALLET_FRICTION 21
#define GX_STRING_ID_UNITSET 22
#define GX_STRING_ID_STRING_35 23
#define GX_STRING_ID_STRING_36 24
#define GX_STRING_ID_STRING_37 25
#define GX_STRING_ID_STRING_44 26
#define GX_STRING_ID_STRING_45 27
#define GX_STRING_ID_STRING_47 28
#define GX_STRING_ID_STRING_50 29
#define GX_STRING_ID_STRING_52 30
#define GX_STRING_ID_STRING_56 31
#define GX_STRING_ID_STRING_57 32
#define GX_STRING_ID_STRING_58 33
#define GX_STRING_ID_STRING_59 34
#define GX_STRING_ID_STRING_60 35
#define GX_STRING_ID_STRING_63 36
#define GX_STRING_ID_STRING_64 37
#define GX_STRING_ID_STRING_65 38
#define GX_STRING_ID_STRING_66 39
#define GX_STRING_ID_STRING_67 40
#define GX_STRING_ID_STRING_69 41
#define GX_STRING_ID_STRING_27 42
#define GX_STRING_ID_STRING_39 43
#define GX_STRING_ID_STRING_1 44
#define GX_STRING_ID_TURN_OFF 45
#define GX_STRING_ID_STRING_26 46
#define GX_STRING_ID_STRING_28 47
#define GX_STRING_ID_STRING_30 48
#define GX_STRING_ID_SURFACE 49
#define GX_STRING_ID_COMPONENTS 50
#define GX_STRING_ID_TRACING_PATTERN 51
#define GX_STRING_ID_CIRCUIT_TEST 52
#define GX_STRING_ID_INITIALIZE_KEYS 53
#define GX_STRING_ID_ROTATE_AXIS 54
#define GX_STRING_ID_DRIVE_PRESS 55
#define GX_STRING_ID_FORCE_JOIN 56
#define GX_STRING_ID_CURRENT_CHECK 57
#define GX_STRING_ID_TARGET_CLAMPS 58
#define GX_STRING_ID_BRAZING_FLUX 59
#define GX_STRING_ID_GROUND_FLUSH 60
#define GX_STRING_ID_STRING_4 61
#define GX_STRING_ID_STRING_5 62
#define MAIN_DISPLAY_STRING_TABLE_SIZE 63

#endif                                       /* sentry                         */
