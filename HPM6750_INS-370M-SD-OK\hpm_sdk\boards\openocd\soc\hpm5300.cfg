# Copyright (c) 2021 HPMicro
# SPDX-License-Identifier: BSD-3-Clause

set _CHIP hpm5361
set _CPUTAPID 0x1000563D
jtag newtap $_CHIP cpu -irlen 5 -expected-id $_CPUTAPID

set _TARGET0 $_CHIP.cpu0
target create $_TARGET0 riscv -chain-position $_CHIP.cpu -coreid 0

$_TARGET0 configure -work-area-phys 0x00000000 -work-area-size 0x20000 -work-area-backup 0

targets $_TARGET0

proc reset_soc {} {
    $::_TARGET0 riscv dmi_write 0x39 0xF410001C
    $::_TARGET0 riscv dmi_write 0x3C 24000000
}
