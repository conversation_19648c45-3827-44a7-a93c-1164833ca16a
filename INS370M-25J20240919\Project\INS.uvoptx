<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<ProjectOpt xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_optx.xsd">

  <SchemaVersion>1.0</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Extensions>
    <cExt>*.c</cExt>
    <aExt>*.s*; *.src; *.a*</aExt>
    <oExt>*.obj; *.o</oExt>
    <lExt>*.lib</lExt>
    <tExt>*.txt; *.h; *.inc; *.md</tExt>
    <pExt>*.plm</pExt>
    <CppX>*.cpp</CppX>
    <nMigrate>0</nMigrate>
  </Extensions>

  <DaveTm>
    <dwLowDateTime>0</dwLowDateTime>
    <dwHighDateTime>0</dwHighDateTime>
  </DaveTm>

  <Target>
    <TargetName>INS</TargetName>
    <ToolsetNumber>0x4</ToolsetNumber>
    <ToolsetName>ARM-ADS</ToolsetName>
    <TargetOption>
      <CLKADS>25000000</CLKADS>
      <OPTTT>
        <gFlags>1</gFlags>
        <BeepAtEnd>1</BeepAtEnd>
        <RunSim>0</RunSim>
        <RunTarget>1</RunTarget>
        <RunAbUc>0</RunAbUc>
      </OPTTT>
      <OPTHX>
        <HexSelection>1</HexSelection>
        <FlashByte>65535</FlashByte>
        <HexRangeLowAddress>0</HexRangeLowAddress>
        <HexRangeHighAddress>0</HexRangeHighAddress>
        <HexOffset>0</HexOffset>
      </OPTHX>
      <OPTLEX>
        <PageWidth>79</PageWidth>
        <PageLength>66</PageLength>
        <TabStop>8</TabStop>
        <ListingPath>.\Listings\</ListingPath>
      </OPTLEX>
      <ListingPage>
        <CreateCListing>1</CreateCListing>
        <CreateAListing>1</CreateAListing>
        <CreateLListing>1</CreateLListing>
        <CreateIListing>0</CreateIListing>
        <AsmCond>1</AsmCond>
        <AsmSymb>1</AsmSymb>
        <AsmXref>0</AsmXref>
        <CCond>1</CCond>
        <CCode>0</CCode>
        <CListInc>0</CListInc>
        <CSymb>0</CSymb>
        <LinkerCodeListing>0</LinkerCodeListing>
      </ListingPage>
      <OPTXL>
        <LMap>1</LMap>
        <LComments>1</LComments>
        <LGenerateSymbols>1</LGenerateSymbols>
        <LLibSym>1</LLibSym>
        <LLines>1</LLines>
        <LLocSym>1</LLocSym>
        <LPubSym>1</LPubSym>
        <LXref>0</LXref>
        <LExpSel>0</LExpSel>
      </OPTXL>
      <OPTFL>
        <tvExp>1</tvExp>
        <tvExpOptDlg>0</tvExpOptDlg>
        <IsCurrentTarget>0</IsCurrentTarget>
      </OPTFL>
      <CpuCode>255</CpuCode>
      <DebugOpt>
        <uSim>0</uSim>
        <uTrg>1</uTrg>
        <sLdApp>1</sLdApp>
        <sGomain>1</sGomain>
        <sRbreak>1</sRbreak>
        <sRwatch>1</sRwatch>
        <sRmem>1</sRmem>
        <sRfunc>1</sRfunc>
        <sRbox>1</sRbox>
        <tLdApp>1</tLdApp>
        <tGomain>1</tGomain>
        <tRbreak>1</tRbreak>
        <tRwatch>1</tRwatch>
        <tRmem>1</tRmem>
        <tRfunc>0</tRfunc>
        <tRbox>1</tRbox>
        <tRtrace>1</tRtrace>
        <sRSysVw>1</sRSysVw>
        <tRSysVw>1</tRSysVw>
        <sRunDeb>0</sRunDeb>
        <sLrtime>0</sLrtime>
        <bEvRecOn>1</bEvRecOn>
        <bSchkAxf>0</bSchkAxf>
        <bTchkAxf>0</bTchkAxf>
        <nTsel>4</nTsel>
        <sDll></sDll>
        <sDllPa></sDllPa>
        <sDlgDll></sDlgDll>
        <sDlgPa></sDlgPa>
        <sIfile></sIfile>
        <tDll></tDll>
        <tDllPa></tDllPa>
        <tDlgDll></tDlgDll>
        <tDlgPa></tDlgPa>
        <tIfile></tIfile>
        <pMon>Segger\JL2CM3.dll</pMon>
      </DebugOpt>
      <TargetDriverDllRegistry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGUARM</Key>
          <Name></Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>ARMRTXEVENTFLAGS</Key>
          <Name>-L70 -Z18 -C0 -M0 -T1</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGTARM</Key>
          <Name>(1010=830,83,1280,640,0)(1007=-1,-1,-1,-1,0)(1008=-1,-1,-1,-1,0)(1009=-1,-1,-1,-1,0)(1012=-1,-1,-1,-1,0)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>ARMDBGFLAGS</Key>
          <Name></Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>JL2CM3</Key>
          <Name>-********* -O78 -S4 -ZTIFSpeedSel2000 -A0 -C0 -JU1 -JI127.0.0.1 -JP0 -RST0 -N00("ARM CoreSight SW-DP") -D00(2BA01477) -L00(0) -TO18 -********** -TP21 -TDS8001 -TDT0 -TDC1F -TIEFFFFFFFF -TIP8 -TB1 -TFE0 -FO31 -********** -FC1000 -FN1 -FF0GD32F4xx_2MB.FLM -********** -********* -FP0($$Device:GD32F470II$Flash\GD32F4xx_2MB.FLM)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>UL2CM3</Key>
          <Name>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0GD32F4xx_2MB -********** -********* -FP0($$Device:GD32F470II$Flash\GD32F4xx_2MB.FLM))</Name>
        </SetRegEntry>
      </TargetDriverDllRegistry>
      <Breakpoint/>
      <WatchWindow1>
        <Ww>
          <count>0</count>
          <WinNumber>1</WinNumber>
          <ItemText>gpagedata</ItemText>
        </Ww>
        <Ww>
          <count>1</count>
          <WinNumber>1</WinNumber>
          <ItemText>combineData.gnssInfo</ItemText>
        </Ww>
        <Ww>
          <count>2</count>
          <WinNumber>1</WinNumber>
          <ItemText>gframeParsebufTxt</ItemText>
        </Ww>
        <Ww>
          <count>3</count>
          <WinNumber>1</WinNumber>
          <ItemText>gframeParsebufTxt</ItemText>
        </Ww>
        <Ww>
          <count>4</count>
          <WinNumber>1</WinNumber>
          <ItemText>hSetting</ItemText>
        </Ww>
        <Ww>
          <count>5</count>
          <WinNumber>1</WinNumber>
          <ItemText>g_FlashBuf</ItemText>
        </Ww>
        <Ww>
          <count>6</count>
          <WinNumber>1</WinNumber>
          <ItemText>g_FlashBuf</ItemText>
        </Ww>
        <Ww>
          <count>7</count>
          <WinNumber>1</WinNumber>
          <ItemText>&amp;g_FlashBuf[0x400]</ItemText>
        </Ww>
        <Ww>
          <count>8</count>
          <WinNumber>1</WinNumber>
          <ItemText>hSetting</ItemText>
        </Ww>
        <Ww>
          <count>9</count>
          <WinNumber>1</WinNumber>
          <ItemText>caliData</ItemText>
        </Ww>
        <Ww>
          <count>10</count>
          <WinNumber>1</WinNumber>
          <ItemText>hSetting</ItemText>
        </Ww>
        <Ww>
          <count>11</count>
          <WinNumber>1</WinNumber>
          <ItemText>NAV_Data_Full_p-&gt;Param.gyro_off</ItemText>
        </Ww>
        <Ww>
          <count>12</count>
          <WinNumber>1</WinNumber>
          <ItemText>receive_message -&gt; rx_data</ItemText>
        </Ww>
        <Ww>
          <count>13</count>
          <WinNumber>1</WinNumber>
          <ItemText>hCAN0.CanRxBuf</ItemText>
        </Ww>
        <Ww>
          <count>14</count>
          <WinNumber>1</WinNumber>
          <ItemText>hINSData.CAN_Data</ItemText>
        </Ww>
        <Ww>
          <count>15</count>
          <WinNumber>1</WinNumber>
          <ItemText>receive_message,0x0A</ItemText>
        </Ww>
      </WatchWindow1>
      <MemoryWindow1>
        <Mm>
          <WinNumber>1</WinNumber>
          <SubType>0</SubType>
          <ItemText>buff</ItemText>
          <AccSizeX>0</AccSizeX>
        </Mm>
      </MemoryWindow1>
      <MemoryWindow2>
        <Mm>
          <WinNumber>2</WinNumber>
          <SubType>0</SubType>
          <ItemText>&amp;g_FlashBuf[0x200]</ItemText>
          <AccSizeX>0</AccSizeX>
        </Mm>
      </MemoryWindow2>
      <MemoryWindow3>
        <Mm>
          <WinNumber>3</WinNumber>
          <SubType>0</SubType>
          <ItemText>buff</ItemText>
          <AccSizeX>0</AccSizeX>
        </Mm>
      </MemoryWindow3>
      <Tracepoint>
        <THDelay>0</THDelay>
      </Tracepoint>
      <DebugFlag>
        <trace>0</trace>
        <periodic>1</periodic>
        <aLwin>0</aLwin>
        <aCover>0</aCover>
        <aSer1>0</aSer1>
        <aSer2>0</aSer2>
        <aPa>0</aPa>
        <viewmode>1</viewmode>
        <vrSel>0</vrSel>
        <aSym>0</aSym>
        <aTbox>0</aTbox>
        <AscS1>0</AscS1>
        <AscS2>0</AscS2>
        <AscS3>0</AscS3>
        <aSer3>0</aSer3>
        <eProf>0</eProf>
        <aLa>0</aLa>
        <aPa1>0</aPa1>
        <AscS4>0</AscS4>
        <aSer4>0</aSer4>
        <StkLoc>0</StkLoc>
        <TrcWin>0</TrcWin>
        <newCpu>0</newCpu>
        <uProt>0</uProt>
      </DebugFlag>
      <LintExecutable></LintExecutable>
      <LintConfigFile></LintConfigFile>
      <bLintAuto>0</bLintAuto>
      <bAutoGenD>0</bAutoGenD>
      <LntExFlags>0</LntExFlags>
      <pMisraName></pMisraName>
      <pszMrule></pszMrule>
      <pSingCmds></pSingCmds>
      <pMultCmds></pMultCmds>
      <pMisraNamep></pMisraNamep>
      <pszMrulep></pszMrulep>
      <pSingCmdsp></pSingCmdsp>
      <pMultCmdsp></pMultCmdsp>
    </TargetOption>
  </Target>

  <Target>
    <TargetName>INS_4000</TargetName>
    <ToolsetNumber>0x4</ToolsetNumber>
    <ToolsetName>ARM-ADS</ToolsetName>
    <TargetOption>
      <CLKADS>12000000</CLKADS>
      <OPTTT>
        <gFlags>1</gFlags>
        <BeepAtEnd>1</BeepAtEnd>
        <RunSim>0</RunSim>
        <RunTarget>1</RunTarget>
        <RunAbUc>0</RunAbUc>
      </OPTTT>
      <OPTHX>
        <HexSelection>1</HexSelection>
        <FlashByte>65535</FlashByte>
        <HexRangeLowAddress>0</HexRangeLowAddress>
        <HexRangeHighAddress>0</HexRangeHighAddress>
        <HexOffset>0</HexOffset>
      </OPTHX>
      <OPTLEX>
        <PageWidth>79</PageWidth>
        <PageLength>66</PageLength>
        <TabStop>8</TabStop>
        <ListingPath>.\Listings\</ListingPath>
      </OPTLEX>
      <ListingPage>
        <CreateCListing>1</CreateCListing>
        <CreateAListing>1</CreateAListing>
        <CreateLListing>1</CreateLListing>
        <CreateIListing>0</CreateIListing>
        <AsmCond>1</AsmCond>
        <AsmSymb>1</AsmSymb>
        <AsmXref>0</AsmXref>
        <CCond>1</CCond>
        <CCode>0</CCode>
        <CListInc>0</CListInc>
        <CSymb>0</CSymb>
        <LinkerCodeListing>0</LinkerCodeListing>
      </ListingPage>
      <OPTXL>
        <LMap>1</LMap>
        <LComments>1</LComments>
        <LGenerateSymbols>1</LGenerateSymbols>
        <LLibSym>1</LLibSym>
        <LLines>1</LLines>
        <LLocSym>1</LLocSym>
        <LPubSym>1</LPubSym>
        <LXref>0</LXref>
        <LExpSel>0</LExpSel>
      </OPTXL>
      <OPTFL>
        <tvExp>1</tvExp>
        <tvExpOptDlg>0</tvExpOptDlg>
        <IsCurrentTarget>1</IsCurrentTarget>
      </OPTFL>
      <CpuCode>255</CpuCode>
      <DebugOpt>
        <uSim>0</uSim>
        <uTrg>1</uTrg>
        <sLdApp>1</sLdApp>
        <sGomain>1</sGomain>
        <sRbreak>1</sRbreak>
        <sRwatch>1</sRwatch>
        <sRmem>1</sRmem>
        <sRfunc>1</sRfunc>
        <sRbox>1</sRbox>
        <tLdApp>1</tLdApp>
        <tGomain>1</tGomain>
        <tRbreak>1</tRbreak>
        <tRwatch>1</tRwatch>
        <tRmem>1</tRmem>
        <tRfunc>0</tRfunc>
        <tRbox>1</tRbox>
        <tRtrace>1</tRtrace>
        <sRSysVw>1</sRSysVw>
        <tRSysVw>1</tRSysVw>
        <sRunDeb>0</sRunDeb>
        <sLrtime>0</sLrtime>
        <bEvRecOn>1</bEvRecOn>
        <bSchkAxf>0</bSchkAxf>
        <bTchkAxf>0</bTchkAxf>
        <nTsel>4</nTsel>
        <sDll></sDll>
        <sDllPa></sDllPa>
        <sDlgDll></sDlgDll>
        <sDlgPa></sDlgPa>
        <sIfile></sIfile>
        <tDll></tDll>
        <tDllPa></tDllPa>
        <tDlgDll></tDlgDll>
        <tDlgPa></tDlgPa>
        <tIfile></tIfile>
        <pMon>Segger\JL2CM3.dll</pMon>
      </DebugOpt>
      <TargetDriverDllRegistry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGUARM</Key>
          <Name></Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>ARMRTXEVENTFLAGS</Key>
          <Name>-L70 -Z18 -C0 -M0 -T1</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGTARM</Key>
          <Name>(1010=830,83,1280,640,0)(1007=-1,-1,-1,-1,0)(1008=-1,-1,-1,-1,0)(1009=-1,-1,-1,-1,0)(1012=-1,-1,-1,-1,0)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>ARMDBGFLAGS</Key>
          <Name></Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>JL2CM3</Key>
          <Name>-U601012352 -O78 -S4 -ZTIFSpeedSel2000 -A0 -C0 -JU1 -JI127.0.0.1 -JP0 -RST0 -N00("ARM CoreSight SW-DP") -D00(2BA01477) -L00(0) -TO18 -********** -TP21 -TDS8001 -TDT0 -TDC1F -TIEFFFFFFFF -TIP8 -TB1 -TFE0 -FO15 -********** -FC1000 -FN1 -FF0GD32F4xx_2MB.FLM -********** -********* -FP0($$Device:GD32F470II$Flash\GD32F4xx_2MB.FLM)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>UL2CM3</Key>
          <Name>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0GD32F4xx_2MB -********** -********* -FP0($$Device:GD32F470II$Flash\GD32F4xx_2MB.FLM))</Name>
        </SetRegEntry>
      </TargetDriverDllRegistry>
      <Breakpoint/>
      <WatchWindow1>
        <Ww>
          <count>0</count>
          <WinNumber>1</WinNumber>
          <ItemText>gins912data,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>1</count>
          <WinNumber>1</WinNumber>
          <ItemText>result,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>2</count>
          <WinNumber>1</WinNumber>
          <ItemText>adlxdata</ItemText>
        </Ww>
        <Ww>
          <count>3</count>
          <WinNumber>1</WinNumber>
          <ItemText>paochedata</ItemText>
        </Ww>
        <Ww>
          <count>4</count>
          <WinNumber>1</WinNumber>
          <ItemText>gfpgadata,0x10</ItemText>
        </Ww>
        <Ww>
          <count>5</count>
          <WinNumber>1</WinNumber>
          <ItemText>gpagedata,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>6</count>
          <WinNumber>1</WinNumber>
          <ItemText>ahrs_k</ItemText>
        </Ww>
        <Ww>
          <count>7</count>
          <WinNumber>1</WinNumber>
          <ItemText>ins_state</ItemText>
        </Ww>
        <Ww>
          <count>8</count>
          <WinNumber>1</WinNumber>
          <ItemText>gnss_vel_delayed</ItemText>
        </Ww>
        <Ww>
          <count>9</count>
          <WinNumber>1</WinNumber>
          <ItemText>START_TIME</ItemText>
        </Ww>
        <Ww>
          <count>10</count>
          <WinNumber>1</WinNumber>
          <ItemText>\\arm2\../Protocol/InsTestingEntry.c\gins912data.fogtemperaturex</ItemText>
        </Ww>
        <Ww>
          <count>11</count>
          <WinNumber>1</WinNumber>
          <ItemText>\\arm2\../Protocol/InsTestingEntry.c\gins912data.accelerometerx</ItemText>
        </Ww>
        <Ww>
          <count>12</count>
          <WinNumber>1</WinNumber>
          <ItemText>\\arm2\../INAV/ins.c\paochedata.fog_az</ItemText>
        </Ww>
        <Ww>
          <count>13</count>
          <WinNumber>1</WinNumber>
          <ItemText>\\arm2\../Protocol/InsTestingEntry.c\gins912data.fogz</ItemText>
        </Ww>
        <Ww>
          <count>14</count>
          <WinNumber>1</WinNumber>
          <ItemText>gfpgadata</ItemText>
        </Ww>
        <Ww>
          <count>15</count>
          <WinNumber>1</WinNumber>
          <ItemText>\\arm2\../Protocol/InsTestingEntry.c\gins912data.accelerometery</ItemText>
        </Ww>
        <Ww>
          <count>16</count>
          <WinNumber>1</WinNumber>
          <ItemText>\\arm2\../Protocol/InsTestingEntry.c\gins912data.accelerometerz</ItemText>
        </Ww>
        <Ww>
          <count>17</count>
          <WinNumber>1</WinNumber>
          <ItemText>\\arm2\../Protocol/InsTestingEntry.c\gins912data.fogx</ItemText>
        </Ww>
        <Ww>
          <count>18</count>
          <WinNumber>1</WinNumber>
          <ItemText>gins912data</ItemText>
        </Ww>
        <Ww>
          <count>19</count>
          <WinNumber>1</WinNumber>
          <ItemText>gnavout</ItemText>
        </Ww>
        <Ww>
          <count>20</count>
          <WinNumber>1</WinNumber>
          <ItemText>g_Navi</ItemText>
        </Ww>
        <Ww>
          <count>21</count>
          <WinNumber>1</WinNumber>
          <ItemText>g_SysVar</ItemText>
        </Ww>
        <Ww>
          <count>22</count>
          <WinNumber>1</WinNumber>
          <ItemText>g_GNSSData_In_Use</ItemText>
        </Ww>
        <Ww>
          <count>23</count>
          <WinNumber>1</WinNumber>
          <ItemText>fpga_syn_count</ItemText>
        </Ww>
        <Ww>
          <count>24</count>
          <WinNumber>1</WinNumber>
          <ItemText>fpga_loop_count</ItemText>
        </Ww>
        <Ww>
          <count>25</count>
          <WinNumber>1</WinNumber>
          <ItemText>fpga_syn_btw</ItemText>
        </Ww>
        <Ww>
          <count>26</count>
          <WinNumber>1</WinNumber>
          <ItemText>NaviCompute_do_count</ItemText>
        </Ww>
        <Ww>
          <count>27</count>
          <WinNumber>1</WinNumber>
          <ItemText>fpga_syn_NAVI_btw</ItemText>
        </Ww>
        <Ww>
          <count>28</count>
          <WinNumber>1</WinNumber>
          <ItemText>g_SysVar.WorkPhase</ItemText>
        </Ww>
        <Ww>
          <count>29</count>
          <WinNumber>1</WinNumber>
          <ItemText>gins912data_cash</ItemText>
        </Ww>
        <Ww>
          <count>30</count>
          <WinNumber>1</WinNumber>
          <ItemText>r_acc</ItemText>
        </Ww>
        <Ww>
          <count>31</count>
          <WinNumber>1</WinNumber>
          <ItemText>rs422_frame</ItemText>
        </Ww>
        <Ww>
          <count>32</count>
          <WinNumber>1</WinNumber>
          <ItemText>g_Navi</ItemText>
        </Ww>
        <Ww>
          <count>33</count>
          <WinNumber>1</WinNumber>
          <ItemText>r_acc</ItemText>
        </Ww>
        <Ww>
          <count>34</count>
          <WinNumber>1</WinNumber>
          <ItemText>FACTOR_ACC</ItemText>
        </Ww>
        <Ww>
          <count>35</count>
          <WinNumber>1</WinNumber>
          <ItemText>gins912data.accelerometerz</ItemText>
        </Ww>
        <Ww>
          <count>36</count>
          <WinNumber>1</WinNumber>
          <ItemText>gins912data.accelerometery</ItemText>
        </Ww>
        <Ww>
          <count>37</count>
          <WinNumber>1</WinNumber>
          <ItemText>gins912data.accelerometerx</ItemText>
        </Ww>
        <Ww>
          <count>38</count>
          <WinNumber>1</WinNumber>
          <ItemText>Acc</ItemText>
        </Ww>
        <Ww>
          <count>39</count>
          <WinNumber>1</WinNumber>
          <ItemText>LastAcc</ItemText>
        </Ww>
        <Ww>
          <count>40</count>
          <WinNumber>1</WinNumber>
          <ItemText>r_acc</ItemText>
        </Ww>
        <Ww>
          <count>41</count>
          <WinNumber>1</WinNumber>
          <ItemText>Acc</ItemText>
        </Ww>
        <Ww>
          <count>42</count>
          <WinNumber>1</WinNumber>
          <ItemText>g_SysVar.WorkPhase</ItemText>
        </Ww>
        <Ww>
          <count>43</count>
          <WinNumber>1</WinNumber>
          <ItemText>paochedata</ItemText>
        </Ww>
        <Ww>
          <count>44</count>
          <WinNumber>1</WinNumber>
          <ItemText>paocheframe</ItemText>
        </Ww>
        <Ww>
          <count>45</count>
          <WinNumber>1</WinNumber>
          <ItemText>g_Kalman</ItemText>
        </Ww>
        <Ww>
          <count>46</count>
          <WinNumber>1</WinNumber>
          <ItemText>gWm</ItemText>
        </Ww>
        <Ww>
          <count>47</count>
          <WinNumber>1</WinNumber>
          <ItemText>r_Gyro</ItemText>
        </Ww>
        <Ww>
          <count>48</count>
          <WinNumber>1</WinNumber>
          <ItemText>gfpgadataPredoSend.fpgaPreDodata</ItemText>
        </Ww>
        <Ww>
          <count>49</count>
          <WinNumber>1</WinNumber>
          <ItemText>gins912data.fogx</ItemText>
        </Ww>
        <Ww>
          <count>50</count>
          <WinNumber>1</WinNumber>
          <ItemText>coe_fog_gx</ItemText>
        </Ww>
        <Ww>
          <count>51</count>
          <WinNumber>1</WinNumber>
          <ItemText>pnavout-&gt;gyroX</ItemText>
        </Ww>
        <Ww>
          <count>52</count>
          <WinNumber>1</WinNumber>
          <ItemText>gfpgadataPredoSend.fpgaPreDodata.fogx</ItemText>
        </Ww>
        <Ww>
          <count>53</count>
          <WinNumber>1</WinNumber>
          <ItemText>gfpgadataPredoSend</ItemText>
        </Ww>
        <Ww>
          <count>54</count>
          <WinNumber>1</WinNumber>
          <ItemText>pnavout-&gt;accelX</ItemText>
        </Ww>
        <Ww>
          <count>55</count>
          <WinNumber>1</WinNumber>
          <ItemText>pnavout-&gt;gyroY</ItemText>
        </Ww>
        <Ww>
          <count>56</count>
          <WinNumber>1</WinNumber>
          <ItemText>ggpsorgdata,0x10</ItemText>
        </Ww>
      </WatchWindow1>
      <WatchWindow2>
        <Ww>
          <count>0</count>
          <WinNumber>2</WinNumber>
          <ItemText>ggpstimescount,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>1</count>
          <WinNumber>2</WinNumber>
          <ItemText>gfpgadata</ItemText>
        </Ww>
        <Ww>
          <count>2</count>
          <WinNumber>2</WinNumber>
          <ItemText>gpagedata,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>3</count>
          <WinNumber>2</WinNumber>
          <ItemText>ginputdata</ItemText>
        </Ww>
        <Ww>
          <count>4</count>
          <WinNumber>2</WinNumber>
          <ItemText>gins912data,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>5</count>
          <WinNumber>2</WinNumber>
          <ItemText>gnavout</ItemText>
        </Ww>
        <Ww>
          <count>6</count>
          <WinNumber>2</WinNumber>
          <ItemText>gdriverspacket</ItemText>
        </Ww>
        <Ww>
          <count>7</count>
          <WinNumber>2</WinNumber>
          <ItemText>canin</ItemText>
        </Ww>
        <Ww>
          <count>8</count>
          <WinNumber>2</WinNumber>
          <ItemText>r_fog,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>9</count>
          <WinNumber>2</WinNumber>
          <ItemText>r_acc,0x0A</ItemText>
        </Ww>
        <Ww>
          <count>10</count>
          <WinNumber>2</WinNumber>
          <ItemText>g_InertialSysAlign</ItemText>
        </Ww>
      </WatchWindow2>
      <MemoryWindow1>
        <Mm>
          <WinNumber>1</WinNumber>
          <SubType>0</SubType>
          <ItemText>0xE000ED88</ItemText>
          <AccSizeX>0</AccSizeX>
        </Mm>
      </MemoryWindow1>
      <MemoryWindow2>
        <Mm>
          <WinNumber>2</WinNumber>
          <SubType>0</SubType>
          <ItemText>0x8120000 + 0x400 * 128 -  8</ItemText>
          <AccSizeX>0</AccSizeX>
        </Mm>
      </MemoryWindow2>
      <MemoryWindow3>
        <Mm>
          <WinNumber>3</WinNumber>
          <SubType>0</SubType>
          <ItemText>buff</ItemText>
          <AccSizeX>0</AccSizeX>
        </Mm>
      </MemoryWindow3>
      <Tracepoint>
        <THDelay>0</THDelay>
      </Tracepoint>
      <DebugFlag>
        <trace>0</trace>
        <periodic>1</periodic>
        <aLwin>1</aLwin>
        <aCover>0</aCover>
        <aSer1>0</aSer1>
        <aSer2>0</aSer2>
        <aPa>0</aPa>
        <viewmode>1</viewmode>
        <vrSel>0</vrSel>
        <aSym>0</aSym>
        <aTbox>0</aTbox>
        <AscS1>0</AscS1>
        <AscS2>0</AscS2>
        <AscS3>0</AscS3>
        <aSer3>0</aSer3>
        <eProf>0</eProf>
        <aLa>0</aLa>
        <aPa1>0</aPa1>
        <AscS4>0</AscS4>
        <aSer4>0</aSer4>
        <StkLoc>0</StkLoc>
        <TrcWin>0</TrcWin>
        <newCpu>0</newCpu>
        <uProt>0</uProt>
      </DebugFlag>
      <LintExecutable></LintExecutable>
      <LintConfigFile></LintConfigFile>
      <bLintAuto>0</bLintAuto>
      <bAutoGenD>0</bAutoGenD>
      <LntExFlags>0</LntExFlags>
      <pMisraName></pMisraName>
      <pszMrule></pszMrule>
      <pSingCmds></pSingCmds>
      <pMultCmds></pMultCmds>
      <pMisraNamep></pMisraNamep>
      <pszMrulep></pszMrulep>
      <pSingCmdsp></pSingCmdsp>
      <pMultCmdsp></pMultCmdsp>
    </TargetOption>
  </Target>

  <Group>
    <GroupName>App</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>1</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\src\gd32f4xx_it.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_it.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>2</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\src\main.c</PathWithFileName>
      <FilenameWithoutPath>main.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>3</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\src\systick.c</PathWithFileName>
      <FilenameWithoutPath>systick.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>4</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\src\can_data.c</PathWithFileName>
      <FilenameWithoutPath>can_data.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>5</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\src\imu_data.c</PathWithFileName>
      <FilenameWithoutPath>imu_data.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>6</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\src\INS_Data.c</PathWithFileName>
      <FilenameWithoutPath>INS_Data.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>7</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\src\INS_Sys.c</PathWithFileName>
      <FilenameWithoutPath>INS_Sys.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>8</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\src\Time_Unify.c</PathWithFileName>
      <FilenameWithoutPath>Time_Unify.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>9</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\src\fpgad.c</PathWithFileName>
      <FilenameWithoutPath>fpgad.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>10</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\src\clock.c</PathWithFileName>
      <FilenameWithoutPath>clock.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>11</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\src\Data_shift.c</PathWithFileName>
      <FilenameWithoutPath>Data_shift.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>12</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\src\gdwatch.c</PathWithFileName>
      <FilenameWithoutPath>gdwatch.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>13</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\src\INS_Output.c</PathWithFileName>
      <FilenameWithoutPath>INS_Output.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>14</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\src\INS_Init.c</PathWithFileName>
      <FilenameWithoutPath>INS_Init.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>15</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\src\api_ch392.c</PathWithFileName>
      <FilenameWithoutPath>api_ch392.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>16</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\inc\deviceconfig.h</PathWithFileName>
      <FilenameWithoutPath>deviceconfig.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>17</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\src\datado.c</PathWithFileName>
      <FilenameWithoutPath>datado.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>18</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\src\datado.h</PathWithFileName>
      <FilenameWithoutPath>datado.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>BSP</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>19</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\bmp2.c</PathWithFileName>
      <FilenameWithoutPath>bmp2.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>20</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\bmp280.c</PathWithFileName>
      <FilenameWithoutPath>bmp280.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>21</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\bsp_adc.c</PathWithFileName>
      <FilenameWithoutPath>bsp_adc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>22</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\bsp_can.c</PathWithFileName>
      <FilenameWithoutPath>bsp_can.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>23</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\bsp_exti.c</PathWithFileName>
      <FilenameWithoutPath>bsp_exti.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>24</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\bsp_flash.c</PathWithFileName>
      <FilenameWithoutPath>bsp_flash.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>25</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\bsp_fmc.c</PathWithFileName>
      <FilenameWithoutPath>bsp_fmc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>26</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\bsp_fwdgt.c</PathWithFileName>
      <FilenameWithoutPath>bsp_fwdgt.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>27</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\bsp_gpio.c</PathWithFileName>
      <FilenameWithoutPath>bsp_gpio.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>28</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\bsp_rtc.c</PathWithFileName>
      <FilenameWithoutPath>bsp_rtc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>29</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\bsp_sys.c</PathWithFileName>
      <FilenameWithoutPath>bsp_sys.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>30</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\bsp_tim.c</PathWithFileName>
      <FilenameWithoutPath>bsp_tim.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>31</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\bsp_uart.c</PathWithFileName>
      <FilenameWithoutPath>bsp_uart.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>32</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\CH378_HAL.C</PathWithFileName>
      <FilenameWithoutPath>CH378_HAL.C</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>33</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\CH395CMD.C</PathWithFileName>
      <FilenameWithoutPath>CH395CMD.C</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>34</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\CH395SPI.C</PathWithFileName>
      <FilenameWithoutPath>CH395SPI.C</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>35</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\common.c</PathWithFileName>
      <FilenameWithoutPath>common.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>36</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\FILE_SYS.C</PathWithFileName>
      <FilenameWithoutPath>FILE_SYS.C</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>37</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\Logger.c</PathWithFileName>
      <FilenameWithoutPath>Logger.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>38</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\TCP_Server.c</PathWithFileName>
      <FilenameWithoutPath>TCP_Server.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>39</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\CH378_SPI_HW.C</PathWithFileName>
      <FilenameWithoutPath>CH378_SPI_HW.C</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>40</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\bsp_soft_i2c_master.c</PathWithFileName>
      <FilenameWithoutPath>bsp_soft_i2c_master.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>41</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\drv_spi.c</PathWithFileName>
      <FilenameWithoutPath>drv_spi.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>42</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\bsp\src\drv_gpio.c</PathWithFileName>
      <FilenameWithoutPath>drv_gpio.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>Common</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>3</GroupNumber>
      <FileNumber>43</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Common\src\data_convert.c</PathWithFileName>
      <FilenameWithoutPath>data_convert.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>RTT</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>44</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\RTT\SEGGER_RTT.c</PathWithFileName>
      <FilenameWithoutPath>SEGGER_RTT.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>4</GroupNumber>
      <FileNumber>45</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\RTT\SEGGER_RTT_printf.c</PathWithFileName>
      <FilenameWithoutPath>SEGGER_RTT_printf.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>NAV</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>5</GroupNumber>
      <FileNumber>46</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\NAV\nav_cli.c</PathWithFileName>
      <FilenameWithoutPath>nav_cli.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>INAV</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>47</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\INAV\adxl355.c</PathWithFileName>
      <FilenameWithoutPath>adxl355.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>48</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\INAV\private_math.c</PathWithFileName>
      <FilenameWithoutPath>private_math.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>49</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\INAV\readpaoche.c</PathWithFileName>
      <FilenameWithoutPath>readpaoche.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>50</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\INAV\navi.c</PathWithFileName>
      <FilenameWithoutPath>navi.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>51</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\INAV\align.c</PathWithFileName>
      <FilenameWithoutPath>align.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>52</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\INAV\AnnTempCompen.c</PathWithFileName>
      <FilenameWithoutPath>AnnTempCompen.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>53</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\INAV\kalman.c</PathWithFileName>
      <FilenameWithoutPath>kalman.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>54</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\INAV\matvecmath.c</PathWithFileName>
      <FilenameWithoutPath>matvecmath.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>55</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\INAV\dynamic_align.c</PathWithFileName>
      <FilenameWithoutPath>dynamic_align.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>6</GroupNumber>
      <FileNumber>56</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\INAV\read_and_check_gnss_data.c</PathWithFileName>
      <FilenameWithoutPath>read_and_check_gnss_data.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>Protocol</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>57</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Protocol\computerFrameParse.c</PathWithFileName>
      <FilenameWithoutPath>computerFrameParse.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>58</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Protocol\protocol.c</PathWithFileName>
      <FilenameWithoutPath>protocol.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>59</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Protocol\frame_analysis.c</PathWithFileName>
      <FilenameWithoutPath>frame_analysis.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>7</GroupNumber>
      <FileNumber>60</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Protocol\InsTestingEntry.c</PathWithFileName>
      <FilenameWithoutPath>InsTestingEntry.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>Source/Edwoy</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>61</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\Edwoy\pjt_board.c</PathWithFileName>
      <FilenameWithoutPath>pjt_board.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>62</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\Edwoy\pjt_board.h</PathWithFileName>
      <FilenameWithoutPath>pjt_board.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>63</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\Edwoy\sensor_misc.c</PathWithFileName>
      <FilenameWithoutPath>sensor_misc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>64</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\Edwoy\app_tool.c</PathWithFileName>
      <FilenameWithoutPath>app_tool.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>65</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\Edwoy\app_tool.h</PathWithFileName>
      <FilenameWithoutPath>app_tool.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>66</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\Edwoy\sensor_misc.h</PathWithFileName>
      <FilenameWithoutPath>sensor_misc.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>8</GroupNumber>
      <FileNumber>67</FileNumber>
      <FileType>5</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Source\Edwoy\pjt_glb_head.h</PathWithFileName>
      <FilenameWithoutPath>pjt_glb_head.h</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>CMSIS</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>68</FileNumber>
      <FileType>2</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s</PathWithFileName>
      <FilenameWithoutPath>startup_gd32f450_470.s</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>69</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c</PathWithFileName>
      <FilenameWithoutPath>system_gd32f4xx.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>9</GroupNumber>
      <FileNumber>70</FileNumber>
      <FileType>4</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\CMSIS\arm_cortexM4lf_math.lib</PathWithFileName>
      <FilenameWithoutPath>arm_cortexM4lf_math.lib</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

  <Group>
    <GroupName>Library</GroupName>
    <tvExp>0</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <RteFlg>0</RteFlg>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>71</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_adc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>72</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_can.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>73</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_crc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>74</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_ctc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>75</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_dac.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>76</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_dbg.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>77</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_dci.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>78</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_dma.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>79</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_enet.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>80</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_exmc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>81</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_exti.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>82</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_fmc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>83</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_fwdgt.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>84</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_gpio.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>85</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_i2c.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>86</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_ipa.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>87</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_iref.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>88</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_misc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>89</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_pmu.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>90</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_rcu.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>91</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_rtc.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>92</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_sdio.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>93</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_spi.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>94</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_syscfg.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>95</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_timer.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>96</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_tli.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>97</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_trng.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>98</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_usart.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
    <File>
      <GroupNumber>10</GroupNumber>
      <FileNumber>99</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <tvExpOptDlg>0</tvExpOptDlg>
      <bDave2>0</bDave2>
      <PathWithFileName>..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c</PathWithFileName>
      <FilenameWithoutPath>gd32f4xx_wwdgt.c</FilenameWithoutPath>
      <RteFlg>0</RteFlg>
      <bShared>0</bShared>
    </File>
  </Group>

</ProjectOpt>
