#ifndef ____COMPUTER_FRAME_PARSE_H____
#define ____COMPUTER_FRAME_PARSE_H____

#include "gd32f4xx.h"
#include "config.h"
//#include "algorithm.h"
#include "tlhtype.h"
#include "INS_Data.h"

#define FRAME_HEAD						0xAF55FA
#define FrameHead_Index					2
#define FRAME_END						0xFF

#define CMD_ACCEL_ADJUST				0xAA10
#define CMD_GYRO_ADJUST					0xAA11

#define CMD_SET_REPORT_MODE				0xAAAA
#define CMD_SET_SERIAL_CONFIG			0xAA13
#define CMD_SET_OUTPUT_FREQ				0xAA14
#define CMD_SET_READ_CONFIG				0xAA24
#define CMD_SET_SYS_MODE				0xAA26
#define CMD_SET_MECHAN_MIGRA			0xAA18
#define CMD_SET_GNSS_VEHI				0xAA19
#define CMD_SET_MECHAN_OBARM			0xAA1A
#define CMD_SET_MECHAN_OB_VEHI			0xAA1B
#define CMD_SET_COURSE_ANGLE			0xAA21
#define CMD_SET_USER_AXIS				0xAA1E
#define CMD_SET_ZERO_OFFSET_TIME		0xAA25
#define CMD_SET_GNSS_BASELINE			0xAA20
#define CMD_SET_ALL						0xAA27
#define CMD_SET_SAVE_CONFIG				0xAAF1	//固化参数
#define CMD_SET_RESUME_DEFAULT			0xAAF2	//恢复默认
#define CMD_SET_SAVE_ALL				0xAAF3	//全部应用
#define CMD_SET_READ_PARA				0xAAF4	//参数回读
#define CMD_READ_FIRMWARE_VER			0xAAF5	//读固件版本
#define CMD_READ_DEV_TYPE				0xAAF0	//读设备型号
#define CMD_SET_FM_UPDATE				0xAAF6	//固件升级

#define CMD_PARAM_DEBUG_SET				0xAAC1	//DEBUG开关
#define CMD_PARAM_IMU_SELECT			0xAAC2	//IMU数据切换开关
#define CMD_PARAM_GPS_TYPE_SET			0xAAC3	//GPS数据类型设置
#define CMD_PARAM_DATA_OUTPUT_TYPE_SET	0xAAC4	//数据输出类型设置
#define CMD_PARAM_CALIB_RATE_SET		0xAA30	//


#define FIRST_PROGRAM_BYTE		0xCC

#define PRODUCT_ID			0x1A0C;
#define DEVICE_ID			0x3E01;

#pragma pack(1)


//typedef  struct INS_frame_set_t
//{
//    uint8_t frameType;
//    uint8_t baudrate;
//    uint16_t freq;
//} INS_Frame_Setting_TypeDef;

//typedef enum frame_serial_baud_cfg_t
//{
//    cfg_baud_9600 = 0x01,
//    cfg_baud_19200 = 0x02,
//    cfg_baud_38400 = 0x03,
//    cfg_baud_57600 = 0x04,
//    cfg_baud_115200 = 0x05,
//    cfg_baud_230400 = 0x06,
//    cfg_baud_460800 = 0x07,
//    cfg_baud_614400 = 0x08,
//} Frame_Serial_Baud_Cfg_TypeDef;

//typedef enum frame_serial_freq_cfg_t
//{
//    cfg_freq_1Hz = 0x0001,
//    cfg_freq_10Hz = 0x000A,
//    cfg_freq_20Hz = 0x0014,
//    cfg_freq_25Hz = 0x0019,
//    cfg_freq_50Hz = 0x0032,
//    cfg_freq_100Hz = 0x0064,
//    cfg_freq_125Hz = 0x007D,
//    cfg_freq_250Hz = 0x00FA,
//    cfg_freq_500Hz = 0x01F4,
//} Frame_Serial_Freq_Cfg_TypeDef;

//typedef enum frame_serial_format_cfg_t
//{
//    cfg_format_RAWIMU = 0x04,
//    cfg_format_GPGGA = 0x03,
//    cfg_format_GPRMC = 0x02,
//    cfg_format_GIPOT = 0x01,
//} Frame_Serial_Format_Cfg_TypeDef;

//typedef enum serial_index_t
//{
//    index_RS422 = 0x00,
//    index_RS232A = 0x01,
//    index_RS232B = 0x02,
//} Serial_Index_TypeDef;

//typedef enum ins_data_type_t
//{
//    INS_DATA_MODE_0 = 0,
//    INS_DATA_MODE_1 = 1,
//    INS_DATA_MODE_2 = 2,
//    INS_DATA_MODE_3 = 3,
//    INS_DATA_MODE_4 = 4,
//    INS_DATA_MODE_5 = 5,
//    INS_DATA_MODE_6 = 6,
//    INS_DATA_MODE_7 = 7,
//    INS_DATA_MODE_8 = 8,
//    INS_DATA_MODE_9 = 9,
//    INS_DATA_MODE_MAX,
//} INS_DATA_ENUMTypeDef;

//typedef enum ins_boot_mode_t
//{
//    INS_BOOT_MODE_1 = 1,
//    INS_BOOT_MODE_2 = 2,
//    INS_BOOT_MODE_MAX,
//} INS_BOOT_MODE_ENUMTypeDef;

typedef struct
{
    uint8_t comX;
    uint32_t baudRate;
    uint8_t data_bits ;
    uint8_t parity    ;
    uint8_t stop_bits ;
    uint8_t mode ;
    uint8_t usr_type    ;
} COMM_CONFIG_TypeDef;

//typedef  struct setting_data_t
//{
//    //uint8_t 	baud;
//    uint16_t	freq;
//    //INS_Frame_Setting_TypeDef serialFrameSetting[3];
//    //系统工作状态 2
//    //INS_BOOT_MODE_ENUMTypeDef workmode;	//导航工作模式
//    //INS_DATA_ENUMTypeDef datamode;		//导航数据模式
//    //GNSS臂杆参数 3
//    //float gnssMechanicalMigration_x;	//传感器基点相对测量点的机械偏移x
//    //float gnssMechanicalMigration_y;	//传感器基点相对测量点的机械偏移y
//    //float gnssMechanicalMigration_z;	//传感器基点相对测量点的机械偏移z
//    Param_t	param;
//    //航向角补偿 4
//    //float courseAngleCompensation;
//    //用户设置的坐标轴类型 5
//    uint8_t imuAxis;
//    //时间补偿 6
//    short timeCompensation;
//    //GNSS基线长度 7
//    float gnssBaselineLength;

//} SettingDataTypeDef;

//为了应付上位机使用结构体传输导致异常
typedef struct
{
    float gnssArmLength[3];
    float gnssAtt_from_vehicle[3];//双天线相对IMU的安装角度
    float OBArmLength[3];
    float OBAtt_from_vehicle[3];//车底盘相对IMU的安装角度
    unsigned char flag;				//bit0:gps杆臂是否被设置
    							//bit1:双天线相对IMU的安装角度是否被设置
    							//bit2:导航系统安装角是否被设置
    							//bit3:车底盘相对IMU的安装角度是否被设置
    							//bit4:双天线基线长度是否合法 
} Tmp_Param_t; //

typedef  struct tmp_setting_data_t
{
    //uint8_t 	baud;
    uint16_t	freq;
    //INS_Frame_Setting_TypeDef serialFrameSetting[3];
    //系统工作状态 2
    //INS_BOOT_MODE_ENUMTypeDef workmode;	//导航工作模式
    //INS_DATA_ENUMTypeDef datamode;		//导航数据模式
    //GNSS臂杆参数 3
    //float gnssMechanicalMigration_x;	//传感器基点相对测量点的机械偏移x
    //float gnssMechanicalMigration_y;	//传感器基点相对测量点的机械偏移y
    //float gnssMechanicalMigration_z;	//传感器基点相对测量点的机械偏移z
    Tmp_Param_t	param;
    //航向角补偿 4
    //float courseAngleCompensation;
    //用户设置的坐标轴类型 5
    uint8_t imuAxis;
    //时间补偿 6
    short timeCompensation;
    //GNSS基线长度 7
    float gnssBaselineLength;

} Temp_SettingDataTypeDef;


//typedef  struct setting_t
//{
//    uint8_t report_en;					//  上报使能 0:使能 1:禁能
//	uint8_t	 calibRate;
//    uint16_t ProductID;					//	产品ID
//    uint16_t DeviceID;					//	设备ID 5
//    uint32_t ChipID[3];					//	器件ID 12
//    SettingDataTypeDef 	settingData;
//    char facilityType[28];				//设备型号
//    char ARM1_FW_Ver[18];				//ARM1软件版本
//    char ARM2_FW_Ver[18];				//ARM2软件版本
//    char FPGA_FW_Ver[18];				//FPGA软件版本 12
//    uint32_t hash;
//} AppSettingTypeDef;


#pragma pack()

typedef  struct
{
    float	S_ax;
    float	M_axy;
    float	M_axz;
    float	S_ay;
    float	M_ayx;
    float	M_ayz;
    float	S_az;
    float	M_azx;
    float	M_azy;
    float	B_ax;
    float	B_ay;
    float	B_az;
} __8k21_cali_t;

typedef  struct calib_t
{
    uint8_t 		imuSelect;
    __8k21_cali_t 	accelCali;
    __8k21_cali_t 	gyroCali;
    AdjPara_t		adj;
    uint32_t 		hash;
} CalibTypeDef;


typedef enum AppState_t
{
    APP_STATE_INIT = 0,
    APP_STATE_MEASURE = 1,
    APP_STATE_CALC = 2,
    APP_STATE_INTEREACT = 3,
    APP_STATE_MAX,
} AppStateTypeDef;

extern AppSettingTypeDef hSetting;
extern AppSettingTypeDef hDefaultSetting;

uint8_t frame_setting_is_update(void);
uint8_t frameParse(uint8_t* pData, uint16_t len);
void comm_store_init(void);
void comm_set_defaultPara(void);
void comm_set_customPara(void);
void comm_fm_update(void);
void comm_resume_defaultPara(void);
void comm_para_ehco_rsp(uint16_t cmd);
uint16_t comm_read_currentFreq(void);
uint8_t comm_axis_read(void);
uint8_t comm_read_currentFrameType(uint8_t channel);
uint8_t comm_read_dataMode(void);
void comm_send_end_frame(uint16_t cmd);
float comm_get_baseline(void);
void comm_nav_para_syn(void);

void comm_param_setbits(uint8_t bitIndex);
void comm_param_clrbits(uint8_t bitIndex);
void protocol_send(void);
float* comm_readAccelCaliPtr(void);
float* comm_readGyroCaliPtr(void);

void miscell_handle(void);
void comm_calib(void);
void IMU_test_data_send(void* pnav);

// 新增的版本查询和升级相关函数
void comm_init_version_info(void);
void comm_handle_update_packet(uint8_t *pData, uint16_t len);
void comm_read_ver_rsp(uint16_t cmd);
void comm_write_rsp(uint16_t cmd);

#endif // ____COMPUTER_FRAME_PARSE_H____

