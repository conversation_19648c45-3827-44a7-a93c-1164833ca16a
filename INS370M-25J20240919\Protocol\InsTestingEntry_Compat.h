//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：InsTestingEntry_Compat.h
// 文件标识：
// 文件摘要：兼容HPM6750项目的数据输出和协议处理接口
//
// 当前版本：V1.0
// 作    者：迁移自HPM6750项目
// 完成时间：2024.12.19
//---------------------------------------------------------

#ifndef __INSTESTINGENTRY_COMPAT_H
#define __INSTESTINGENTRY_COMPAT_H

#include "gd32f4xx.h"
#include <stdint.h>
#include <string.h>

// 数据输出模式定义
#define UPPER_InsUart_Hd0x66AA      0x66AA

// 帧类型定义
#define FRAME_CFRDpart_16bitQty     32

// 前向声明，避免包含冲突
struct navoutdata_t;

// 新增的预处理数据发送结构体（简化版本）
typedef struct {
    uint16_t head1;
    uint16_t head2;
    uint16_t dataLen;
    uint8_t fpgaPreDodata[256];  // 使用字节数组避免类型冲突
    uint32_t fpgaItrCount;
    uint32_t fpgaLoopCount;
    uint8_t Status;
    uint16_t CheckSum;
} compat_fpgadata_predo_send_t;

// 新增的全局变量
extern compat_fpgadata_predo_send_t gfpgadataPredoSend_compat; // 兼容的预处理数据发送

extern uint32_t fpga_syn_count;         // FPGA同步计数
extern uint32_t fpga_loop_count;        // FPGA循环计数

// 兼容性函数声明
void AlgorithmDo_compat(void);
void INS912_Output_compat(struct navoutdata_t *pnavout);

// 数据发送函数
void FPGATo422_11BB_send(void);
void FPGATo422_00BB_send(struct navoutdata_t *pnavout);
void SDTo422_00BB_send(struct navoutdata_t *pnavout);

// 校验函数
uint16_t app_accum_verify_16bit(uint16_t *data, uint16_t len);
uint8_t app_accum_verify_8bit(uint8_t *data, uint16_t len);

// 串口发送函数（兼容现有接口）
void uart4sendmsg_compat(char *msg, uint16_t len);

// 数据处理函数
void get_fpgadata_compat(void);
void analysisRxdata_compat(void);
void loopDoOther_compat(void);

#endif // __INSTESTINGENTRY_COMPAT_H
