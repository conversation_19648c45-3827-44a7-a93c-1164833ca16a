//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：InsTestingEntry_Compat.h
// 文件标识：
// 文件摘要：兼容HPM6750项目的数据输出和协议处理接口
//
// 当前版本：V1.0
// 作    者：迁移自HPM6750项目
// 完成时间：2024.12.19
//---------------------------------------------------------

#ifndef __INSTESTINGENTRY_COMPAT_H
#define __INSTESTINGENTRY_COMPAT_H

#include "gd32f4xx.h"
#include <stdint.h>
#include <string.h>

// 数据输出模式定义
#define UPPER_InsUart_Hd0x66AA      0x66AA
#define DRIVERSDATATYPE_GDW912      0x912

// 帧类型定义
#define FRAME_CFRDpart_16bitQty     32

// 导航输出数据结构体
typedef struct {
    float gyroX;        // 陀螺仪X轴
    float gyroY;        // 陀螺仪Y轴
    float gyroZ;        // 陀螺仪Z轴
    float accelX;       // 加速度计X轴
    float accelY;       // 加速度计Y轴
    float accelZ;       // 加速度计Z轴
    float pitch;        // 俯仰角
    float roll;         // 横滚角
    float azimuth;      // 航向角
    double latitude;    // 纬度
    double longitude;   // 经度
    float altitude;     // 高度
    float ve;           // 东向速度
    float vn;           // 北向速度
    float vu;           // 天向速度
    uint8_t status;     // 状态
} navoutdata_t;

// CAN输入数据结构体
typedef struct {
    uint8_t data[64];   // CAN数据
    uint8_t valid;      // 数据有效标志
} navcanin_t;

// FPGA数据结构体
typedef struct {
    uint16_t head1;
    uint16_t head2;
    uint16_t dataLen;
    uint8_t data[96];   // FPGA原始数据
    uint32_t fpgaItrCount;
    uint32_t fpgaLoopCount;
    uint8_t Status;
    uint16_t CheckSum;
} fpgadata_send_t;

// 预处理数据发送结构体
typedef struct {
    uint16_t head1;
    uint16_t head2;
    uint16_t dataLen;
    navoutdata_t fpgaPreDodata;  // 预处理后的数据
    uint32_t fpgaItrCount;
    uint32_t fpgaLoopCount;
    uint8_t Status;
    uint16_t CheckSum;
} fpgadata_predo_send_t;

// 数组转数据结构体
typedef struct {
    uint8_t gddata[128];
} arraytodata_t;

// GDW接收数据结构体
typedef struct {
    uint8_t data[256];
} gdwrxdata912_t;

// FPGA到INS数据结构体
typedef struct {
    uint8_t data[128];
} fpga2ins_t;

// 系统变量结构体
typedef struct {
    uint8_t WorkPhase;  // 工作阶段
} SysVar_t;

// 全局变量声明
extern uint16_t gfpgadata[200];         // FPGA数据
extern navoutdata_t gnavout;            // 导航输出数据
extern arraytodata_t ginputdata;        // 输入数据
extern gdwrxdata912_t gins912data;      // GDW912数据
extern fpga2ins_t fpga2ins;             // FPGA到INS数据
extern SysVar_t g_SysVar;               // 系统变量

extern fpgadata_send_t gfpgadataSend;           // FPGA数据发送
extern fpgadata_predo_send_t gfpgadataPredoSend; // 预处理数据发送

extern uint32_t fpga_syn_count;         // FPGA同步计数
extern uint32_t fpga_loop_count;        // FPGA循环计数

// 函数声明
void INS912AlgorithmEntry(uint16_t *pfpgadata, navcanin_t *pcanin, navoutdata_t *pnavout);
void AlgorithmDo(void);
void INS912_Output(navoutdata_t *pnavout);

// 数据发送函数
void FPGATo422_11BB_send(void);
void FPGATo422_00BB_send(navoutdata_t *pnavout);
void SDTo422_00BB_send(navoutdata_t *pnavout);

// 校验函数
uint16_t app_accum_verify_16bit(uint16_t *data, uint16_t len);
uint8_t app_accum_verify_8bit(uint8_t *data, uint16_t len);

// 串口发送函数
void uart4sendmsg(char *msg, uint16_t len);

// 数据处理函数
void get_fpgadata(void);
void analysisRxdata(void);
void loopDoOther(void);

#endif // __INSTESTINGENTRY_COMPAT_H
