#include "bsp_flash.h"

#include "stdio.h"
#include "stdlib.h"
#include "string.h"
#include "main.h"

//#define STM_SECTOR_SIZE 			2048
//#define STM_USER_ADDR					STM_SECTOR_SIZE * 48

//#define FLASH_BUFF_SIZE  	512U	//bill 2023-06-25
#define FLASH_BUFF_SIZE  	1024U	//bill 2023-06-25
uint32_t g_Addr = ADDR_FMC_SECTOR_6;

uint16_t g_FlashBuf[FLASH_BUFF_SIZE];
uint32_t g_AddrBase;
uint8_t g_NeedWrite = 0;
uint8_t g_StartWrite = 0;

void InitFlashAddr(uint32_t nOffset)
{
	g_Addr = ADDR_FMC_SECTOR_6 + nOffset; 
}

void WriteOld(void)
{
	int i = 0;
	if(fmc_sector_erase(CTL_SECTOR_NUMBER_6) != FMC_READY)
	{
		//error
	}
	
	for( i = 0; i < FLASH_BUFF_SIZE; i++, g_AddrBase += 2)
	{
		fmc_halfword_program(g_AddrBase,g_FlashBuf[i]);
	}
	g_NeedWrite = 0;
}

void EndWrite(void)
{
	if(g_NeedWrite == 1)
	{
		WriteOld();	
	}
	fmc_lock();
	g_StartWrite = 0;
}

void WriteFlash(uint8_t *buff, uint16_t size)
{
	uint32_t addr_base;
	uint16_t len;
	uint16_t i;
	if(g_Addr < FLASH_BASE ||(g_Addr >= ADDR_FMC_SECTOR_6+16*1024))
		return;
	else
	{
		if(g_StartWrite == 0)
		{
			g_StartWrite = 1;
			g_NeedWrite = 0;
			fmc_unlock();
			/* clear pending flags */
			fmc_flag_clear(FMC_FLAG_END | FMC_FLAG_OPERR | FMC_FLAG_WPERR | FMC_FLAG_PGMERR | FMC_FLAG_PGSERR);
		}

		while(size)
		{
			addr_base = ADDR_FMC_SECTOR_6;
			
			if(g_NeedWrite == 1 && g_AddrBase != addr_base)
			{
				WriteOld();
			}
			
			len=size;
			if(size < FLASH_BUFF_SIZE)
			{
				if(g_NeedWrite == 0)
					ReadFlashByAddr(addr_base, (uint8_t *)g_FlashBuf, FLASH_BUFF_SIZE*2);
				
				//memcpy((uint8_t *)(&g_FlashBuf[0]) + g_Addr - addr_base, buff, len);	//bill 
				memcpy((uint8_t *)(&g_FlashBuf[0]) + 0, buff, len);
				
				g_NeedWrite = 1;
				g_AddrBase = addr_base;
				g_Addr += len;
				//break;
			}
			
			//memcpy((uint8_t *)(&g_FlashBuf[0]) + g_Addr - addr_base, buff, len);	//bill 
			memcpy((uint8_t *)(&g_FlashBuf[0]) + 0, buff, len);
			if(fmc_sector_erase(CTL_SECTOR_NUMBER_6) != FMC_READY)
			{ 
				i = 0;
			}
			for(i=0;i<FLASH_BUFF_SIZE;)
			{
				if (fmc_halfword_program(addr_base,g_FlashBuf[i]) == FMC_READY)
				{
					addr_base=addr_base+2;
					i++;
				}
			}
			g_Addr += len;
			buff += len;
			size -= len;
		}
	}
}

void ReadFlashByAddr(uint32_t addr, uint8_t *buff, uint16_t size)
{
	uint16_t i;
	if(addr < ADDR_FMC_SECTOR_6)
		return;
	else
	{
		for(i = 0; i < size; i++)
		{
			buff[i] = *(__IO uint8_t *)(addr + i);
		}
	}
}

void ReadFlash(uint8_t *buff, uint16_t size)
{
	uint16_t i;
	for(i = 0; i < size; i++)
	{
		buff[i] = *(__IO uint8_t *)(g_Addr + i);
	}
	g_Addr += size;
}

// ========== 新增的Flash操作接口，兼容HPM6750项目 ==========

/**
 * @brief 初始化Flash
 * @return 0: 成功, -1: 失败
 */
int norflash_init(void)
{
    // GD32F4xx内部Flash不需要特殊初始化
    return 0;
}

/**
 * @brief 从Flash读取数据
 * @param offset 偏移地址
 * @param buf 数据缓冲区
 * @param size_bytes 读取字节数
 * @return 0: 成功, -1: 失败
 */
int norflash_read(uint32_t offset, void *buf, uint32_t size_bytes)
{
    uint32_t flash_addr = FLASH_BASE + offset;
    uint32_t i;
    uint8_t *pbuf = (uint8_t *)buf;

    if (buf == NULL || size_bytes == 0) {
        return -1;
    }

    for (i = 0; i < size_bytes; i++) {
        pbuf[i] = *(__IO uint8_t *)(flash_addr + i);
    }

    return 0;
}

/**
 * @brief 向Flash写入数据
 * @param offset 偏移地址
 * @param buf 数据缓冲区
 * @param size_bytes 写入字节数
 * @return 0: 成功, -1: 失败
 */
int norflash_write(uint32_t offset, const void *buf, uint32_t size_bytes)
{
    uint32_t flash_addr = FLASH_BASE + offset;
    uint32_t i;
    const uint8_t *pbuf = (const uint8_t *)buf;
    fmc_state_enum status;

    if (buf == NULL || size_bytes == 0) {
        return -1;
    }

    // 解锁Flash
    fmc_unlock();

    // 清除标志位
    fmc_flag_clear(FMC_FLAG_END | FMC_FLAG_OPERR | FMC_FLAG_WPERR | FMC_FLAG_PGMERR | FMC_FLAG_PGSERR);

    // 按字节写入
    for (i = 0; i < size_bytes; i++) {
        status = fmc_byte_program(flash_addr + i, pbuf[i]);
        if (status != FMC_READY) {
            fmc_lock();
            return -1;
        }
    }

    // 锁定Flash
    fmc_lock();

    return 0;
}

/**
 * @brief 擦除Flash扇区
 * @param offset 偏移地址
 * @return 0: 成功, -1: 失败
 */
int norflash_erase_sector(uint32_t offset)
{
    uint32_t flash_addr = FLASH_BASE + offset;
    fmc_state_enum status;
    uint32_t sector_num;

    // 根据地址确定扇区号
    if (flash_addr >= ADDR_FMC_SECTOR_0 && flash_addr < ADDR_FMC_SECTOR_1) {
        sector_num = CTL_SECTOR_NUMBER_0;
    } else if (flash_addr >= ADDR_FMC_SECTOR_1 && flash_addr < ADDR_FMC_SECTOR_2) {
        sector_num = CTL_SECTOR_NUMBER_1;
    } else if (flash_addr >= ADDR_FMC_SECTOR_2 && flash_addr < ADDR_FMC_SECTOR_3) {
        sector_num = CTL_SECTOR_NUMBER_2;
    } else if (flash_addr >= ADDR_FMC_SECTOR_3 && flash_addr < ADDR_FMC_SECTOR_4) {
        sector_num = CTL_SECTOR_NUMBER_3;
    } else if (flash_addr >= ADDR_FMC_SECTOR_4 && flash_addr < ADDR_FMC_SECTOR_5) {
        sector_num = CTL_SECTOR_NUMBER_4;
    } else if (flash_addr >= ADDR_FMC_SECTOR_5 && flash_addr < ADDR_FMC_SECTOR_6) {
        sector_num = CTL_SECTOR_NUMBER_5;
    } else if (flash_addr >= ADDR_FMC_SECTOR_6 && flash_addr < ADDR_FMC_SECTOR_7) {
        sector_num = CTL_SECTOR_NUMBER_6;
    } else if (flash_addr >= ADDR_FMC_SECTOR_7 && flash_addr < ADDR_FMC_SECTOR_8) {
        sector_num = CTL_SECTOR_NUMBER_7;
    } else if (flash_addr >= ADDR_FMC_SECTOR_8 && flash_addr < ADDR_FMC_SECTOR_9) {
        sector_num = CTL_SECTOR_NUMBER_8;
    } else if (flash_addr >= ADDR_FMC_SECTOR_9 && flash_addr < ADDR_FMC_SECTOR_10) {
        sector_num = CTL_SECTOR_NUMBER_9;
    } else if (flash_addr >= ADDR_FMC_SECTOR_10 && flash_addr < ADDR_FMC_SECTOR_11) {
        sector_num = CTL_SECTOR_NUMBER_10;
    } else if (flash_addr >= ADDR_FMC_SECTOR_11) {
        sector_num = CTL_SECTOR_NUMBER_11;
    } else {
        return -1;
    }

    // 解锁Flash
    fmc_unlock();

    // 清除标志位
    fmc_flag_clear(FMC_FLAG_END | FMC_FLAG_OPERR | FMC_FLAG_WPERR | FMC_FLAG_PGMERR | FMC_FLAG_PGSERR);

    // 擦除扇区
    status = fmc_sector_erase(sector_num);

    // 锁定Flash
    fmc_lock();

    return (status == FMC_READY) ? 0 : -1;
}

/**
 * @brief 擦除Flash块（多个扇区）
 * @param offset 偏移地址
 * @return 0: 成功, -1: 失败
 */
int norflash_erase_block(uint32_t offset)
{
    // 对于GD32F4xx，块擦除等同于扇区擦除
    return norflash_erase_sector(offset);
}

/**
 * @brief 擦除整个Flash芯片
 * @return 0: 成功, -1: 失败
 */
int norflash_erase_chip(void)
{
    fmc_state_enum status;

    // 解锁Flash
    fmc_unlock();

    // 清除标志位
    fmc_flag_clear(FMC_FLAG_END | FMC_FLAG_OPERR | FMC_FLAG_WPERR | FMC_FLAG_PGMERR | FMC_FLAG_PGSERR);

    // 批量擦除
    status = fmc_mass_erase();

    // 锁定Flash
    fmc_lock();

    return (status == FMC_READY) ? 0 : -1;
}

// ========== 固件升级相关接口 ==========

/**
 * @brief 写入Flash数据（用于固件升级）
 * @param pucBuff 数据缓冲区
 * @param uiAddress 地址
 * @param uiLen 长度
 */
void Drv_FlashWrite(uint8_t *pucBuff, uint32_t uiAddress, uint32_t uiLen)
{
    norflash_write(uiAddress, pucBuff, uiLen);
}

/**
 * @brief 从Flash读取数据（用于固件升级）
 * @param pucBuff 数据缓冲区
 * @param uiAddress 地址
 * @param uiLen 长度
 */
void Drv_FlashRead(uint8_t *pucBuff, uint32_t uiAddress, uint32_t uiLen)
{
    norflash_read(uiAddress, pucBuff, uiLen);
}

/**
 * @brief 擦除Flash扇区（用于固件升级）
 * @param uiAddress 地址
 */
void Drv_FlashErase(uint32_t uiAddress)
{
    norflash_erase_sector(uiAddress);
}

/**
 * @brief 系统复位
 */
void Drv_SystemReset(void)
{
    NVIC_SystemReset();
}

/**
 * @brief CRC8校验
 * @param ptr 数据指针
 * @param len 数据长度
 * @return CRC8校验值
 */
uint8_t Get_CRC8(uint8_t *ptr, uint8_t len)
{
    uint8_t i, j;
    uint8_t crc = 0x00;

    for (i = 0; i < len; i++) {
        crc ^= ptr[i];
        for (j = 0; j < 8; j++) {
            if (crc & 0x80)
                crc = (crc << 1) ^ 0x31;
            else
                crc = (crc << 1);
        }
    }

    return (crc);
}
