# INS370M-25J20240919 编译指导

## 概述

本文档说明如何逐步编译和启用从HPM6750项目迁移过来的功能。由于存在类型定义冲突，建议按照以下步骤逐步启用功能。

## 当前状态

**重要更新**: 已采用更好的方法，直接扩展现有的 `insTestingEntry.h` 和 `InsTestingEntry.c` 文件，而不是创建兼容文件。这样避免了类型冲突问题，更加简洁和易于维护。

## 编译步骤

### 第一步：基础编译测试

1. **确保原有代码正常编译**
   - 当前main.c中新功能已注释
   - 应该能够正常编译通过

2. **添加新的源文件到项目**
   ```
   bsp/src/bsp_sd_fatfs.c
   Protocol/SetParaBao.c
   Source/src/INS_App_Init.c
   Source/src/app_test.c
   ```

   注意：不再需要 `InsTestingEntry_Compat.c`，因为功能已直接集成到现有的 `InsTestingEntry.c` 中。

3. **添加头文件路径**
   ```
   bsp/inc/
   Protocol/
   Source/inc/
   ```

### 第二步：逐步启用功能

#### 2.1 启用Flash功能

1. **修改bsp_flash.h和bsp_flash.c**
   - 这些文件扩展了现有的Flash操作
   - 应该不会有冲突

2. **测试Flash功能**
   ```c
   // 在main函数中添加测试代码
   #include "bsp_flash.h"
   
   // 测试Flash读写
   norflash_init();
   uint8_t test_data[256] = {0x55, 0xAA, ...};
   norflash_write(0x10000, test_data, 256);
   ```

#### 2.2 启用SD卡功能

1. **包含SD卡头文件**
   ```c
   #include "bsp_sd_fatfs.h"
   ```

2. **初始化SD卡**
   ```c
   if (Fatfs_Init() == 0) {
       // SD卡初始化成功
   }
   ```

#### 2.3 启用参数配置功能

1. **包含参数配置头文件**
   ```c
   #include "SetParaBao.h"
   ```

2. **初始化参数**
   ```c
   SetParaInit();
   ```

### 第三步：解决类型冲突

#### 3.1 类型冲突分析

主要冲突的类型：
- `navoutdata_t` (在INS912AlgorithmEntry.h中已定义)
- `navcanin_t` (在INS912AlgorithmEntry.h中已定义)
- `arraytodata_t` (在insTestingEntry.h中已定义)
- `gdwrxdata912_t` (在gdtypedefine.h中已定义)
- `SysVar_t` vs `SysVar` (在GLOBALDATA.h中已定义)

#### 3.2 解决方案

**方案1：使用现有类型定义**
```c
// 在InsTestingEntry_Compat.c中
#include "INS912AlgorithmEntry.h"  // 获取navoutdata_t定义
#include "insTestingEntry.h"       // 获取arraytodata_t定义
#include "gdtypedefine.h"          // 获取gdwrxdata912_t定义
#include "GLOBALDATA.h"            // 获取SysVar定义

// 使用现有的全局变量
extern navoutdata_t gnavout;       // 已在INS912AlgorithmEntry.h中声明
extern arraytodata_t ginputdata;   // 已在insTestingEntry.h中声明
extern gdwrxdata912_t gins912data; // 已在insTestingEntry.h中声明
extern SysVar g_SysVar;            // 已在GLOBALDATA.h中声明
```

**方案2：重命名冲突的类型**
```c
// 在InsTestingEntry_Compat.h中
typedef struct navoutdata_t compat_navoutdata_t;
typedef struct gdwrxdata912_t compat_gdwrxdata912_t;
// ... 其他类型
```

### 第四步：函数接口适配

#### 4.1 串口发送函数适配

现有函数：`void uart4sendmsg(char *msg, int len)`
兼容函数：`void uart4sendmsg_compat(char *msg, uint16_t len)`

```c
void uart4sendmsg_compat(char *msg, uint16_t len)
{
    uart4sendmsg(msg, (int)len);
}
```

#### 4.2 算法函数适配

现有函数：`void AlgorithmDo()`
兼容函数：`void AlgorithmDo_compat(void)`

```c
void AlgorithmDo_compat(void)
{
    AlgorithmDo();  // 调用现有函数
    fpga_loop_count++;  // 添加计数器
}
```

### 第五步：完整启用

1. **取消main.c中的注释**
   ```c
   #include "INS_App_Init.h"
   #include "InsTestingEntry_Compat.h"
   #include "SetParaBao.h"
   #include "bsp_sd_fatfs.h"
   
   // 在main函数中
   if (INS_App_Init() != 0) {
       // 错误处理
   }
   
   // 在主循环中
   INS_App_MainLoop();
   INS_App_StatusMonitor();
   ```

2. **配置功能开关**
   ```c
   // 在app_config.h中
   #define ENABLE_FLASH_OPERATIONS     1
   #define ENABLE_SD_FATFS             1
   #define ENABLE_PARAMETER_CONFIG     1
   #define ENABLE_PROTOCOL_11BB        1
   #define ENABLE_PROTOCOL_00BB        1
   ```

## 调试建议

### 1. 编译错误处理

- **类型重定义错误**：检查头文件包含顺序
- **函数声明冲突**：使用不同的函数名
- **变量重定义**：使用extern声明

### 2. 运行时调试

- **Flash操作测试**：先测试读写小块数据
- **SD卡测试**：检查CH378连接状态
- **串口输出**：验证数据格式正确性

### 3. 性能监控

- **内存使用**：检查栈和堆使用情况
- **CPU占用**：监控算法处理时间
- **数据吞吐**：验证数据输出频率

## 常见问题

### Q1: 编译时出现类型重定义错误
**A1**: 检查头文件包含顺序，使用条件编译或重命名类型

### Q2: 链接时出现符号重定义错误
**A2**: 检查是否有重复的全局变量定义，使用extern声明

### Q3: 运行时数据格式错误
**A3**: 检查数据结构对齐和字节序，确保与原有格式兼容

### Q4: SD卡操作失败
**A4**: 检查CH378连接，确认文件系统初始化成功

## 测试验证

### 1. 单元测试
```c
// 调用测试函数
run_all_tests();
```

### 2. 集成测试
- 验证Flash读写功能
- 验证SD卡文件操作
- 验证参数配置保存
- 验证数据输出格式

### 3. 性能测试
```c
performance_test();
```

## 总结

迁移工作已基本完成，主要挑战是解决类型定义冲突。建议按照上述步骤逐步启用功能，确保每一步都能正常编译和运行。
