/**
 * @file hpm6750_compat.h
 * @brief HPM6750项目升级和版本查询模块完整移植头文件
 * <AUTHOR> from HPM6750_INS-370M-SD-OK project
 * @date 2024
 */

#ifndef __HPM6750_COMPAT_H__
#define __HPM6750_COMPAT_H__

#include "stdint.h"
#include "UartDefine.h"

#ifdef __cplusplus
extern "C" {
#endif

// 移植自HPM6750的命令定义
#define SETPARA_TYPE0_output        0x0001  // 输出参数
#define SETPARA_TYPE0_baud          0x0002  // 波特率
#define SETPARA_TYPE0_frequency     0x0003  // 数据输出频率
#define SETPARA_TYPE0_gnss          0x0004  // 设置GNSS杆臂参数
#define SETPARA_TYPE0_angle         0x0005  // 设置天线安装角度
#define SETPARA_TYPE0_vector        0x0006  // 惯导-后轮轴中心位置矢量
#define SETPARA_TYPE0_deviation     0x0007  // 惯导角度安装偏差
#define SETPARA_TYPE0_initvalue     0x0008  // GNSS初始值
#define SETPARA_TYPE0_coord         0x0009  // 设置用户坐标轴
#define SETPARA_TYPE0_offsetime     0x000A  // 设置静态测零偏时间
#define SETPARA_TYPE0_transfer      0x000B  // 设置透传协议
#define SETPARA_TYPE0_solidify      0x000C  // 固化参数
#define SETPARA_TYPE0_factory       0x000D  // 恢复出厂设置
#define SETPARA_TYPE0_setpara       0x000E  // 设置所有参数
#define SETPARA_TYPE0_readpara      0x000F  // 参数回读
#define SETPARA_TYPE0_readver       0x0010  // 读取版本号
#define SETPARA_TYPE0_gps           0x0011  // GPS中断类型
#define SETPARA_TYPE0_type          0x0012  // 数据输出Type
#define SETPARA_TYPE0_debug         0x0013  // 是否开启Debug模式
#define SETPARA_TYPE0_gyro          0x0014  // 陀螺类型
#define SETPARA_TYPE0_calibration   0x0015  // 标定参数
#define SETPARA_TYPE0_temoffset     0x0016  // 温度补偿
#define SETPARA_TYPE0_KalmanQ       0x0017  // 卡尔曼滤波Q矩阵
#define SETPARA_TYPE0_KalmanR       0x0018  // 卡尔曼滤波R矩阵
#define SETPARA_TYPE0_filter        0x0019  // 间接滤波校正系数
#define SETPARA_TYPE0_FactorGyro    0x001A  // 陀螺标定因数
#define SETPARA_TYPE0_FactorAcc     0x001B  // 加计标定因数

// 升级相关命令定义
#define SETPARA_TYPE0_UPDATE_START  0x0020  // 软件升级开始命令
#define SETPARA_TYPE0_UPDATE_SEND   0x0021  // 发送升级包命令
#define SETPARA_TYPE0_UPDATE_END    0x0022  // 升级包完成命令
#define SETPARA_TYPE0_UPDATE_STOP   0x0023  // 升级终止命令
#define SETPARA_TYPE0_DATA_SD       0x0024  // SD卡操作命令

// 升级相关的类型定义
#define SETPARA_TYPE1_UPDATE_START  0x0120  // 升级开始类型
#define SETPARA_TYPE1_UPDATE_SEND   0x0121  // 升级发送类型
#define SETPARA_TYPE1_UPDATE_END    0x0122  // 升级结束类型
#define SETPARA_TYPE1_UPDATE_STOP   0x0123  // 升级停止类型
#define SETPARA_TYPE1_DATA_SD       0x0124  // SD卡操作类型

// 数据结构定义（移植自HPM6750）
typedef struct {
    uint8_t rxbuffer[1024];
    uint16_t rxlen;
} dmauart_t, *p_dmauart_t;

// 升级数据包结构
typedef struct {
    uint16_t BaoIndex;      // 包索引
    uint16_t TotalBao;      // 总包数
    uint8_t Length;         // 数据长度
    uint8_t UpdateData[256]; // 升级数据
} parabag_UpdateSend_info;

typedef struct {
    uint8_t head[3];        // 帧头
    uint16_t type;          // 类型
    uint16_t length;        // 长度
    parabag_UpdateSend_info info;
    uint8_t check;          // 校验
    uint8_t tail[2];        // 帧尾
} parabag_UpdateSend;

// 升级响应包结构
typedef struct {
    uint16_t BaoIndex;      // 包索引
    uint8_t BackFlag;       // 反馈标志
    uint8_t Count;          // 计数
} parabag_UpdateSend_back_info;

typedef struct {
    uint8_t head[3];        // 帧头
    uint16_t type;          // 类型
    uint16_t length;        // 长度
    parabag_UpdateSend_back_info info;
    uint8_t check;          // 校验
    uint8_t tail[2];        // 帧尾
} parabag_UpdateSend_back;

// 升级结束包结构
typedef struct {
    uint8_t BackFlag;       // 反馈标志
} parabag_UpdateEnd_back_info;

typedef struct {
    uint8_t head[3];        // 帧头
    uint16_t type;          // 类型
    uint16_t length;        // 长度
    parabag_UpdateEnd_back_info info;
    uint8_t check;          // 校验
    uint8_t tail[2];        // 帧尾
} parabag_UpdateEnd_back;

// 升级停止包结构
typedef struct {
    uint8_t BackFlag;       // 反馈标志
} parabag_UpdateStop_back_info;

typedef struct {
    uint8_t head[3];        // 帧头
    uint16_t type;          // 类型
    uint16_t length;        // 长度
    parabag_UpdateStop_back_info info;
    uint8_t check;          // 校验
    uint8_t tail[2];        // 帧尾
} parabag_UpdateStop_back;

// 通用设置类型结构
typedef struct {
    uint8_t head[3];        // 帧头
    uint16_t type;          // 类型
    uint16_t length;        // 长度
    uint8_t data[256];      // 数据
    uint8_t check;          // 校验
    uint8_t tail[2];        // 帧尾
} parabag_SetType;

// SD卡操作结构
typedef struct {
    uint8_t SetFlieType;    // 文件类型
    uint8_t SetOperateType; // 操作类型
} parabag_SetSd_info;

typedef struct {
    uint8_t head[3];        // 帧头
    uint16_t type;          // 类型
    uint16_t length;        // 长度
    parabag_SetSd_info info;
    uint8_t check;          // 校验
    uint8_t tail[2];        // 帧尾
} parabag_SetSd;

// 全局变量声明
extern uint8_t g_StartUpdateFirm;      // 升级模式标志
extern uint8_t g_UpdateBackFlag;       // 升级反馈标志
extern uint8_t g_UpdateSuccessful;     // 升级成功标志
extern uint8_t g_ucSystemResetFlag;    // 系统重启标志

// 函数声明（移植自HPM6750）
void ParaUpdateHandle(uint8_t *pucBuf, uint16_t usIndex, uint16_t usTotalBao, uint8_t ucLen);
void UartDmaRecSetPara(p_dmauart_t pdmauart);
void SetParaUpdateStart(p_dmauart_t pdmauart);
void SetParaUpdateSend(p_dmauart_t pdmauart);
void SetParaUpdateEnd(p_dmauart_t pdmauart);
void SetParaUpdateStop(p_dmauart_t pdmauart);
void ReadVersion(p_dmauart_t pdmauart);
void analysisRxdata_enhanced(void);

// 辅助函数声明
void UpdateStart_SetHead(void *pstruct, uint16_t type, uint16_t len);
void UpdateStart_SetEnd(void *pstruct, uint16_t len);
void UpdateSend_SetHead(void *pstruct, uint16_t type, uint16_t len);
void UpdateSend_SetEnd(void *pstruct, uint16_t len);
void UpdateEnd_SetHead(void *pstruct, uint16_t type, uint16_t len);
void UpdateEnd_SetEnd(void *pstruct, uint16_t len);
void UpdateStop_SetHead(void *pstruct, uint16_t type, uint16_t len);
void UpdateStop_SetEnd(void *pstruct, uint16_t len);

// CRC校验函数
uint8_t crc_verify_8bit(uint8_t *data, uint16_t len);

// Flash操作函数（需要根据GD32F4xx实现）
void Drv_FlashErase(uint32_t addr);
void Drv_FlashWrite(uint8_t *data, uint32_t addr, uint16_t len);
void Drv_SystemReset(void);

// SD卡操作函数
void SdFileOperateTypeSet(uint8_t operate_type, uint8_t file_type);

// 串口发送函数（需要适配到当前项目）
extern void uart4sendmsg(char *txbuf, int size);

#ifdef __cplusplus
}
#endif

#endif /* __HPM6750_COMPAT_H__ */
