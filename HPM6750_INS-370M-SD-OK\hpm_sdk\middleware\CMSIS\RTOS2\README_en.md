# CMSIS-RTOS2 API Supported In HPM_SDK

## FreeRTOS

| Feature                       |  Supported |       Short Description      |
|-------------------------------|------------|------------------------------|
|Kernel Information and Control |      Y     | It provide version/system information and starts/controls the RTOS Kernel. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__KernelCtrl.html) |
|Thread Management              |      Y     | It define, create, and control thread functions.. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__ThreadMgmt.html) |
|Thread Flags                   |      Y     | It synchronize threads using flags. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__ThreadFlagsMgmt.html) |
|Event Flags                    |      Y     | It synchronize threads using event flags. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__EventFlags.html) |
|Generic Wait Functions         |      Y     | It wait for a certain period of time..  [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__Wait.html) |
|Timer Management               |      Y     | It create and control timer and timer callback functions.  [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__TimerMgmt.html) |
|Mutex Management               |      Y     | It synchronize resource access using Mutual Exclusion (Mutex). [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__MutexMgmt.html) |
|Semaphores                     |      Y     | It access shared resources simultaneously from different threads. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__SemaphoreMgmt.html) |
|Memory Pool                    |      Y     | It manage thread-safe fixed-size blocks of dynamic memory. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__PoolMgmt.html) |
|Message Queue                  |      Y     | It exchange messages between threads in a FIFO-like operation. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__Message.html) |


## RTThread

| Feature                       |  Supported |       Short Description      |
|-------------------------------|------------|------------------------------|
|Kernel Information and Control |      Y     | It provide version/system information and starts/controls the RTOS Kernel. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__KernelCtrl.html) |
|Thread Management              |      Y     | It define, create, and control thread functions.. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__ThreadMgmt.html) |
|Thread Flags                   |      Y     | It synchronize threads using flags. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__ThreadFlagsMgmt.html) |
|Event Flags                    |      Y     | It synchronize threads using event flags. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__EventFlags.html) |
|Generic Wait Functions         |      Y     | It wait for a certain period of time..  [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__Wait.html) |
|Timer Management               |      Y     | It create and control timer and timer callback functions.  [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__TimerMgmt.html) |
|Mutex Management               |      Y     | It synchronize resource access using Mutual Exclusion (Mutex). [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__MutexMgmt.html) |
|Semaphores                     |      Y     | It access shared resources simultaneously from different threads. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__SemaphoreMgmt.html) |
|Memory Pool                    |      Y     | It manage thread-safe fixed-size blocks of dynamic memory. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__PoolMgmt.html) |
|Message Queue                  |      Y     | It exchange messages between threads in a FIFO-like operation. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__Message.html) |


## ThreadX

| Feature                       |  Supported |       Short Description      |
|-------------------------------|------------|------------------------------|
|Kernel Information and Control |      Y     | It provide version/system information and starts/controls the RTOS Kernel. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__KernelCtrl.html) |
|Thread Management              |      Y     | It define, create, and control thread functions.. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__ThreadMgmt.html) |
|Thread Flags                   |      N     | It synchronize threads using flags. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__ThreadFlagsMgmt.html) |
|Event Flags                    |      Y     | It synchronize threads using event flags. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__EventFlags.html) |
|Generic Wait Functions         |      Y     | It wait for a certain period of time..  [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__Wait.html) |
|Timer Management               |      Y     | It create and control timer and timer callback functions.  [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__TimerMgmt.html) |
|Mutex Management               |      Y     | It synchronize resource access using Mutual Exclusion (Mutex). [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__MutexMgmt.html) |
|Semaphores                     |      Y     | It access shared resources simultaneously from different threads. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__SemaphoreMgmt.html) |
|Memory Pool                    |      Y     | It manage thread-safe fixed-size blocks of dynamic memory. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__PoolMgmt.html) |
|Message Queue                  |      Y     | It exchange messages between threads in a FIFO-like operation. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__Message.html) |

## MicriumOS(ucOS-III)

| Feature                       |  Supported |       Short Description      |
|-------------------------------|------------|------------------------------|
|Kernel Information and Control |      Y     | It provide version/system information and starts/controls the RTOS Kernel. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__KernelCtrl.html) |
|Thread Management              |      Y     | It define, create, and control thread functions.. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__ThreadMgmt.html) |
|Thread Flags                   |      Y     | It synchronize threads using flags. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__ThreadFlagsMgmt.html) |
|Event Flags                    |      Y     | It synchronize threads using event flags. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__EventFlags.html) |
|Generic Wait Functions         |      Y     | It wait for a certain period of time..  [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__Wait.html) |
|Timer Management               |      N     | It create and control timer and timer callback functions.  [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__TimerMgmt.html) |
|Mutex Management               |      Y     | It synchronize resource access using Mutual Exclusion (Mutex). [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__MutexMgmt.html) |
|Semaphores                     |      Y     | It access shared resources simultaneously from different threads. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__SemaphoreMgmt.html) |
|Memory Pool                    |      N     | It manage thread-safe fixed-size blocks of dynamic memory. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__PoolMgmt.html) |
|Message Queue                  |      Y     | It exchange messages between threads in a FIFO-like operation. [More...](https://www.keil.com/pack/doc/cmsis/RTOS2/html/group__CMSIS__RTOS__Message.html) |
