# Copyright (c) 2023 HPMicro
# SPDX-License-Identifier: BSD-3-Clause
flash bank xpi0 hpm_xpi 0x80000000 0x2000000 1 1 $_TARGET0 0xF3000000 0x7

proc init_clock {} {
    $::_TARGET0 riscv dmi_write 0x39 0xF4000800
    $::_TARGET0 riscv dmi_write 0x3C 0xFFFFFFFF

    $::_TARGET0 riscv dmi_write 0x39 0xF4000810
    $::_TARGET0 riscv dmi_write 0x3C 0xFFFFFFFF

    $::_TARGET0 riscv dmi_write 0x39 0xF4000820
    $::_TARGET0 riscv dmi_write 0x3C 0xFFFFFFFF

    $::_TARGET0 riscv dmi_write 0x39 0xF4000830
    $::_TARGET0 riscv dmi_write 0x3C 0xFFFFFFFF
    echo "clocks has been enabled!"
}

proc init_ddr3 {} {
# ddr dcdc setup
    $::_TARGET0 riscv dmi_write 0x39 0xF4104080
    $::_TARGET0 riscv dmi_write 0x3C 0x10578

# ddr3 setup
    $::_TARGET0 riscv dmi_write 0x39 0xF40C0180
    $::_TARGET0 riscv dmi_write 0x3C 0x30000019
    $::_TARGET0 riscv dmi_write 0x39 0xF400180C
    $::_TARGET0 riscv dmi_write 0x3C 0x09100401

    $::_TARGET0 riscv dmi_write 0x39 0xF4153000
    $::_TARGET0 riscv dmi_write 0x3C 0xF0000010

    $::_TARGET0 riscv dmi_write 0x39 0xF30101B0
    $::_TARGET0 riscv dmi_write 0x3C 0

    $::_TARGET0 riscv dmi_write 0x39 0xF4150040
    $::_TARGET0 riscv dmi_write 0x3C 0xf004641f

    $::_TARGET0 riscv dmi_write 0x39 0xF4153000
    $::_TARGET0 riscv dmi_write 0x3C 0xf0000011

    $::_TARGET0 riscv dmi_write 0x39 0xF3013000
    $::_TARGET0 riscv dmi_write 0x3C 0xf4000000

    $::_TARGET0 riscv dmi_write 0x39 0xF3010490
    $::_TARGET0 riscv dmi_write 0x3C 1

    $::_TARGET0 riscv dmi_write 0x39 0xF3010000
    $::_TARGET0 riscv dmi_write 0x3C 0x1040001

    $::_TARGET0 riscv dmi_write 0x39 0xF30100D0
    $::_TARGET0 riscv dmi_write 0x3C 0x4002004e

    $::_TARGET0 riscv dmi_write 0x39 0xF3010110
    $::_TARGET0 riscv dmi_write 0x3C 0x05010407
    $::_TARGET0 riscv dmi_write 0x39 0xF3010190
    $::_TARGET0 riscv dmi_write 0x3C 0x07040102
    $::_TARGET0 riscv dmi_write 0x39 0xF3010194
    $::_TARGET0 riscv dmi_write 0x3C 0x20404
    $::_TARGET0 riscv dmi_write 0x39 0xF30101A4
    $::_TARGET0 riscv dmi_write 0x3C 0x20008
    $::_TARGET0 riscv dmi_write 0x39 0xF3010240
    $::_TARGET0 riscv dmi_write 0x3C 0x06000600

    $::_TARGET0 riscv dmi_write 0x39 0xF3010200
    $::_TARGET0 riscv dmi_write 0x3C 0x1F1F1F
    $::_TARGET0 riscv dmi_write 0x39 0xF3010204
    $::_TARGET0 riscv dmi_write 0x3C 0x121212
    $::_TARGET0 riscv dmi_write 0x39 0xF3010208
    $::_TARGET0 riscv dmi_write 0x3C 0
    $::_TARGET0 riscv dmi_write 0x39 0xF301020C
    $::_TARGET0 riscv dmi_write 0x3C 0
    $::_TARGET0 riscv dmi_write 0x39 0xF3010210
    $::_TARGET0 riscv dmi_write 0x3C 0x1F1F
    $::_TARGET0 riscv dmi_write 0x39 0xF3010214
    $::_TARGET0 riscv dmi_write 0x3C 0x06030303
    $::_TARGET0 riscv dmi_write 0x39 0xF3010218
    $::_TARGET0 riscv dmi_write 0x3C 0x0F060606

    $::_TARGET0 riscv dmi_write 0x39 0xF3013000
    $::_TARGET0 riscv dmi_write 0x3C 0xFC000000

    $::_TARGET0 riscv dmi_write 0x39 0xF4150054
    $::_TARGET0 riscv dmi_write 0x3C 0xc70
    $::_TARGET0 riscv dmi_write 0x39 0xF4150058
    $::_TARGET0 riscv dmi_write 0x3C 0x6
    $::_TARGET0 riscv dmi_write 0x39 0xF415005c
    $::_TARGET0 riscv dmi_write 0x3C 0x18
    $::_TARGET0 riscv dmi_write 0x39 0xF4150048
    $::_TARGET0 riscv dmi_write 0x3C 0x919c8866
    $::_TARGET0 riscv dmi_write 0x39 0xF415004c
    $::_TARGET0 riscv dmi_write 0x3C 0x1a838360
    $::_TARGET0 riscv dmi_write 0x39 0xF415008c
    $::_TARGET0 riscv dmi_write 0x3C 0xf06d50
    $::_TARGET0 riscv dmi_write 0x39 0xF4150050
    $::_TARGET0 riscv dmi_write 0x3C 0x3002d200


    $::_TARGET0 riscv dmi_write 0x39 0xF30101b0
    $::_TARGET0 riscv dmi_write 0x3C 1
    sleep 100

    $::_TARGET0 riscv dmi_write 0x39 0xF4150068
    $::_TARGET0 riscv dmi_write 0x3C 0x930035C7
    $::_TARGET0 riscv dmi_write 0x39 0xF4150004
    $::_TARGET0 riscv dmi_write 0x3C 0xFF81
    sleep 200

    echo "ddr3 has been enabled!"
}

proc init_dram {} {
# ddr dcdc setup
    $::_TARGET0 riscv dmi_write 0x39 0xF4104080
    $::_TARGET0 riscv dmi_write 0x3C 0x10708

# pll1 setup
    $::_TARGET0 riscv dmi_write 0x39 0xF40c0180
    $::_TARGET0 riscv dmi_write 0x3C 0xb0000016
    $::_TARGET0 riscv dmi_write 0x39 0xF40c0184
    $::_TARGET0 riscv dmi_write 0x3C 0
    $::_TARGET0 riscv dmi_write 0x39 0xF40c0188
    $::_TARGET0 riscv dmi_write 0x3C 0xe4e1c00

#ddr setup
    $::_TARGET0 riscv dmi_write 0x39 0xF3010000
    $::_TARGET0 riscv dmi_write 0x3C 0x3040000

    $::_TARGET0 riscv dmi_write 0x39 0xF30101B0
    $::_TARGET0 riscv dmi_write 0x3C 0

    $::_TARGET0 riscv dmi_write 0x39 0xF4150044
    $::_TARGET0 riscv dmi_write 0x3C 0x40a

    $::_TARGET0 riscv dmi_write 0x39 0xF4150040
    $::_TARGET0 riscv dmi_write 0x3C 0xf004641f

    $::_TARGET0 riscv dmi_write 0x39 0xF4153000
    $::_TARGET0 riscv dmi_write 0x3C 0xf0000011

    $::_TARGET0 riscv dmi_write 0x39 0xF3013000
    $::_TARGET0 riscv dmi_write 0x3C 0xf4000000

    $::_TARGET0 riscv dmi_write 0x39 0xF3010490
    $::_TARGET0 riscv dmi_write 0x3C 1

    $::_TARGET0 riscv dmi_write 0x39 0xF3010000
    $::_TARGET0 riscv dmi_write 0x3C 0x1040000
    $::_TARGET0 riscv dmi_write 0x39 0xF3010190
    $::_TARGET0 riscv dmi_write 0x3C 0x07010101
    $::_TARGET0 riscv dmi_write 0x39 0xF3010194
    $::_TARGET0 riscv dmi_write 0x3C 0x20404
    $::_TARGET0 riscv dmi_write 0x39 0xF30101A4
    $::_TARGET0 riscv dmi_write 0x3C 0x20008
    $::_TARGET0 riscv dmi_write 0x39 0xF3010240
    $::_TARGET0 riscv dmi_write 0x3C 0x6000600
    $::_TARGET0 riscv dmi_write 0x39 0xF3010200
    $::_TARGET0 riscv dmi_write 0x3C 0x1f1f1f
    $::_TARGET0 riscv dmi_write 0x39 0xF3010204
    $::_TARGET0 riscv dmi_write 0x3C 0x70707
    $::_TARGET0 riscv dmi_write 0x39 0xF3010208
    $::_TARGET0 riscv dmi_write 0x3C 0
    $::_TARGET0 riscv dmi_write 0x39 0xF301020c
    $::_TARGET0 riscv dmi_write 0x3C 0
    $::_TARGET0 riscv dmi_write 0x39 0xF3010210
    $::_TARGET0 riscv dmi_write 0x3C 0x1f1f
    $::_TARGET0 riscv dmi_write 0x39 0xF3010214
    $::_TARGET0 riscv dmi_write 0x3C 0x6060606
    $::_TARGET0 riscv dmi_write 0x39 0xF3010218
    $::_TARGET0 riscv dmi_write 0x3C 0xf0f0606

    $::_TARGET0 riscv dmi_write 0x39 0xF3013000
    $::_TARGET0 riscv dmi_write 0x3C 0xfc000000
    $::_TARGET0 riscv dmi_write 0x39 0xF4150020
    $::_TARGET0 riscv dmi_write 0x3C 0x3000100
    $::_TARGET0 riscv dmi_write 0x39 0xF4150028
    $::_TARGET0 riscv dmi_write 0x3C 0x18002356
    $::_TARGET0 riscv dmi_write 0x39 0xF415002c
    $::_TARGET0 riscv dmi_write 0x3C 0x0aac4156
    $::_TARGET0 riscv dmi_write 0x39 0xF4150054
    $::_TARGET0 riscv dmi_write 0x3C 0xe73
    $::_TARGET0 riscv dmi_write 0x39 0xF4150058
    $::_TARGET0 riscv dmi_write 0x3C 0x5
    $::_TARGET0 riscv dmi_write 0x39 0xF415005c
    $::_TARGET0 riscv dmi_write 0x3C 0
    $::_TARGET0 riscv dmi_write 0x39 0xF4150048
    $::_TARGET0 riscv dmi_write 0x3C 0xf2adfe53
    $::_TARGET0 riscv dmi_write 0x39 0xF415004c
    $::_TARGET0 riscv dmi_write 0x3C 0x22820362
    $::_TARGET0 riscv dmi_write 0x39 0xF4150050
    $::_TARGET0 riscv dmi_write 0x3C 0x30020100
    $::_TARGET0 riscv dmi_write 0x39 0xF415008c
    $::_TARGET0 riscv dmi_write 0x3C 0xf06d50

    $::_TARGET0 riscv dmi_write 0x39 0xF30101b0
    $::_TARGET0 riscv dmi_write 0x3C 1
    sleep 100

    $::_TARGET0 riscv dmi_write 0x39 0xF4150068
    $::_TARGET0 riscv dmi_write 0x3C 0x91003587
    $::_TARGET0 riscv dmi_write 0x39 0xF4150004
    $::_TARGET0 riscv dmi_write 0x3C 0xF501
    sleep 200
    echo "ddr has been enabled!"
}

$_TARGET0 configure -event reset-init {
    init_clock
    init_ddr3
}

$_TARGET0 configure -event gdb-attach {
    reset halt
}
