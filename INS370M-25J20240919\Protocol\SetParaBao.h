//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：SetParaBao.h
// 文件标识：
// 文件摘要：参数配置和固件升级处理
//
// 当前版本：V1.0
// 作    者：迁移自HPM6750项目
// 完成时间：2024.12.19
//---------------------------------------------------------

#ifndef __SETPARABAO_H
#define __SETPARABAO_H

#include "gd32f4xx.h"
#include <stdint.h>
#include <string.h>

// 参数设置相关定义
#define SETPARA_DATAOUT_FPGA_FREQ   200     // FPGA数据输出频率

// 升级相关定义
#define APP_UPDATE_ADDRESS          0x32000 // 升级程序存储地址

// 参数设置结构体
typedef struct {
    uint16_t Setfre;            // 设置频率
    uint32_t Setbaud;           // 设置波特率
    uint8_t SetDataOutType;     // 设置数据输出类型
    // 可以根据需要添加更多参数
} SetPara_t;

// 升级状态定义
extern uint8_t g_UpdateBackFlag;       // 升级反馈标志
extern uint8_t g_StartUpdateFirm;      // 开始升级固件标志

// 全局参数设置变量
extern SetPara_t stSetPara;

// 参数设置和升级相关函数
void ParaUpdateHandle(uint8_t *pucBuf, uint16_t usIndex, uint16_t usTotalBao, uint8_t ucLen);
void SetParaInit(void);
void SetParaDefault(void);
void SetParaSave(void);
void SetParaLoad(void);

// 参数配置处理
void ProcessSetFrequency(uint16_t freq);
void ProcessSetBaudrate(uint32_t baudrate);
void ProcessSetDataOutType(uint8_t type);

// 升级状态管理
void UpdateFirmwareStart(void);
void UpdateFirmwareStop(void);
uint8_t GetUpdateStatus(void);

#endif // __SETPARABAO_H
