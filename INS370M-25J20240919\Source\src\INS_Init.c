/*!
    \file  INS_Init.c
    \brief led spark with systick 
*/
#ifndef  __GOL_ALGORITHM_C__
#define  __GOL_ALGORITHM_C__
#include "appmain.h"
//#include "nav_includes.h"
//#include "frame_analysis.h"
#include "gdtypedefine.h"
#include "INS912AlgorithmEntry.h"
#include "api_ch392.h"

#include "ins.h"

uint32_t g_bmp280_measTime;
uint8_t g_usb_ready;
char g_logFileName[256] = {0};
LogBufTypeDef g_LogBuf;
LogBufTypeDef* g_pLogBuf = &g_LogBuf;
int g_networkState = -1;
uint8_t fisrtTimeGNSSTimeSync = 1;
uint8_t fpga_syn = 0;//fpga同步
uint32_t fpga_syn_count =0;//fpga_syn硬触发计数
uint32_t fpga_loop_count =0;//fpga_syn主循环获取数据帧计数
int gcan0_rx_syn = 0;//can0 synchronization
int g_gpsWeek;							//GPS周内秒		周
double g_gpsSecond;						//GPS周内秒		秒
//unsigned short gfpgadata[200];
uint32_t g_CAN_Timeout_Start_flag = 0;
uint32_t g_CAN_Timeout_Cnt = 0;

uint8_t g_CAN_Count_Last = 0;

uint8_t g_KF_OutData_Rx_Flag;

//unsigned int gprotocol_send_baudrate = BAUD_RATE_921600;	//BAUD_RATE_115200, BAUD_RATE_460800, BAUD_RATE_921600
float calcGPRMC_TRA(char *pchar);
void wheel_is_running(void);
void NAV_Output(void);
void INS912_Output(navoutdata_t *pnavout);

/*!
    \brief      check whether the TRNG module is ready
    \param[in]  none
    \param[out] none
    \retval     ErrStatus: SUCCESS or ERROR
*/
ErrStatus trng_ready_check(void)
{
	uint32_t timeout = 0;
	FlagStatus trng_flag = RESET;
	ErrStatus reval = SUCCESS;

	/* check wherther the random data is valid */
	do{
		timeout++;
		trng_flag = trng_flag_get(TRNG_FLAG_DRDY);
	}while((RESET == trng_flag) &&(0xFFFF > timeout));

	if(RESET == trng_flag)
	{   
		/* ready check timeout */
		trng_flag = trng_flag_get(TRNG_FLAG_CECS);
		trng_flag = trng_flag_get(TRNG_FLAG_SECS);
		reval = ERROR;
	}

	/* return check status */
	return reval;
}

/*!
    \brief      configure TRNG module
    \param[in]  none
    \param[out] none
    \retval     ErrStatus: SUCCESS or ERROR
*/
ErrStatus trng_configuration(void)
{
	ErrStatus reval = SUCCESS;

	/* TRNG module clock enable */
	rcu_periph_clock_enable(RCU_TRNG);

	/* TRNG registers reset */
	trng_deinit();
	trng_enable();
	/* check TRNG work status */
	reval = trng_ready_check();

	return reval;
}

#if 0
char gmsgbuf[8];
unsigned short gmsgbuf16[4];
union {
	unsigned short bd[4];
	float fv;
	double dv;
	unsigned int iv;
} m16_uMemory;
float get_16bit_D32(unsigned short *msgbuff)
{   
    m16_uMemory.bd[0] = msgbuff[0];
    m16_uMemory.bd[1] = msgbuff[1];
    return m16_uMemory.fv;
}
#endif


char gmsgbuf[8];
unsigned short gmsgbuf16[4];
union {
	unsigned short bd[4];
	float fv;
	double dv;
	unsigned int iv;
} m16_uMemory;
double get_16bit_D64(unsigned short *msgbuff)
{        
	#if 1
    m16_uMemory.bd[0] = msgbuff[0];
    m16_uMemory.bd[1] = msgbuff[1];
    m16_uMemory.bd[2] = msgbuff[2];
    m16_uMemory.bd[3] = msgbuff[3];
	#else
    m16_uMemory.bd[0] = msgbuff[3];
    m16_uMemory.bd[1] = msgbuff[2];
    m16_uMemory.bd[2] = msgbuff[1];
    m16_uMemory.bd[3] = msgbuff[0];
	#endif
    return m16_uMemory.dv;
}
float get_16bit_D32(unsigned short *msgbuff)
{   
    m16_uMemory.bd[0] = msgbuff[0];
    m16_uMemory.bd[1] = msgbuff[1];
    return m16_uMemory.fv;
}

unsigned int  get_16bit_Int32(unsigned short *msgbuff)
{   
    m16_uMemory.bd[0] = msgbuff[0];
    m16_uMemory.bd[1] = msgbuff[1];
    return m16_uMemory.iv;
}



void gd_eval_com_init(uint32_t com,uint32_t baudval);

void INS_Init(void)
{
	hw_interrupt_disable();
#ifdef BOOT_LOADER
    nvic_vector_table_set(NVIC_VECTTAB_FLASH, APP_LOADED_ADDR);
    __asm("CPSIE  I");	 //open ir…q
#endif
	if (RESET != rcu_flag_get(RCU_FLAG_FWDGTRST)) {	/* check if the system has resumed from FWDGT reset */
		rcu_all_reset_flag_clear();					/* clear the FWDGT reset flag */
	}
	
	delay_init(200);
	initializationdriversettings();
	gpio_mode_set(PWM_IO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLDOWN,PWM_IO_PIN);
	gpio_output_options_set(PWM_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,PWM_IO_PIN);
	bsp_gpio_init();
	bsp_tim_init();
	InitFlashAddr(0);		//bill -2023-06-25 cancel
	//InitFlashAddr(APP_SETTING_FLASH_OFFSET);
	//SetDefaultProductInfo();	//bill -2023-06-25 cancel --- reference comm_store_init()
	
	UM982_PowerON();				//开启GNSS电源
	Z_AXIS_5V_PowerON();			//开启光纤陀螺电源
	MEMS_3V3_PowerON();				//开启IMU电源
	ARM1_PowerON();					//开启ARM1电源
	
	exmc_asynchronous_sram_init();
	Uart_TxInit(UART_TXPORT_COMPLEX_8,UART_BAUDRATE_19200BPS,UART_PARITY_NONE,UART_STOPBIT_ONE,UART_RS232,UART_ENABLE);
	Uart_RxInit(UART_RXPORT_COMPLEX_8,UART_BAUDRATE_19200BPS,UART_PARITY_NONE,UART_STOPBIT_ONE,UART_RS232,UART_ENABLE);
	//bsp_systick_init01(NULL);
	mInitCH378Host();	//usb模块 沁恒
	
	//CAN1_STB_High();	//old
	CAN1_STB_Low();		//new
	CAN2_STB_High();
	
	CAN1_Enable();
	CAN2_Enable();
	
	bsp_rtc_init(pRTC);
	bsp_can_init(&hCAN0);
	bsp_can_init(&hCAN1);
	trng_configuration();
//	bsp_fwdgt_init();
	
	//TCPServer_Init();
	bsp_exti_init();
	
	g_bmp280_measTime = bmp280_init();
	
	memset(&hINSData,0,sizeof(hINSData));
	memset(&gdriverdatalist,0,sizeof(gdriverdatalist));

	
	
	
	//uart4 initialization...
	nvic_irq_enable(UART3_IRQn, 0, 0);
	nvic_irq_enable(UART4_IRQn, 0, 0);
	/* configure EVAL_COM4 */
    gd_eval_com_init(UART3, gprotocol_send_baudrate);
    gd_eval_com_init(UART4, gprotocol_send_baudrate);
    /* enable USART0 receive interrupt */
    usart_interrupt_enable(UART3, USART_INT_RBNE);
    usart_interrupt_enable(UART4, USART_INT_RBNE);

#ifdef DEVICE_ACC_TYPE_ADLX355
   ADXL355_UART7_Init();
#endif 


    /* enable USART0 transmit interrupt */
    //usart_interrupt_enable(UART4, USART_INT_TBE);
	
	comm_store_init();
	mDelaymS( 100 );
	if(RTC_BKP5 == CMD_BOOT) {
        RTC_BKP5 = 0;
        comm_send_end_frame(CMD_SET_FM_UPDATE);
    }
	
	g_LEDIndicatorState = LED_STATE_WHEEL_ERR_INIT_OK;
	mDelaymS( 100 ); 
	
	//ch392_init();
}

void GetChipID(void)
{
	hSetting.ChipID[0] = *(( volatile uint32_t * )0x1FFF7A10);
	hSetting.ChipID[1] = *(( volatile uint32_t * )0x1FFF7A14);
	hSetting.ChipID[2] = *(( volatile uint32_t * )0x1FFF7A18);
	hDefaultSetting.ChipID[0] = hSetting.ChipID[0];
	hDefaultSetting.ChipID[1] = hSetting.ChipID[1];
	hDefaultSetting.ChipID[2] = hSetting.ChipID[2];
}

void SetDefaultProductInfo(void)
{
	//InitFlashAddr(APP_SETTING_FLASH_OFFSET);	//bill - 2023-06-25 insert
	ReadFlash((uint8_t*)&hSetting,sizeof(AppSettingTypeDef));
	GetChipID();

	hSetting.ProductID				= PRODUCT_ID;
	hSetting.DeviceID				= DEVICE_ID;
	
	//strcpy(hSetting.ARM2_FW_Ver, "arm2-v0.0.1");
	//WriteFlash((uint8_t*)&hSetting,sizeof(AppSettingTypeDef));
	
	if(hSetting.firstProgram != FIRST_PROGRAM_BYTE)
	{
		hSetting.firstProgram = FIRST_PROGRAM_BYTE;
		//InitFlashAddr(APP_SETTING_FLASH_OFFSET);	//bill - 2023-06-25 insert
		WriteFlash((uint8_t*)&hSetting,sizeof(AppSettingTypeDef));
//		Sys_Soft_Reset();
	}

	hSetting.imuAxis = 0;
	hSetting.gInsSysStatus = INS_STATUS_INIT;
	hSetting.datamode = INS_DATA_MODE_0;
	
	//默认设置
	
	hDefaultSetting.serialFrameSetting[index_RS422].baudrate	= cfg_baud_115200;
	hDefaultSetting.serialFrameSetting[index_RS422].frameType	= cfg_format_GIPOT;
	hDefaultSetting.serialFrameSetting[index_RS422].freq		= cfg_freq_100Hz;
	
	hDefaultSetting.serialFrameSetting[index_RS232A].baudrate	= cfg_baud_115200;
	hDefaultSetting.serialFrameSetting[index_RS232A].frameType	= cfg_format_GPGGA;
	hDefaultSetting.serialFrameSetting[index_RS232A].freq		= cfg_freq_1Hz;
	
	hDefaultSetting.serialFrameSetting[index_RS232B].baudrate	= cfg_baud_9600;
	hDefaultSetting.serialFrameSetting[index_RS232B].frameType	= cfg_format_GPRMC;
	hDefaultSetting.serialFrameSetting[index_RS232B].freq		= cfg_freq_1Hz;

	// 初始化版本信息
	extern void comm_init_version_info(void);
	comm_init_version_info();
}



int checkUSBReady(void)
{
	UINT8  status;
	int i = 0;
	for( i = 0; i < 10; i ++ )
	{
		status = CH378DiskReady( );		/* 初始化磁盘并测试磁盘是否就绪 */
		if( status == ERR_SUCCESS ) 
		{
//			CH378HardwareReset();
			return 1;					/* 准备好 */
		}
		else if( status == ERR_DISK_DISCON ) 
		{
			return 0;					/* 检测到断开,重新检测并计时 */
		}
		if( CH378GetDiskStatus( ) >= DEF_DISK_MOUNTED && i >= 5 ) 
		{
			return 0;					/* 有的U盘总是返回未准备好,不过可以忽略,只要其建立连接MOUNTED且尝试5*50mS */
		}
	}
	return 0;
}

//int queryDiskCapacity(void)
//{
//	UINT8 status;
//	status = CH378DiskCapacity(cap);
//}

void loggingLogFile(void* arg)
{
	LogBufTypeDef* pBuf = (LogBufTypeDef*)arg;
	
	unsigned char logfileName[128] = {0};
	memset(logfileName,0,128);
	generateCSVLogFileName((char*)logfileName);
	writeCSVLog(logfileName,pBuf);
}

/* retarget the C library printf function to the USART */
int fputc(int ch, FILE *f)
{
	usart_data_transmit(USART0, (uint8_t)ch);
	while(RESET == usart_flag_get(USART0, USART_FLAG_TC));
	return ch;
}

//读取FPGA数据
void StartNavigation(void)
{
	//通知ARM1进行卡尔曼滤波解算
	ARM2_OUTPUT_ARM1_High();
	delay_us(5);
	ARM2_OUTPUT_ARM1_Low();

}

void StopNavigation(void)
{
	
}

void LEDIndicator(uint8_t state)
{
	switch(state)
	{
		case 0:
			break;
		case LED_STATE_WHEEL_OK_INIT_OK:	//1
			LED_STATE_ON();
			LED_WHEEL_ON();
			break;
		case LED_STATE_WHEEL_OK_INIT_ERR:	//2
			LED_STATE_OFF();
			LED_WHEEL_ON();
			break;
		case LED_STATE_WHEEL_ERR_INIT_OK:	//3
			LED_STATE_ON();
			LED_WHEEL_OFF();
			break;
		case LED_STATE_WHEEL_ERR_INIT_ERR:	//4
			LED_STATE_OFF();
			LED_WHEEL_OFF();
			break;
		default:
			break;
	}
}

/*!
    \brief      configure COM port
    \param[in]  COM: COM on the board
      \arg        EVAL_COM0: COM on the board
    \param[out] none
    \retval     none
*/
void gd_eval_com_init(uint32_t com,uint32_t baudval)
{
    /* enable GPIO clock */
    //uint32_t COM_ID = 0;
    //if(EVAL_COM0 == com)
    //{
    //    COM_ID = 0U;
    //}

    //rcu_periph_clock_enable( EVAL_COM0_GPIO_CLK);	//RCU_GPIOA
    rcu_periph_clock_enable( RCU_GPIOC);	//
    rcu_periph_clock_enable( RCU_GPIOD);	//

    /* enable USART clock */
    //rcu_periph_clock_enable(COM_CLK[COM_ID]);	//RCU_USART0
    rcu_periph_clock_enable(RCU_UART3);	//RCU_USART0
    rcu_periph_clock_enable(RCU_UART4);	//RCU_USART0

    /* connect port to USARTx_Tx */
    gpio_af_set(GPIOC, GPIO_AF_8, GPIO_PIN_10);
    gpio_af_set(GPIOC, GPIO_AF_8, GPIO_PIN_12);

    /* connect port to USARTx_Rx */
    gpio_af_set(GPIOD, GPIO_AF_8, GPIO_PIN_11);
    gpio_af_set(GPIOD, GPIO_AF_8, GPIO_PIN_2);

    /* configure USART Tx as alternate function push-pull */
    gpio_mode_set(GPIOC, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_10);
    gpio_output_options_set(GPIOC, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_10);
    gpio_mode_set(GPIOC, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_12);
    gpio_output_options_set(GPIOC, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_12);

    /* configure USART Rx as alternate function push-pull */
    gpio_mode_set(GPIOC, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_11);
    gpio_output_options_set(GPIOC, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_11);
    gpio_mode_set(GPIOD, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_2);
    gpio_output_options_set(GPIOD, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_2);

    /* USART configure */
    usart_deinit(UART3);
    usart_baudrate_set(UART3, baudval);
    usart_receive_config(UART3, USART_RECEIVE_ENABLE);
    usart_transmit_config(UART3, USART_TRANSMIT_ENABLE);
    usart_enable(UART3);
	//
    usart_deinit(com);
    usart_baudrate_set(com, baudval);
    usart_receive_config(com, USART_RECEIVE_ENABLE);
    usart_transmit_config(com, USART_TRANSMIT_ENABLE);
    usart_enable(com);
}


//int gberror = 0;
//unsigned short gpsssecond0 = 0;
//unsigned short gpsssecond1 = 0;
//int	gfpgasenddatalen = 0;
//unsigned int gframeindex = 0;
//#define OUT_GETFPGADATA_DEBUG_INFO	0
//#define FACTOR_FOG_GYRO           	300000.0

//#ifdef CMPL_CODE_EDWOY
//char r_testString[48] = {0};
//#endif 

//void get_fpgadata(void)
//{
//	//char debugbuff[1024]={0};
//	//char GPRMC_TRA[8];	
//#if !OUT_GETFPGADATA_DEBUG_INFO
//	int	i;
//	gfpgasenddatalen = *(unsigned short*)(0x60000000 + (0 << 1));
//	if (gfpgasenddatalen >= 1024)	return; 

//	unsigned short	checksum = 0;
//	for (i = 0; i < gfpgasenddatalen; i++) {			//从FPGA接受20 Word, 并转换为40个字符
//		gfpgadata[i] = *(unsigned short*)(0x60000000 + (i << 1));	//从FPGA读取16bit数据
//		//checksum += gfpgadata[i];
//	}

//	
//	gfpgadata[5]  = adlxdata[0];//再填mems
//	gfpgadata[6]  = adlxdata[1];
//	gfpgadata[7]  = adlxdata[2];
//	gfpgadata[8]  = adlxdata[3];
//	gfpgadata[9]  = adlxdata[4];
//	gfpgadata[10] = adlxdata[5];
//	
//	checksum = 0;
//	for (i = 0; i < gfpgasenddatalen; i++) //再计算校验码
//	{
//		checksum += gfpgadata[i];
//	}
//	
//	gfpgadata[i] = checksum;
//	gfpgadata[i + 1] = gframeindex++;
//	
//	gpagedata = *(fpgadata_t*)&gfpgadata;


////--update 2024.5.31----
//#ifdef CMPL_CODE_EDWOY
//  static uint32_t tmp_pace = 0;
//  tmp_pace++;
//  sprintf(r_testString,"\r\n\r\nsn-%d,gfpgadata[1]->HighByte = %d; gfpgadata[67]->LowByte = %d\r\n\r\n",
//                     tmp_pace,(uint8_t)(gfpgadata[1] >> 8), (uint8_t)gfpgadata[67]);

//  uart4sendmsg(r_testString,strlen(r_testString));
////  if(NULL == tmp_pace++ % 80) 
////  {    
////     uart4sendmsg(r_testString,strlen(r_testString));
////  }
//  
//#endif 

//	
////	memset(&hGPSData, 0, sizeof(hGPSData));

////	if (gpsssecond0 == gpagedata.hGPSData_gpssecond0 && gpsssecond1 == gpagedata.hGPSData_gpssecond1) {
////		gpsssecond0 = gpagedata.hGPSData_gpssecond0;
////		gpsssecond1 = gpagedata.hGPSData_gpssecond1;
////		gberror = 1;
////	}
////	gpsssecond0 = gpagedata.hGPSData_gpssecond0;
////	gpsssecond1 = gpagedata.hGPSData_gpssecond1;
//	
////	if (gberror) {
////		//gpagedata.hGPSData_gpssecond0 = 3691;
////		//gpagedata.hGPSData_gpssecond1 = 0;
////		gpagedata.Dtemp_data += 0;
////		gpagedata.Utemp_data += 90;
////	}
//	return;
//#endif
//}

float calcGPRMC_TRA(char *pchar)
{
	char tmpchar;
	float multiplier = 1.0;
	int i, bfind = 0;
	float tra = 0.0;
	for (i = 0; i < 5; i++) {
		if (*(pchar + i) == '.') {
			bfind = 1;
			break;
		}
	}
	if (bfind) {
		tmpchar = *(pchar + i + 1);
		if (tmpchar < '0' || tmpchar > '9')	return	888.8;
		else tra = (tmpchar - '0') * 0.1;
		
		for (i-- ; i >= 0; i--) {
			tmpchar = *(pchar + i);
			if (tmpchar < '0' || tmpchar > '9')	break;
			else tra += (tmpchar - '0') * multiplier;
			//multiplier *= 10.0;
		}
		return tra;
	}
	return 999.9;
}

void sys_irq_stop(void)
{
//    gd32_pin_irq_enable(FPGA_TO_ARM1_INT, PIN_IRQ_DISABLE);
//    gd32_pin_irq_enable(ARM1_TO_ARM2_IO, PIN_IRQ_DISABLE);
//    gd32_pin_irq_enable(FPGA_PPS_ARM1_INT, PIN_IRQ_DISABLE);
//    gd32_pin_irq_enable(FPGA_TX_TO_ARM1_INT, PIN_IRQ_DISABLE);
}

void sys_irq_restart(void)
{
//    gd32_pin_irq_enable(FPGA_TO_ARM1_INT, PIN_IRQ_ENABLE);
//    gd32_pin_irq_enable(ARM1_TO_ARM2_IO, PIN_IRQ_ENABLE);
//    gd32_pin_irq_enable(FPGA_PPS_ARM1_INT, PIN_IRQ_ENABLE);
//    gd32_pin_irq_enable(FPGA_TX_TO_ARM1_INT, PIN_IRQ_ENABLE);
}
#endif


