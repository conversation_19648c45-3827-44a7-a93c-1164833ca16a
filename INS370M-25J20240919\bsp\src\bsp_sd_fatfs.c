//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：bsp_sd_fatfs.c
// 文件标识：
// 文件摘要：SD卡FatFS文件系统操作实现
//
// 当前版本：V1.0
// 作    者：迁移自HPM6750项目
// 完成时间：2024.12.19
//---------------------------------------------------------

#include "bsp_sd_fatfs.h"
#include "FILE_SYS.H"  // 使用现有的CH378文件系统
#include "app_config.h"  // 包含配置文件

// 全局变量定义
uint8_t SetSdOperateType = 0;
uint8_t SetSdFlieType = 0;
SdDataSave_t gSdDataSave = {0};
ff_queue_t g_queue = {0};

// 文件句柄（使用CH378文件系统时可能不需要）
static uint8_t s_com3_file_opened = 0;
static uint8_t s_bb00_file_opened = 0;

/**
 * @brief SD卡FatFS初始化
 * @return 0: 成功, -1: 失败
 */
int Fatfs_Init(void)
{
#if ENABLE_SD_FATFS
    uint8_t status;

    // 检查CH378是否连接
    status = CH378CheckExist();
    if (status != ERR_SUCCESS) {
        return -1;
    }

    // 检查磁盘连接
    status = CH378DiskConnect();
    if (status != ERR_SUCCESS) {
        return -1;
    }

    // 初始化磁盘
    status = CH378DiskReady();
    if (status != ERR_SUCCESS) {
        return -1;
    }

    // 初始化队列
    ff_queue_init(&g_queue);

    return 0;
#else
    // SD卡功能已禁用
    return -1;  // 返回失败，表示SD卡不可用
#endif
}

/**
 * @brief SD卡初始化
 * @return 0: 成功, -1: 失败
 */
int sd_init(void)
{
#if ENABLE_SD_FATFS
    return Fatfs_Init();
#else
    // SD卡功能已禁用
    return -1;
#endif
}

/**
 * @brief SD卡去初始化
 * @return 0: 成功, -1: 失败
 */
int sd_deinit(void)
{
    // 关闭所有打开的文件
    CloseFileToSd(SD_FILE_TYPE_ALL);
    return 0;
}

/**
 * @brief 获取SD卡状态
 * @return SD卡状态
 */
uint8_t sd_card_status_get(void)
{
    uint8_t status = CH378DiskConnect();
    if (status == ERR_SUCCESS) {
        return SD_STATUS_OK;
    } else {
        return SD_STATUS_NO_CARD;
    }
}

/**
 * @brief 从SD卡打开数据文件，用于写属性
 * @param FlieType 文件类型
 */
void WriteFileOpenFromSd(uint8_t FlieType)
{
#if ENABLE_SD_FATFS
    if (FlieType == SD_FILE_TYPE_COM3 || FlieType == SD_FILE_TYPE_ALL) {
        WriteCom3FileOpenFromSd();
    }

    if (FlieType == SD_FILE_TYPE_BB00 || FlieType == SD_FILE_TYPE_ALL) {
        WriteBB00FileOpenFromSd();
    }
#else
    // SD卡功能已禁用，不执行任何操作
    (void)FlieType;  // 避免未使用参数警告
#endif
}

/**
 * @brief 从SD卡打开数据文件，用于读属性
 * @param FlieType 文件类型
 */
void ReadFileOpenFromSd(uint8_t FlieType)
{
    if (FlieType == SD_FILE_TYPE_COM3 || FlieType == SD_FILE_TYPE_ALL) {
        ReadCom3FileFromSd();
    }
    
    if (FlieType == SD_FILE_TYPE_BB00 || FlieType == SD_FILE_TYPE_ALL) {
        ReadBB00FileFromSd();
    }
}

/**
 * @brief 关闭SD卡文件
 * @param FlieType 文件类型
 */
void CloseFileToSd(uint8_t FlieType)
{
    if ((FlieType == SD_FILE_TYPE_COM3 || FlieType == SD_FILE_TYPE_ALL) && s_com3_file_opened) {
        CH378FileClose(1);  // 更新文件大小
        s_com3_file_opened = 0;
    }
    
    if ((FlieType == SD_FILE_TYPE_BB00 || FlieType == SD_FILE_TYPE_ALL) && s_bb00_file_opened) {
        CH378FileClose(1);  // 更新文件大小
        s_bb00_file_opened = 0;
    }
}

/**
 * @brief 写数据到SD卡文件
 * @param FlieType 文件类型
 */
void WriteFileToSd(uint8_t FlieType)
{
    if (FlieType == SD_FILE_TYPE_COM3 || FlieType == SD_FILE_TYPE_ALL) {
        WriteCom3FileToSd();
    }
    
    if (FlieType == SD_FILE_TYPE_BB00 || FlieType == SD_FILE_TYPE_ALL) {
        WriteBB00FileToSd();
    }
}

/**
 * @brief 从SD卡读取数据文件
 * @param FlieType 文件类型
 */
void ReadFileToSd(uint8_t FlieType)
{
    if (FlieType == SD_FILE_TYPE_COM3 || FlieType == SD_FILE_TYPE_ALL) {
        ReadCom3FileFromSd();
    }
    
    if (FlieType == SD_FILE_TYPE_BB00 || FlieType == SD_FILE_TYPE_ALL) {
        ReadBB00FileFromSd();
    }
}

/**
 * @brief 删除SD卡文件
 * @param FlieType 文件类型
 */
void DeleteFileFromSd(uint8_t FlieType)
{
    if (FlieType == SD_FILE_TYPE_COM3 || FlieType == SD_FILE_TYPE_ALL) {
        CH378FileErase("/COM3.DAT");
    }
    
    if (FlieType == SD_FILE_TYPE_BB00 || FlieType == SD_FILE_TYPE_ALL) {
        CH378FileErase("/BB00.DAT");
    }
}

/**
 * @brief 打开COM3写文件
 */
void WriteCom3FileOpenFromSd(void)
{
    uint8_t status;
    
    // 尝试打开文件，如果不存在则创建
    status = CH378FileOpen("/COM3.DAT");
    if (status != ERR_SUCCESS) {
        status = CH378FileCreate("/COM3.DAT");
    }
    
    if (status == ERR_SUCCESS) {
        s_com3_file_opened = 1;
        // 移动到文件末尾（追加模式）
        CH378ByteLocate(0xFFFFFFFF);
    }
}

/**
 * @brief 打开BB00写文件
 */
void WriteBB00FileOpenFromSd(void)
{
    uint8_t status;
    
    // 尝试打开文件，如果不存在则创建
    status = CH378FileOpen("/BB00.DAT");
    if (status != ERR_SUCCESS) {
        status = CH378FileCreate("/BB00.DAT");
    }
    
    if (status == ERR_SUCCESS) {
        s_bb00_file_opened = 1;
        // 移动到文件末尾（追加模式）
        CH378ByteLocate(0xFFFFFFFF);
    }
}

/**
 * @brief 写COM3数据到SD卡
 */
void WriteCom3FileToSd(void)
{
    // 这里需要根据实际的数据结构来实现
    // 示例代码，需要根据实际情况修改
    if (s_com3_file_opened) {
        uint8_t data[] = "COM3 Data Sample\r\n";
        uint16_t real_count;
        CH378ByteWrite(data, sizeof(data) - 1, &real_count);
    }
}

/**
 * @brief 写BB00数据到SD卡
 */
void WriteBB00FileToSd(void)
{
    // 这里需要根据实际的数据结构来实现
    // 示例代码，需要根据实际情况修改
    if (s_bb00_file_opened) {
        uint8_t data[] = "BB00 Data Sample\r\n";
        uint16_t real_count;
        CH378ByteWrite(data, sizeof(data) - 1, &real_count);
    }
}

/**
 * @brief 从SD卡读取COM3数据
 */
void ReadCom3FileFromSd(void)
{
    uint8_t status;
    uint8_t buffer[256];
    uint16_t real_count;
    
    status = CH378FileOpen("/COM3.DAT");
    if (status == ERR_SUCCESS) {
        CH378ByteRead(buffer, sizeof(buffer), &real_count);
        CH378FileClose(0);  // 不更新文件大小
        // 处理读取的数据...
    }
}

/**
 * @brief 从SD卡读取BB00数据
 */
void ReadBB00FileFromSd(void)
{
    uint8_t status;
    uint8_t buffer[256];
    uint16_t real_count;
    
    status = CH378FileOpen("/BB00.DAT");
    if (status == ERR_SUCCESS) {
        CH378ByteRead(buffer, sizeof(buffer), &real_count);
        CH378FileClose(0);  // 不更新文件大小
        // 处理读取的数据...
    }
}

/**
 * @brief 格式化SD卡
 */
void FormatSD(void)
{
    // CH378不直接支持格式化，这里可以删除所有文件来模拟
    DeleteFileFromSd(SD_FILE_TYPE_ALL);
}

/**
 * @brief SD卡文件系统操作类型处理
 * @param OperateType 操作类型
 * @param FlieType 文件类型
 */
void SdFileOperateTypeSet(uint8_t OperateType, uint8_t FlieType)
{
    SetSdOperateType = OperateType;
    SetSdFlieType = FlieType;
    
    switch (OperateType) {
        case SD_OPERATE_OPEN_WRITE:
            CloseFileToSd(FlieType);
            WriteFileOpenFromSd(FlieType);
            break;
            
        case SD_OPERATE_CLOSE_WRITE:
            CloseFileToSd(FlieType);
            break;
            
        case SD_OPERATE_OPEN_READ:
            CloseFileToSd(FlieType);
            ReadFileOpenFromSd(FlieType);
            break;
            
        case SD_OPERATE_CLOSE_READ:
            CloseFileToSd(FlieType);
            break;
            
        case SD_OPERATE_DELETE:
            CloseFileToSd(FlieType);
            DeleteFileFromSd(FlieType);
            break;
            
        default:
            break;
    }
}

/**
 * @brief SD卡文件系统写操作
 */
void SdFileWriteOperate(void)
{
    if (SetSdOperateType == SD_OPERATE_OPEN_WRITE) {
        WriteFileToSd(SetSdFlieType);
    }
}

/**
 * @brief SD卡文件系统读操作
 */
void SdFileReadOperate(void)
{
    if (SetSdOperateType == SD_OPERATE_OPEN_READ) {
        ReadFileToSd(SetSdFlieType);
    }
}

/**
 * @brief SD卡文件测试程序
 */
void SdFileTest(void)
{
    WriteBB00FileOpenFromSd();
    WriteBB00FileToSd();
    CloseFileToSd(SD_FILE_TYPE_BB00);
}

/**
 * @brief 显示错误字符串
 * @param error_code 错误代码
 * @return 错误字符串
 */
const char* show_error_string(int error_code)
{
    switch (error_code) {
        case ERR_SUCCESS:
            return "Success";
        case ERR_USB_UNKNOWN:
            return "USB Unknown Error";
        case ERR_DISK_DISCON:
            return "Disk Disconnected";
        default:
            return "Unknown Error";
    }
}

// ========== 队列处理功能（异步写入） ==========

/**
 * @brief 初始化队列
 * @param queue 队列指针
 * @return 0: 成功, -1: 失败
 */
int ff_queue_init(ff_queue_t *queue)
{
    if (queue == NULL) {
        return -1;
    }

    memset(queue, 0, sizeof(ff_queue_t));
    return 0;
}

/**
 * @brief 向队列添加项目
 * @param queue 队列指针
 * @param pdrv 驱动器号
 * @param buff 数据缓冲区
 * @param sector 扇区号
 * @param count 扇区数量
 * @return 0: 成功, -1: 失败
 */
int ff_queue_push(ff_queue_t *queue, uint8_t pdrv, const uint8_t *buff, uint32_t sector, uint32_t count)
{
    if (queue == NULL || queue->count >= 16) {
        return -1;  // 队列满
    }

    ff_queue_item_t *item = &queue->items[queue->tail];
    item->pdrv = pdrv;
    item->buff = buff;
    item->sector = sector;
    item->count = count;
    item->valid = 1;

    queue->tail = (queue->tail + 1) % 16;
    queue->count++;

    return 0;
}

/**
 * @brief 从队列取出项目
 * @param queue 队列指针
 * @param item 输出项目
 * @return 0: 成功, -1: 失败（队列空）
 */
int ff_queue_pop(ff_queue_t *queue, ff_queue_item_t *item)
{
    if (queue == NULL || item == NULL || queue->count == 0) {
        return -1;  // 队列空
    }

    *item = queue->items[queue->head];
    queue->items[queue->head].valid = 0;

    queue->head = (queue->head + 1) % 16;
    queue->count--;

    return 0;
}

/**
 * @brief 处理队列中的写入请求
 */
void ff_handle_poll(void)
{
    ff_queue_item_t item;

    // 处理队列中的所有项目
    while (ff_queue_pop(&g_queue, &item) == 0) {
        if (item.valid) {
            // 这里实现实际的写入操作
            // 由于使用CH378文件系统，需要根据实际情况调整
            // 示例：将数据写入到文件
            uint16_t real_count;
            CH378ByteWrite((uint8_t*)item.buff, item.count * 512, &real_count);
        }
    }
}
