# Copyright (c) 2021 HPMicro
# SPDX-License-Identifier: BSD-3-Clause
#


set _CHIP hpm6280
set _CPUTAPID 0x1000563D
jtag newtap $_CHIP cpu -irlen 5 -expected-id $_CPUTAPID

set _TARGET0 $_CHIP.cpu0
target create $_TARGET0 riscv -chain-position $_CHIP.cpu -coreid 0

$_TARGET0 configure -work-area-phys 0x00000000 -work-area-size 0x20000 -work-area-backup 0

targets $_TARGET0

proc dmi_write {reg value} {
    $::_TARGET0 riscv dmi_write ${reg} ${value}
}

proc dmi_read {reg} {
    set v [$::_TARGET0 riscv dmi_read ${reg}]
    return ${v}
}
proc dmi_write_memory {addr value} {
    dmi_write 0x39 ${addr}
    dmi_write 0x3C ${value}
}

proc dmi_read_memory {addr} {
    set sbcs [expr { 0x100000 | [dmi_read 0x38] }]
    dmi_write 0x38 ${sbcs}
    dmi_write 0x39 ${addr}
    set value [dmi_read 0x3C]
    return ${value}
}

proc release_core1 {} {
    dmi_write_memory 0xF4002C00 0x1000
}

set _TARGET1 $_CHIP.cpu1
target create $_TARGET1 riscv -chain-position $_CHIP.cpu -coreid 1
$_TARGET1 configure -work-area-phys 0x00000000 -work-area-size 0x20000 -work-area-backup 0

$_TARGET1 configure -event examine-start {
    release_core1
}

$_TARGET1 configure -event reset-deassert-pre {
    $::_TARGET0 arp_poll
    release_core1
}

proc reset_soc {} {
    $::_TARGET0 riscv dmi_write 0x39 0xF40C001C
    $::_TARGET0 riscv dmi_write 0x3C 24000000
}
