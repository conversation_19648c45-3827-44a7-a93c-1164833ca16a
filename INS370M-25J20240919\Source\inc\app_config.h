//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：app_config.h
// 文件标识：
// 文件摘要：应用层配置文件，管理迁移功能的开关
//
// 当前版本：V1.0
// 作    者：迁移自HPM6750项目
// 完成时间：2024.12.19
//---------------------------------------------------------

#ifndef __APP_CONFIG_H
#define __APP_CONFIG_H

// ========== 功能开关配置 ==========

// Flash功能开关
#define ENABLE_FLASH_OPERATIONS     1   // 启用Flash读写操作
#define ENABLE_FIRMWARE_UPDATE      1   // 启用固件升级功能

// SD卡功能开关
#define ENABLE_SD_FATFS             0   // 禁用SD卡FatFS文件系统
#define ENABLE_SD_ASYNC_WRITE       0   // 禁用SD卡异步写入
#define ENABLE_SD_DATA_LOG          0   // 禁用SD卡数据记录

// 参数配置功能开关
#define ENABLE_PARAMETER_CONFIG     1   // 启用参数配置功能
#define ENABLE_PARAMETER_SAVE       1   // 启用参数保存到Flash

// 通信协议功能开关
#define ENABLE_PROTOCOL_11BB        1   // 启用11BB协议（FPGA原始数据）
#define ENABLE_PROTOCOL_00BB        1   // 启用00BB协议（预处理数据）
#define ENABLE_SD_PLAYBACK          1   // 启用SD卡数据回放

// 坐标系转换功能开关
#define ENABLE_COORDINATE_TRANSFORM 1   // 启用坐标系转换
#define ENABLE_NAVIGATION_ALGORITHM 1   // 启用导航算法

// 调试功能开关
#define ENABLE_DEBUG_OUTPUT         1   // 启用调试输出
#define ENABLE_STATUS_MONITOR       1   // 启用状态监控
#define ENABLE_ERROR_HANDLER        1   // 启用错误处理

// ========== 硬件配置 ==========

// Flash配置
#define FLASH_SECTOR_SIZE           (16*1024)      // Flash扇区大小
#define FLASH_PARAM_SECTOR          6              // 参数存储扇区
#define FLASH_UPDATE_START_ADDR     0x32000        // 固件升级起始地址

// SD卡配置
#define SD_QUEUE_SIZE               16             // SD卡写入队列大小
#define SD_BUFFER_SIZE              512            // SD卡缓冲区大小
#define SD_MAX_FILENAME_LEN         64             // 最大文件名长度

// 串口配置
#define UART_OUTPUT_PORT            UART4          // 数据输出串口
#define UART_CONFIG_PORT            UART3          // 参数配置串口
#define UART_DEFAULT_BAUDRATE       460800         // 默认波特率

// ========== 算法配置 ==========

// 数据输出频率配置
#define FPGA_DATA_FREQ              200            // FPGA数据频率(Hz)
#define DEFAULT_OUTPUT_FREQ         100            // 默认输出频率(Hz)
#define MAX_OUTPUT_FREQ             1000           // 最大输出频率(Hz)

// 数据包配置
#define MAX_PACKET_SIZE             512            // 最大数据包大小
#define CHECKSUM_ENABLE             1              // 启用校验和

// ========== 系统配置 ==========

// 任务优先级配置
#define TASK_PRIORITY_HIGH          1              // 高优先级任务
#define TASK_PRIORITY_NORMAL        2              // 普通优先级任务
#define TASK_PRIORITY_LOW           3              // 低优先级任务

// 内存配置
#define STACK_SIZE_MAIN             2048           // 主任务栈大小
#define HEAP_SIZE                   4096           // 堆大小

// 定时器配置
#define TIMER_FPGA_SYNC             TIM0           // FPGA同步定时器
#define TIMER_STATUS_MONITOR        TIM1           // 状态监控定时器

// ========== 协议配置 ==========

// 数据包头定义
#define PACKET_HEADER_11BB          0x11BB         // 11BB协议包头
#define PACKET_HEADER_00BB          0x00BB         // 00BB协议包头
#define PACKET_HEADER_SYNC          0xBDDB         // 同步字

// 命令定义
#define CMD_SET_FREQUENCY           0x0001         // 设置频率命令
#define CMD_SET_BAUDRATE            0x0002         // 设置波特率命令
#define CMD_SET_OUTPUT_TYPE         0x0003         // 设置输出类型命令
#define CMD_START_UPDATE            0x0010         // 开始升级命令
#define CMD_STOP_UPDATE             0x0011         // 停止升级命令

// ========== 错误代码定义 ==========

#define ERROR_NONE                  0x00           // 无错误
#define ERROR_FLASH_INIT            0x01           // Flash初始化错误
#define ERROR_SD_INIT               0x02           // SD卡初始化错误
#define ERROR_UART_INIT             0x03           // 串口初始化错误
#define ERROR_ALGORITHM_INIT        0x04           // 算法初始化错误
#define ERROR_PARAMETER_INVALID     0x05           // 参数无效错误
#define ERROR_UPDATE_FAILED         0x06           // 升级失败错误

// ========== 状态定义 ==========

#define STATUS_INIT                 0x00           // 初始化状态
#define STATUS_RUNNING              0x01           // 运行状态
#define STATUS_ERROR                0x02           // 错误状态
#define STATUS_UPDATING             0x03           // 升级状态

// ========== 版本信息 ==========

#define APP_VERSION_MAJOR           1              // 主版本号
#define APP_VERSION_MINOR           0              // 次版本号
#define APP_VERSION_PATCH           0              // 补丁版本号

#define APP_VERSION_STRING          "V1.0.0"       // 版本字符串
#define APP_BUILD_DATE              __DATE__       // 编译日期
#define APP_BUILD_TIME              __TIME__       // 编译时间

// ========== 编译选项检查 ==========

#if !defined(ENABLE_FLASH_OPERATIONS) || !defined(ENABLE_PARAMETER_CONFIG)
#error "Flash operations and parameter config must be enabled"
#endif

#if ENABLE_SD_ASYNC_WRITE && !ENABLE_SD_FATFS
#error "SD async write requires SD FatFS to be enabled"
#endif

#if ENABLE_FIRMWARE_UPDATE && !ENABLE_FLASH_OPERATIONS
#error "Firmware update requires Flash operations to be enabled"
#endif

// ========== 调试宏定义 ==========

#if ENABLE_DEBUG_OUTPUT
#define DEBUG_PRINT(fmt, ...)       printf(fmt, ##__VA_ARGS__)
#else
#define DEBUG_PRINT(fmt, ...)       do {} while(0)
#endif

#if ENABLE_STATUS_MONITOR
#define STATUS_UPDATE(status)       g_system_status = (status)
#else
#define STATUS_UPDATE(status)       do {} while(0)
#endif

// ========== 全局变量声明 ==========

extern uint8_t g_system_status;        // 系统状态
extern uint32_t g_error_code;          // 错误代码

#endif // __APP_CONFIG_H
