//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：SetParaBao.c
// 文件标识：
// 文件摘要：参数配置和固件升级处理实现
//
// 当前版本：V1.0
// 作    者：迁移自HPM6750项目
// 完成时间：2024.12.19
//---------------------------------------------------------

#include "SetParaBao.h"
#include "bsp_flash.h"

// 全局变量定义
uint8_t g_UpdateBackFlag = 0;       // 升级反馈标志(0x01-正常/02-异常)
uint8_t g_StartUpdateFirm = 0;      // 开始升级固件标志
SetPara_t stSetPara = {0};          // 参数设置结构体

/**
 * @brief 参数设置初始化
 */
void SetParaInit(void)
{
    // 从Flash加载参数
    SetParaLoad();
    
    // 如果参数无效，使用默认值
    if (stSetPara.Setfre == 0 || stSetPara.Setfre > 1000) {
        SetParaDefault();
        SetParaSave();
    }
}

/**
 * @brief 设置默认参数
 */
void SetParaDefault(void)
{
    stSetPara.Setfre = 100;         // 默认频率100Hz
    stSetPara.Setbaud = 4608;       // 默认波特率460800 (4608 * 100)
    stSetPara.SetDataOutType = 1;   // 默认数据输出类型
}

/**
 * @brief 保存参数到Flash
 */
void SetParaSave(void)
{
    // 将参数保存到Flash的指定区域
    uint32_t flash_addr = ADDR_FMC_SECTOR_6;  // 使用扇区6保存参数
    
    // 擦除扇区
    norflash_erase_sector(flash_addr - FLASH_BASE);
    
    // 写入参数
    norflash_write(flash_addr - FLASH_BASE, &stSetPara, sizeof(SetPara_t));
}

/**
 * @brief 从Flash加载参数
 */
void SetParaLoad(void)
{
    uint32_t flash_addr = ADDR_FMC_SECTOR_6;  // 从扇区6读取参数
    
    // 从Flash读取参数
    norflash_read(flash_addr - FLASH_BASE, &stSetPara, sizeof(SetPara_t));
}

/**
 * @brief 处理设置频率
 * @param freq 频率值
 */
void ProcessSetFrequency(uint16_t freq)
{
    if (freq > 0 && freq <= 1000) {
        stSetPara.Setfre = freq;
        SetParaSave();
    }
}

/**
 * @brief 处理设置波特率
 * @param baudrate 波特率值
 */
void ProcessSetBaudrate(uint32_t baudrate)
{
    // 波特率验证
    if (baudrate >= 96 && baudrate <= 9216) {  // 9600 - 921600
        stSetPara.Setbaud = baudrate;
        SetParaSave();
    }
}

/**
 * @brief 处理设置数据输出类型
 * @param type 数据输出类型
 */
void ProcessSetDataOutType(uint8_t type)
{
    if (type <= 3) {
        stSetPara.SetDataOutType = type;
        SetParaSave();
    }
}

/**
 * @brief 开始固件升级
 */
void UpdateFirmwareStart(void)
{
    g_StartUpdateFirm = 1;
    g_UpdateBackFlag = 0;
}

/**
 * @brief 停止固件升级
 */
void UpdateFirmwareStop(void)
{
    g_StartUpdateFirm = 0;
}

/**
 * @brief 获取升级状态
 * @return 升级状态
 */
uint8_t GetUpdateStatus(void)
{
    return g_UpdateBackFlag;
}

/**
 * @brief 固件升级处理
 * @param pucBuf 数据缓冲区
 * @param usIndex 包索引
 * @param usTotalBao 总包数
 * @param ucLen 数据长度
 */
void ParaUpdateHandle(uint8_t *pucBuf, uint16_t usIndex, uint16_t usTotalBao, uint8_t ucLen)
{
    static uint32_t uiOffsetAddr = 0;
    static uint32_t uiLastBaoInDex = 1;
    static uint8_t flag = 0;
    
    // 检查包序号是否重复
    if (uiLastBaoInDex == usIndex) {
        return;
    }
    
    // 第一次升级初始化
    if (flag == 0) {
        flag = 1;
        if (0 != usIndex) {
            g_UpdateBackFlag = 2;  // 反馈标志(0x01-正常/02-异常)
            return;
        }
    }
    
    // 第一包数据处理
    if (usIndex == 0) {
        uiOffsetAddr = 0;
        
        // 擦除升级区域的Flash扇区
        for (int i = 0; i < 95; i++) {
            Drv_FlashErase(APP_UPDATE_ADDRESS + i * FLASH_WRITE_SECTOR_SIZE);
        }
        
        // 写入第一包数据
        Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS + uiOffsetAddr, ucLen);
        uiOffsetAddr += ucLen;
    } else {
        // 写入后续包数据
        Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS + uiOffsetAddr, ucLen);
        uiOffsetAddr += ucLen;
    }
    
    uiLastBaoInDex = usIndex;
    
    // 升级完成处理
    if (usIndex == usTotalBao - 1) {
        g_UpdateBackFlag = 1;  // 升级成功
        flag = 0;
        
        // 可以在这里添加升级完成后的处理，比如校验、重启等
        // 延时后重启系统
        for (volatile int i = 0; i < 1000000; i++);
        Drv_SystemReset();
    }
}

/**
 * @brief 参数配置数据处理
 * @param cmd 命令
 * @param data 数据
 * @param len 数据长度
 */
void ProcessParameterConfig(uint16_t cmd, uint8_t *data, uint16_t len)
{
    switch (cmd) {
        case 0x0001:  // 设置频率
            if (len >= 2) {
                uint16_t freq = (data[0] << 8) | data[1];
                ProcessSetFrequency(freq);
            }
            break;
            
        case 0x0002:  // 设置波特率
            if (len >= 4) {
                uint32_t baudrate = (data[0] << 24) | (data[1] << 16) | (data[2] << 8) | data[3];
                ProcessSetBaudrate(baudrate);
            }
            break;
            
        case 0x0003:  // 设置数据输出类型
            if (len >= 1) {
                ProcessSetDataOutType(data[0]);
            }
            break;
            
        case 0x0010:  // 开始固件升级
            UpdateFirmwareStart();
            break;
            
        case 0x0011:  // 停止固件升级
            UpdateFirmwareStop();
            break;
            
        default:
            break;
    }
}
