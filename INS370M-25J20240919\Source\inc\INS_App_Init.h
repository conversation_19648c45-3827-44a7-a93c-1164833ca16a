//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：INS_App_Init.h
// 文件标识：
// 文件摘要：应用层初始化头文件
//
// 当前版本：V1.0
// 作    者：迁移自HPM6750项目
// 完成时间：2024.12.19
//---------------------------------------------------------

#ifndef __INS_APP_INIT_H
#define __INS_APP_INIT_H

#include "gd32f4xx.h"
#include <stdint.h>
#include <string.h>

// 错误代码定义
#define INS_APP_ERROR_FLASH     0x01    // Flash错误
#define INS_APP_ERROR_SD        0x02    // SD卡错误
#define INS_APP_ERROR_UART      0x03    // 串口错误
#define INS_APP_ERROR_ALGORITHM 0x04    // 算法错误

// 应用层初始化和管理函数
int INS_App_Init(void);
void INS_App_MainLoop(void);
uint8_t INS_App_GetInitStatus(void);
void INS_App_SoftReset(void);
void INS_App_ErrorHandler(uint32_t error_code);
void INS_App_StatusMonitor(void);

// 各模块初始化函数
void INS_UART_Init(void);
void INS_Algorithm_Init(void);
void INS_Protocol_Init(void);
void INS_CoordinateTransform_Init(void);
void INS_HostComm_Init(void);
void INS_IOProtocol_Init(void);

#endif // __INS_APP_INIT_H
