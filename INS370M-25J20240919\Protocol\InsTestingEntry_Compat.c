//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：InsTestingEntry_Compat.c
// 文件标识：
// 文件摘要：兼容HPM6750项目的数据输出和协议处理实现
//
// 当前版本：V1.0
// 作    者：迁移自HPM6750项目
// 完成时间：2024.12.19
//---------------------------------------------------------

#include "InsTestingEntry_Compat.h"
#include "SetParaBao.h"
#include "bsp_uart.h"
#include "bsp_sd_fatfs.h"
#include "appmain.h"
#include "INS912AlgorithmEntry.h"
#include "insTestingEntry.h"
#include "gdtypedefine.h"
#include "GLOBALDATA.h"
#include "fpgad.h"

// 全局变量定义（只定义新增的变量，避免重复定义）
compat_fpgadata_predo_send_t gfpgadataPredoSend_compat = {0};

uint32_t fpga_syn_count = 0;
uint32_t fpga_loop_count = 0;

// 内部缓冲区
static uint8_t r_TAFEAG8_buf[512];
static uint16_t r_cfrdbuf16bit[64];

/**
 * @brief 算法处理入口（兼容版本）
 */
void AlgorithmDo_compat(void)
{
    navcanin_t canin = {0};

    // 调用算法处理函数
    INS912AlgorithmEntry(gfpgadata, &canin, &gnavout);

    // 更新计数器
    fpga_loop_count++;
}

/**
 * @brief INS912输出处理（兼容版本）
 * @param pnavout 导航输出数据指针
 */
void INS912_Output_compat(struct navoutdata_t *pnavout)
{
    if (pnavout == NULL) {
        return;
    }

    // 根据设置的数据输出类型进行不同的输出
    switch (stSetPara.SetDataOutType) {
        case 1:
            FPGATo422_11BB_send();
            break;

        case 2:
            FPGATo422_00BB_send(pnavout);
            break;

        case 3:
            SDTo422_00BB_send(pnavout);
            break;

        default:
            break;
    }
}

/**
 * @brief 发送FPGA原始数据 (11BB格式)
 */
void FPGATo422_11BB_send(void)
{
    // 输出频率控制
    static uint16_t tCnt = 0;
    uint16_t freq = (SETPARA_DATAOUT_FPGA_FREQ / stSetPara.Setfre);
    tCnt++;
    
    if (tCnt >= freq) {
        tCnt = 0;
        
        // 构建数据包（使用现有的数据结构）
        int len = sizeof(gfpgadataSend);
        gfpgadataSend.head1 = 0x11BB;
        gfpgadataSend.head2 = 0xBDDB;
        gfpgadataSend.dataLen = len;

        // 复制FPGA数据
        gfpgadataSend.fpgadata = gpagedata;

        // 包尾信息
        gfpgadataSend.fpgaItrCount = fpga_syn_count;
        gfpgadataSend.fpgaLoopCount = fpga_loop_count;
        gfpgadataSend.Status = g_SysVar.WorkPhase;

        // 计算校验和
        memcpy(&r_TAFEAG8_buf[0], &gfpgadataSend, len);
        gfpgadataSend.CheckSum = app_accum_verify_8bit(r_TAFEAG8_buf, len - 2);

        // 发送数据
        uart4sendmsg_compat((char*)&gfpgadataSend, len);
    }
}

/**
 * @brief 发送预处理数据 (00BB格式)
 * @param pnavout 导航输出数据指针
 */
void FPGATo422_00BB_send(struct navoutdata_t *pnavout)
{
    // 输出频率控制
    static uint16_t tCnt = 0;
    uint16_t freq = (SETPARA_DATAOUT_FPGA_FREQ / stSetPara.Setfre);
    tCnt++;
    
    if (tCnt >= freq) {
        tCnt = 0;
        
        if ((g_StartUpdateFirm != 1) && (SetSdOperateType != 0x03)) {
            // 构建数据包（使用兼容的数据结构）
            int len = sizeof(gfpgadataPredoSend_compat);
            gfpgadataPredoSend_compat.head1 = 0x00BB;
            gfpgadataPredoSend_compat.head2 = 0xBDDB;
            gfpgadataPredoSend_compat.dataLen = len;

            // 复制导航数据到现有的数据结构
            gfpgadataPredoSend_compat.fpgaPreDodata = gins912data;

            // 包尾信息
            gfpgadataPredoSend_compat.fpgaItrCount = fpga_syn_count;
            gfpgadataPredoSend_compat.fpgaLoopCount = fpga_loop_count;
            gfpgadataPredoSend_compat.Status = g_SysVar.WorkPhase;

            // 计算校验和
            memcpy(&r_TAFEAG8_buf[0], &gfpgadataPredoSend_compat, len);
            gfpgadataPredoSend_compat.CheckSum = app_accum_verify_8bit(r_TAFEAG8_buf, len - 2);

            // 发送数据
            uart4sendmsg_compat((char*)&gfpgadataPredoSend_compat, len);
        }
    }
}

/**
 * @brief 从SD卡读取并发送数据
 * @param pnavout 导航输出数据指针
 */
void SDTo422_00BB_send(struct navoutdata_t *pnavout)
{
    // 构建数据包
    int len = sizeof(gfpgadataPredoSend_compat);
    gfpgadataPredoSend_compat.head1 = 0x00BB;
    gfpgadataPredoSend_compat.head2 = 0xBDDB;
    gfpgadataPredoSend_compat.dataLen = len;

    // 从SD卡读取的数据填充
    // 将导航数据转换为现有的数据格式
    memcpy(&gfpgadataPredoSend_compat.fpgaPreDodata, &gins912data, sizeof(gins912data));

    // 包尾信息
    gfpgadataPredoSend_compat.fpgaItrCount = fpga_syn_count;
    gfpgadataPredoSend_compat.fpgaLoopCount = fpga_loop_count;
    gfpgadataPredoSend_compat.Status = g_SysVar.WorkPhase;

    // 计算校验和
    memcpy(&r_TAFEAG8_buf[0], &gfpgadataPredoSend_compat, len);
    gfpgadataPredoSend_compat.CheckSum = app_accum_verify_8bit(r_TAFEAG8_buf, len - 2);

    // 发送数据
    uart4sendmsg_compat((char*)&gfpgadataPredoSend_compat, len);
}

/**
 * @brief 16位数据校验
 * @param data 数据指针
 * @param len 数据长度
 * @return 校验值
 */
uint16_t app_accum_verify_16bit(uint16_t *data, uint16_t len)
{
    uint16_t checksum = 0;
    for (uint16_t i = 0; i < len; i++) {
        checksum += data[i];
    }
    return checksum;
}

/**
 * @brief 8位数据校验
 * @param data 数据指针
 * @param len 数据长度
 * @return 校验值
 */
uint8_t app_accum_verify_8bit(uint8_t *data, uint16_t len)
{
    uint8_t checksum = 0;
    for (uint16_t i = 0; i < len; i++) {
        checksum += data[i];
    }
    return checksum;
}

/**
 * @brief 串口4发送消息（兼容版本）
 * @param msg 消息指针
 * @param len 消息长度
 */
void uart4sendmsg_compat(char *msg, uint16_t len)
{
    // 调用现有的串口发送函数
    uart4sendmsg(msg, (int)len);
}

/**
 * @brief 获取FPGA数据（兼容版本）
 */
void get_fpgadata_compat(void)
{
    // 调用现有的get_fpgadata函数
    get_fpgadata();
    fpga_syn_count++;
}

/**
 * @brief 分析接收数据（兼容版本）
 */
void analysisRxdata_compat(void)
{
    // 接收数据分析处理
    // 这里可以添加参数设置、升级等命令的处理

    // 示例：处理升级命令
    static uint8_t upgrade_buffer[256];
    static uint16_t upgrade_index = 0;
    static uint16_t total_packages = 0;

    // 这里需要根据实际的接收数据格式来解析
    // 如果是升级数据包，调用ParaUpdateHandle
    if (g_StartUpdateFirm) {
        // ParaUpdateHandle(upgrade_buffer, upgrade_index, total_packages, sizeof(upgrade_buffer));
    }
}

/**
 * @brief 循环中的其他处理（兼容版本）
 */
void loopDoOther_compat(void)
{
    // 调用现有的loopDoOther函数
    loopDoOther();
}
