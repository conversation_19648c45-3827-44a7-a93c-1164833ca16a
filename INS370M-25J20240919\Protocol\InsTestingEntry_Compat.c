//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：InsTestingEntry_Compat.c
// 文件标识：
// 文件摘要：兼容HPM6750项目的数据输出和协议处理实现
//
// 当前版本：V1.0
// 作    者：迁移自HPM6750项目
// 完成时间：2024.12.19
//---------------------------------------------------------

#include "InsTestingEntry_Compat.h"
#include "SetParaBao.h"
#include "bsp_uart.h"
#include "bsp_sd_fatfs.h"

// 全局变量定义
uint16_t gfpgadata[200] = {0};
navoutdata_t gnavout = {0};
arraytodata_t ginputdata = {0};
gdwrxdata912_t gins912data = {0};
fpga2ins_t fpga2ins = {0};
SysVar_t g_SysVar = {0};

fpgadata_send_t gfpgadataSend = {0};
fpgadata_predo_send_t gfpgadataPredoSend = {0};

uint32_t fpga_syn_count = 0;
uint32_t fpga_loop_count = 0;

// 内部缓冲区
static uint8_t r_TAFEAG8_buf[512];
static uint16_t r_cfrdbuf16bit[64];

/**
 * @brief 算法处理入口
 */
void AlgorithmDo(void)
{
    navcanin_t canin = {0};
    
    // 调用算法处理函数
    INS912AlgorithmEntry(gfpgadata, &canin, &gnavout);
    
    // 更新计数器
    fpga_loop_count++;
}

/**
 * @brief INS912输出处理
 * @param pnavout 导航输出数据指针
 */
void INS912_Output(navoutdata_t *pnavout)
{
    if (pnavout == NULL) {
        return;
    }
    
    // 根据设置的数据输出类型进行不同的输出
    switch (stSetPara.SetDataOutType) {
        case 1:
            FPGATo422_11BB_send();
            break;
            
        case 2:
            FPGATo422_00BB_send(pnavout);
            break;
            
        case 3:
            SDTo422_00BB_send(pnavout);
            break;
            
        default:
            break;
    }
}

/**
 * @brief 发送FPGA原始数据 (11BB格式)
 */
void FPGATo422_11BB_send(void)
{
    // 输出频率控制
    static uint16_t tCnt = 0;
    uint16_t freq = (SETPARA_DATAOUT_FPGA_FREQ / stSetPara.Setfre);
    tCnt++;
    
    if (tCnt >= freq) {
        tCnt = 0;
        
        // 构建数据包
        int len = sizeof(gfpgadataSend);
        gfpgadataSend.head1 = 0x11BB;
        gfpgadataSend.head2 = 0xBDDB;
        gfpgadataSend.dataLen = len;
        
        // 复制FPGA数据
        memcpy(gfpgadataSend.data, gfpgadata, sizeof(gfpgadataSend.data));
        
        // 包尾信息
        gfpgadataSend.fpgaItrCount = fpga_syn_count;
        gfpgadataSend.fpgaLoopCount = fpga_loop_count;
        gfpgadataSend.Status = g_SysVar.WorkPhase;
        
        // 计算校验和
        memcpy(&r_TAFEAG8_buf[0], &gfpgadataSend, len);
        gfpgadataSend.CheckSum = app_accum_verify_8bit(r_TAFEAG8_buf, len - 2);
        
        // 发送数据
        uart4sendmsg((char*)&gfpgadataSend, len);
    }
}

/**
 * @brief 发送预处理数据 (00BB格式)
 * @param pnavout 导航输出数据指针
 */
void FPGATo422_00BB_send(navoutdata_t *pnavout)
{
    // 输出频率控制
    static uint16_t tCnt = 0;
    uint16_t freq = (SETPARA_DATAOUT_FPGA_FREQ / stSetPara.Setfre);
    tCnt++;
    
    if (tCnt >= freq) {
        tCnt = 0;
        
        if ((g_StartUpdateFirm != 1) && (SetSdOperateType != 0x03)) {
            // 构建数据包
            int len = sizeof(gfpgadataPredoSend);
            gfpgadataPredoSend.head1 = 0x00BB;
            gfpgadataPredoSend.head2 = 0xBDDB;
            gfpgadataPredoSend.dataLen = len;
            
            // 复制导航数据
            gfpgadataPredoSend.fpgaPreDodata = *pnavout;
            
            // 包尾信息
            gfpgadataPredoSend.fpgaItrCount = fpga_syn_count;
            gfpgadataPredoSend.fpgaLoopCount = fpga_loop_count;
            gfpgadataPredoSend.Status = g_SysVar.WorkPhase;
            
            // 计算校验和
            memcpy(&r_TAFEAG8_buf[0], &gfpgadataPredoSend, len);
            gfpgadataPredoSend.CheckSum = app_accum_verify_8bit(r_TAFEAG8_buf, len - 2);
            
            // 发送数据
            uart4sendmsg((char*)&gfpgadataPredoSend, len);
        }
    }
}

/**
 * @brief 从SD卡读取并发送数据
 * @param pnavout 导航输出数据指针
 */
void SDTo422_00BB_send(navoutdata_t *pnavout)
{
    // 构建数据包
    int len = sizeof(gfpgadataPredoSend);
    gfpgadataPredoSend.head1 = 0x00BB;
    gfpgadataPredoSend.head2 = 0xBDDB;
    gfpgadataPredoSend.dataLen = len;
    
    // 从SD卡读取的数据填充
    gfpgadataPredoSend.fpgaPreDodata.pitch = pnavout->pitch;
    gfpgadataPredoSend.fpgaPreDodata.roll = pnavout->roll;
    gfpgadataPredoSend.fpgaPreDodata.azimuth = pnavout->azimuth;
    
    // 包尾信息
    gfpgadataPredoSend.fpgaItrCount = fpga_syn_count;
    gfpgadataPredoSend.fpgaLoopCount = fpga_loop_count;
    gfpgadataPredoSend.Status = g_SysVar.WorkPhase;
    
    // 计算校验和
    memcpy(&r_TAFEAG8_buf[0], &gfpgadataPredoSend, len);
    gfpgadataPredoSend.CheckSum = app_accum_verify_8bit(r_TAFEAG8_buf, len - 2);
    
    // 发送数据
    uart4sendmsg((char*)&gfpgadataPredoSend, len);
}

/**
 * @brief 16位数据校验
 * @param data 数据指针
 * @param len 数据长度
 * @return 校验值
 */
uint16_t app_accum_verify_16bit(uint16_t *data, uint16_t len)
{
    uint16_t checksum = 0;
    for (uint16_t i = 0; i < len; i++) {
        checksum += data[i];
    }
    return checksum;
}

/**
 * @brief 8位数据校验
 * @param data 数据指针
 * @param len 数据长度
 * @return 校验值
 */
uint8_t app_accum_verify_8bit(uint8_t *data, uint16_t len)
{
    uint8_t checksum = 0;
    for (uint16_t i = 0; i < len; i++) {
        checksum += data[i];
    }
    return checksum;
}

/**
 * @brief 串口4发送消息
 * @param msg 消息指针
 * @param len 消息长度
 */
void uart4sendmsg(char *msg, uint16_t len)
{
    // 使用UART4发送数据
    for (uint16_t i = 0; i < len; i++) {
        usart_data_transmit(UART4, (uint8_t)msg[i]);
        while (RESET == usart_flag_get(UART4, USART_FLAG_TC));
    }
}

/**
 * @brief 获取FPGA数据
 */
void get_fpgadata(void)
{
    // 从FPGA读取数据的实现
    // 这里需要根据实际的FPGA接口来实现
    // 示例代码：
    static uint16_t frame_index = 0;
    
    // 模拟从FPGA读取数据
    for (int i = 0; i < 96; i++) {
        gfpgadata[i] = i + frame_index;  // 示例数据
    }
    
    frame_index++;
    fpga_syn_count++;
}

/**
 * @brief 分析接收数据
 */
void analysisRxdata(void)
{
    // 接收数据分析处理
    // 这里可以添加参数设置、升级等命令的处理
    
    // 示例：处理升级命令
    static uint8_t upgrade_buffer[256];
    static uint16_t upgrade_index = 0;
    static uint16_t total_packages = 0;
    
    // 这里需要根据实际的接收数据格式来解析
    // 如果是升级数据包，调用ParaUpdateHandle
    if (g_StartUpdateFirm) {
        // ParaUpdateHandle(upgrade_buffer, upgrade_index, total_packages, sizeof(upgrade_buffer));
    }
}

/**
 * @brief 循环中的其他处理
 */
void loopDoOther(void)
{
    // LED指示
    static uint32_t led_count = 0;
    led_count++;
    
    if (led_count >= 10000) {
        led_count = 0;
        // 切换LED状态
        // LED_Toggle();
    }
    
    // 其他周期性任务
    // 例如：看门狗喂狗、状态检查等
}
