/*
 * Copyright (c) 2022, sakumisu
 *
 * SPDX-License-Identifier: Apache-2.0
 */
#ifndef USB_VIDEO_H
#define USB_VIDEO_H

#define USB_DEVICE_VIDEO_CLASS_VERSION_1_5 0

/*! @brief Video device subclass code */
#define VIDEO_SC_UNDEFINED                  0x00U
#define VIDEO_SC_VIDEOCONTROL               0x01U
#define VIDEO_SC_VIDEOSTREAMING             0x02U
#define VIDEO_SC_VIDEO_INTERFACE_COLLECTION 0x03U

/*! @brief Video device protocol code */
#define VIDEO_PC_PROTOCOL_UNDEFINED 0x00U
#define VIDEO_PC_PROTOCOL_15        0x01U

/*! @brief Video device class-specific descriptor type */
#define VIDEO_CS_UNDEFINED_DESCRIPTOR_TYPE     0x20U
#define VIDEO_CS_DEVICE_DESCRIPTOR_TYPE        0x21U
#define VIDEO_CS_CONFIGURATION_DESCRIPTOR_TYPE 0x22U
#define VIDEO_CS_STRING_DESCRIPTOR_TYPE        0x23U
#define VIDEO_CS_INTERFACE_DESCRIPTOR_TYPE     0x24U
#define VIDEO_CS_ENDPOINT_DESCRIPTOR_TYPE      0x25U

/*! @brief Video device class-specific VC interface descriptor subtype */
#define VIDEO_VC_DESCRIPTOR_UNDEFINED_DESCRIPTOR_SUBTYPE 0x00U
#define VIDEO_VC_HEADER_DESCRIPTOR_SUBTYPE               0x01U
#define VIDEO_VC_INPUT_TERMINAL_DESCRIPTOR_SUBTYPE       0x02U
#define VIDEO_VC_OUTPUT_TERMINAL_DESCRIPTOR_SUBTYPE      0x03U
#define VIDEO_VC_SELECTOR_UNIT_DESCRIPTOR_SUBTYPE        0x04U
#define VIDEO_VC_PROCESSING_UNIT_DESCRIPTOR_SUBTYPE      0x05U
#define VIDEO_VC_EXTENSION_UNIT_DESCRIPTOR_SUBTYPE       0x06U
#define VIDEO_VC_ENCODING_UNIT_DESCRIPTOR_SUBTYPE        0x07U

/*! @brief Video device class-specific VS interface descriptor subtype */
#define VIDEO_VS_UNDEFINED_DESCRIPTOR_SUBTYPE             0x00U
#define VIDEO_VS_INPUT_HEADER_DESCRIPTOR_SUBTYPE          0x01U
#define VIDEO_VS_OUTPUT_HEADER_DESCRIPTOR_SUBTYPE         0x02U
#define VIDEO_VS_STILL_IMAGE_FRAME_DESCRIPTOR_SUBTYPE     0x03U
#define VIDEO_VS_FORMAT_UNCOMPRESSED_DESCRIPTOR_SUBTYPE   0x04U
#define VIDEO_VS_FRAME_UNCOMPRESSED_DESCRIPTOR_SUBTYPE    0x05U
#define VIDEO_VS_FORMAT_MJPEG_DESCRIPTOR_SUBTYPE          0x06U
#define VIDEO_VS_FRAME_MJPEG_DESCRIPTOR_SUBTYPE           0x07U
#define VIDEO_VS_FORMAT_MPEG2TS_DESCRIPTOR_SUBTYPE        0x0AU
#define VIDEO_VS_FORMAT_DV_DESCRIPTOR_SUBTYPE             0x0CU
#define VIDEO_VS_COLORFORMAT_DESCRIPTOR_SUBTYPE           0x0DU
#define VIDEO_VS_FORMAT_FRAME_BASED_DESCRIPTOR_SUBTYPE    0x10U
#define VIDEO_VS_FRAME_FRAME_BASED_DESCRIPTOR_SUBTYPE     0x11U
#define VIDEO_VS_FORMAT_STREAM_BASED_DESCRIPTOR_SUBTYPE   0x12U
#define VIDEO_VS_FORMAT_H264_DESCRIPTOR_SUBTYPE           0x13U
#define VIDEO_VS_FRAME_H264_DESCRIPTOR_SUBTYPE            0x14U
#define VIDEO_VS_FORMAT_H264_SIMULCAST_DESCRIPTOR_SUBTYPE 0x15U
#define VIDEO_VS_FORMAT_VP8_DESCRIPTOR_SUBTYPE            0x16U
#define VIDEO_VS_FRAME_VP8_DESCRIPTOR_SUBTYPE             0x17U
#define VIDEO_VS_FORMAT_VP8_SIMULCAST_DESCRIPTOR_SUBTYPE  0x18U

/*! @brief Video device class-specific VC endpoint descriptor subtype */
#define VIDEO_EP_UNDEFINED_DESCRIPTOR_SUBTYPE 0x00U
#define VIDEO_EP_GENERAL_DESCRIPTOR_SUBTYPE   0x01U
#define VIDEO_EP_ENDPOINT_DESCRIPTOR_SUBTYPE  0x02U
#define VIDEO_EP_INTERRUPT_DESCRIPTOR_SUBTYPE 0x03U

/*! @brief Video device class-specific request code */
#define VIDEO_REQUEST_UNDEFINED   0x00U
#define VIDEO_REQUEST_SET_CUR     0x01U
#define VIDEO_REQUEST_SET_CUR_ALL 0x11U
#define VIDEO_REQUEST_GET_CUR     0x81U
#define VIDEO_REQUEST_GET_MIN     0x82U
#define VIDEO_REQUEST_GET_MAX     0x83U
#define VIDEO_REQUEST_GET_RES     0x84U
#define VIDEO_REQUEST_GET_LEN     0x85U
#define VIDEO_REQUEST_GET_INFO    0x86U
#define VIDEO_REQUEST_GET_DEF     0x87U
#define VIDEO_REQUEST_GET_CUR_ALL 0x91U
#define VIDEO_REQUEST_GET_MIN_ALL 0x92U
#define VIDEO_REQUEST_GET_MAX_ALL 0x93U
#define VIDEO_REQUEST_GET_RES_ALL 0x94U
#define VIDEO_REQUEST_GET_DEF_ALL 0x97U

/*! @brief Video device class-specific VideoControl interface control selector */
#define VIDEO_VC_CONTROL_UNDEFINED          0x00U
#define VIDEO_VC_VIDEO_POWER_MODE_CONTROL   0x01U
#define VIDEO_VC_REQUEST_ERROR_CODE_CONTROL 0x02U

/*! @brief Video device class-specific Terminal control selector */
#define VIDEO_TE_CONTROL_UNDEFINED 0x00U

/*! @brief Video device class-specific Selector Unit control selector */
#define VIDEO_SU_CONTROL_UNDEFINED    0x00U
#define VIDEO_SU_INPUT_SELECT_CONTROL 0x01U

/*! @brief Video device class-specific Camera Terminal control selector */
#define VIDEO_CT_CONTROL_UNDEFINED              0x00U
#define VIDEO_CT_SCANNING_MODE_CONTROL          0x01U
#define VIDEO_CT_AE_MODE_CONTROL                0x02U
#define VIDEO_CT_AE_PRIORITY_CONTROL            0x03U
#define VIDEO_CT_EXPOSURE_TIME_ABSOLUTE_CONTROL 0x04U
#define VIDEO_CT_EXPOSURE_TIME_RELATIVE_CONTROL 0x05U
#define VIDEO_CT_FOCUS_ABSOLUTE_CONTROL         0x06U
#define VIDEO_CT_FOCUS_RELATIVE_CONTROL         0x07U
#define VIDEO_CT_FOCUS_AUTO_CONTROL             0x08U
#define VIDEO_CT_IRIS_ABSOLUTE_CONTROL          0x09U
#define VIDEO_CT_IRIS_RELATIVE_CONTROL          0x0AU
#define VIDEO_CT_ZOOM_ABSOLUTE_CONTROL          0x0BU
#define VIDEO_CT_ZOOM_RELATIVE_CONTROL          0x0CU
#define VIDEO_CT_PANTILT_ABSOLUTE_CONTROL       0x0DU
#define VIDEO_CT_PANTILT_RELATIVE_CONTROL       0x0EU
#define VIDEO_CT_ROLL_ABSOLUTE_CONTROL          0x0FU
#define VIDEO_CT_ROLL_RELATIVE_CONTROL          0x10U
#define VIDEO_CT_PRIVACY_CONTROL                0x11U
#define VIDEO_CT_FOCUS_SIMPLE_CONTROL           0x12U
#define VIDEO_CT_WINDOW_CONTROL                 0x13U
#define VIDEO_CT_REGION_OF_INTEREST_CONTROL     0x14U

/*! @brief Video device class-specific Processing Unit control selector */
#define VIDEO_PU_CONTROL_UNDEFINED                      0x00U
#define VIDEO_PU_BACKLIGHT_COMPENSATION_CONTROL         0x01U
#define VIDEO_PU_BRIGHTNESS_CONTROL                     0x02U
#define VIDEO_PU_CONTRAST_CONTROL                       0x03U
#define VIDEO_PU_GAIN_CONTROL                           0x04U
#define VIDEO_PU_POWER_LINE_FREQUENCY_CONTROL           0x05U
#define VIDEO_PU_HUE_CONTROL                            0x06U
#define VIDEO_PU_SATURATION_CONTROL                     0x07U
#define VIDEO_PU_SHARPNESS_CONTROL                      0x08U
#define VIDEO_PU_GAMMA_CONTROL                          0x09U
#define VIDEO_PU_WHITE_BALANCE_TEMPERATURE_CONTROL      0x0AU
#define VIDEO_PU_WHITE_BALANCE_TEMPERATURE_AUTO_CONTROL 0x0BU
#define VIDEO_PU_WHITE_BALANCE_COMPONENT_CONTROL        0x0CU
#define VIDEO_PU_WHITE_BALANCE_COMPONENT_AUTO_CONTROL   0x0DU
#define VIDEO_PU_DIGITAL_MULTIPLIER_CONTROL             0x0EU
#define VIDEO_PU_DIGITAL_MULTIPLIER_LIMIT_CONTROL       0x0FU
#define VIDEO_PU_HUE_AUTO_CONTROL                       0x10U
#define VIDEO_PU_ANALOG_VIDEO_STANDARD_CONTROL          0x11U
#define VIDEO_PU_ANALOG_LOCK_STATUS_CONTROL             0x12U
#define VIDEO_PU_CONTRAST_AUTO_CONTROL                  0x13U

/*! @brief Video device class-specific Encoding Unit control selector */
#define VIDEO_EU_CONTROL_UNDEFINED           0x00U
#define VIDEO_EU_SELECT_LAYER_CONTROL        0x01U
#define VIDEO_EU_PROFILE_TOOLSET_CONTROL     0x02U
#define VIDEO_EU_VIDEO_RESOLUTION_CONTROL    0x03U
#define VIDEO_EU_MIN_FRAME_INTERVAL_CONTROL  0x04U
#define VIDEO_EU_SLICE_MODE_CONTROL          0x05U
#define VIDEO_EU_RATE_CONTROL_MODE_CONTROL   0x06U
#define VIDEO_EU_AVERAGE_BITRATE_CONTROL     0x07U
#define VIDEO_EU_CPB_SIZE_CONTROL            0x08U
#define VIDEO_EU_PEAK_BIT_RATE_CONTROL       0x09U
#define VIDEO_EU_QUANTIZATION_PARAMS_CONTROL 0x0AU
#define VIDEO_EU_SYNC_REF_FRAME_CONTROL      0x0BU
#define VIDEO_EU_LTR_BUFFER_CONTROL          0x0CU
#define VIDEO_EU_LTR_PICTURE_CONTROL         0x0DU
#define VIDEO_EU_LTR_VALIDATION_CONTROL      0x0EU
#define VIDEO_EU_LEVEL_IDC_LIMIT_CONTROL     0x0FU
#define VIDEO_EU_SEI_PAYLOADTYPE_CONTROL     0x10U
#define VIDEO_EU_QP_RANGE_CONTROL            0x11U
#define VIDEO_EU_PRIORITY_CONTROL            0x12U
#define VIDEO_EU_START_OR_STOP_LAYER_CONTROL 0x13U
#define VIDEO_EU_ERROR_RESILIENCY_CONTROL    0x14U

/*! @brief Video device class-specific Extension Unit control selector */
#define VIDEO_XU_CONTROL_UNDEFINED 0x00U

/*! @brief Video device class-specific VideoStreaming Interface control selector */
#define VIDEO_VS_CONTROL_UNDEFINED            0x00U
#define VIDEO_VS_PROBE_CONTROL                0x01U
#define VIDEO_VS_COMMIT_CONTROL               0x02U
#define VIDEO_VS_STILL_PROBE_CONTROL          0x03U
#define VIDEO_VS_STILL_COMMIT_CONTROL         0x04U
#define VIDEO_VS_STILL_IMAGE_TRIGGER_CONTROL  0x05U
#define VIDEO_VS_STREAM_ERROR_CODE_CONTROL    0x06U
#define VIDEO_VS_GENERATE_KEY_FRAME_CONTROL   0x07U
#define VIDEO_VS_UPDATE_FRAME_SEGMENT_CONTROL 0x08U
#define VIDEO_VS_SYNCH_DELAY_CONTROL          0x09U

/*! @}*/

/*!
 * @name USB Video class terminal types
 * @{
 */

/*! @brief Video device USB terminal type */
#define VIDEO_TT_VENDOR_SPECIFIC 0x0100U
#define VIDEO_TT_STREAMING       0x0101U

/*! @brief Video device input terminal type */
#define VIDEO_ITT_VENDOR_SPECIFIC       0x0200U
#define VIDEO_ITT_CAMERA                0x0201U
#define VIDEO_ITT_MEDIA_TRANSPORT_INPUT 0x0202U

/*! @brief Video device output terminal type */
#define VIDEO_OTT_VENDOR_SPECIFIC        0x0300U
#define VIDEO_OTT_DISPLAY                0x0301U
#define VIDEO_OTT_MEDIA_TRANSPORT_OUTPUT 0x0302U

/*! @brief Video device external terminal type */
#define VIDEO_ET_VENDOR_SPECIFIC     0x0400U
#define VIDEO_ET_COMPOSITE_CONNECTOR 0x0401U
#define VIDEO_ET_SVIDEO_CONNECTOR    0x0402U
#define VIDEO_ET_COMPONENT_CONNECTOR 0x0403U

/*! @}*/

/*!
 * @name USB Video class setup request types
 * @{
 */

/*! @brief Video device class setup request set type */
#define VIDEO_SET_REQUEST_INTERFACE 0x21U
#define VIDEO_SET_REQUEST_ENDPOINT  0x22U

/*! @brief Video device class setup request get type */
#define VIDEO_GET_REQUEST_INTERFACE 0xA1U
#define VIDEO_GET_REQUEST_ENDPOINT  0xA2U

/*! @}*/

/*! @brief Video device still image trigger control */
#define VIDEO_STILL_IMAGE_TRIGGER_NORMAL_OPERATION                            0x00U
#define VIDEO_STILL_IMAGE_TRIGGER_TRANSMIT_STILL_IMAGE                        0x01U
#define VIDEO_STILL_IMAGE_TRIGGER_TRANSMIT_STILL_IMAGE_VS_DEDICATED_BULK_PIPE 0x02U
#define VIDEO_STILL_IMAGE_TRIGGER_ABORT_STILL_IMAGE_TRANSMISSION              0x03U

/*!
 * @name USB Video device class-specific request commands
 * @{
 */

/*! @brief Video device class-specific request GET CUR COMMAND */
#define VIDEO_GET_CUR_VC_POWER_MODE_CONTROL 0x8101U
#define VIDEO_GET_CUR_VC_ERROR_CODE_CONTROL 0x8102U

#define VIDEO_GET_CUR_PU_BACKLIGHT_COMPENSATION_CONTROL         0x8121U
#define VIDEO_GET_CUR_PU_BRIGHTNESS_CONTROL                     0x8122U
#define VIDEO_GET_CUR_PU_CONTRACT_CONTROL                       0x8123U
#define VIDEO_GET_CUR_PU_GAIN_CONTROL                           0x8124U
#define VIDEO_GET_CUR_PU_POWER_LINE_FREQUENCY_CONTROL           0x8125U
#define VIDEO_GET_CUR_PU_HUE_CONTROL                            0x8126U
#define VIDEO_GET_CUR_PU_SATURATION_CONTROL                     0x8127U
#define VIDEO_GET_CUR_PU_SHARRNESS_CONTROL                      0x8128U
#define VIDEO_GET_CUR_PU_GAMMA_CONTROL                          0x8129U
#define VIDEO_GET_CUR_PU_WHITE_BALANCE_TEMPERATURE_CONTROL      0x812AU
#define VIDEO_GET_CUR_PU_WHITE_BALANCE_TEMPERATURE_AUTO_CONTROL 0x812BU
#define VIDEO_GET_CUR_PU_WHITE_BALANCE_COMPONENT_CONTROL        0x812CU
#define VIDEO_GET_CUR_PU_WHITE_BALANCE_COMPONENT_AUTO_CONTROL   0x812DU
#define VIDEO_GET_CUR_PU_DIGITAL_MULTIPLIER_CONTROL             0x812EU
#define VIDEO_GET_CUR_PU_DIGITAL_MULTIPLIER_LIMIT_CONTROL       0x812FU
#define VIDEO_GET_CUR_PU_HUE_AUTO_CONTROL                       0x8130U
#define VIDEO_GET_CUR_PU_ANALOG_VIDEO_STANDARD_CONTROL          0x8131U
#define VIDEO_GET_CUR_PU_ANALOG_LOCK_STATUS_CONTROL             0x8132U
#if defined(USB_DEVICE_VIDEO_CLASS_VERSION_1_5) && USB_DEVICE_VIDEO_CLASS_VERSION_1_5
#define VIDEO_GET_CUR_PU_CONTRAST_AUTO_CONTROL 0x8133U
#endif

#define VIDEO_GET_CUR_CT_SCANNING_MODE_CONTROL          0x8141U
#define VIDEO_GET_CUR_CT_AE_MODE_CONTROL                0x8142U
#define VIDEO_GET_CUR_CT_AE_PRIORITY_CONTROL            0x8143U
#define VIDEO_GET_CUR_CT_EXPOSURE_TIME_ABSOLUTE_CONTROL 0x8144U
#define VIDEO_GET_CUR_CT_EXPOSURE_TIME_RELATIVE_CONTROL 0x8145U
#define VIDEO_GET_CUR_CT_FOCUS_ABSOLUTE_CONTROL         0x8146U
#define VIDEO_GET_CUR_CT_FOCUS_RELATIVE_CONTROL         0x8147U
#define VIDEO_GET_CUR_CT_FOCUS_AUTO_CONTROL             0x8148U
#define VIDEO_GET_CUR_CT_IRIS_ABSOLUTE_CONTROL          0x8149U
#define VIDEO_GET_CUR_CT_IRIS_RELATIVE_CONTROL          0x814AU
#define VIDEO_GET_CUR_CT_ZOOM_ABSOLUTE_CONTROL          0x814BU
#define VIDEO_GET_CUR_CT_ZOOM_RELATIVE_CONTROL          0x814CU
#define VIDEO_GET_CUR_CT_PANTILT_ABSOLUTE_CONTROL       0x814DU
#define VIDEO_GET_CUR_CT_PANTILT_RELATIVE_CONTROL       0x814EU
#define VIDEO_GET_CUR_CT_ROLL_ABSOLUTE_CONTROL          0x814FU
#define VIDEO_GET_CUR_CT_ROLL_RELATIVE_CONTROL          0x8150U
#define VIDEO_GET_CUR_CT_PRIVACY_CONTROL                0x8151U
#if defined(USB_DEVICE_VIDEO_CLASS_VERSION_1_5) && USB_DEVICE_VIDEO_CLASS_VERSION_1_5
#define VIDEO_GET_CUR_CT_FOCUS_SIMPLE_CONTROL       0x8152U
#define VIDEO_GET_CUR_CT_DIGITAL_WINDOW_CONTROL     0x8153U
#define VIDEO_GET_CUR_CT_REGION_OF_INTEREST_CONTROL 0x8154U
#endif

#define VIDEO_GET_CUR_VS_PROBE_CONTROL                0x8161U
#define VIDEO_GET_CUR_VS_COMMIT_CONTROL               0x8162U
#define VIDEO_GET_CUR_VS_STILL_PROBE_CONTROL          0x8163U
#define VIDEO_GET_CUR_VS_STILL_COMMIT_CONTROL         0x8164U
#define VIDEO_GET_CUR_VS_STILL_IMAGE_TRIGGER_CONTROL  0x8165U
#define VIDEO_GET_CUR_VS_STREAM_ERROR_CODE_CONTROL    0x8166U
#define VIDEO_GET_CUR_VS_GENERATE_KEY_FRAME_CONTROL   0x8167U
#define VIDEO_GET_CUR_VS_UPDATE_FRAME_SEGMENT_CONTROL 0x8168U
#define VIDEO_GET_CUR_VS_SYNCH_DELAY_CONTROL          0x8169U

#if defined(USB_DEVICE_VIDEO_CLASS_VERSION_1_5) && USB_DEVICE_VIDEO_CLASS_VERSION_1_5
#define VIDEO_GET_CUR_EU_SELECT_LAYER_CONTROL        0x8181U
#define VIDEO_GET_CUR_EU_PROFILE_TOOLSET_CONTROL     0x8182U
#define VIDEO_GET_CUR_EU_VIDEO_RESOLUTION_CONTROL    0x8183U
#define VIDEO_GET_CUR_EU_MIN_FRAME_INTERVAL_CONTROL  0x8184U
#define VIDEO_GET_CUR_EU_SLICE_MODE_CONTROL          0x8185U
#define VIDEO_GET_CUR_EU_RATE_CONTROL_MODE_CONTROL   0x8186U
#define VIDEO_GET_CUR_EU_AVERAGE_BITRATE_CONTROL     0x8187U
#define VIDEO_GET_CUR_EU_CPB_SIZE_CONTROL            0x8188U
#define VIDEO_GET_CUR_EU_PEAK_BIT_RATE_CONTROL       0x8189U
#define VIDEO_GET_CUR_EU_QUANTIZATION_PARAMS_CONTROL 0x818AU
#define VIDEO_GET_CUR_EU_SYNC_REF_FRAME_CONTROL      0x818BU
#define VIDEO_GET_CUR_EU_LTR_BUFFER_CONTROL          0x818CU
#define VIDEO_GET_CUR_EU_LTR_PICTURE_CONTROL         0x818DU
#define VIDEO_GET_CUR_EU_LTR_VALIDATION_CONTROL      0x818EU
#define VIDEO_GET_CUR_EU_LEVEL_IDC_LIMIT_CONTROL     0x818FU
#define VIDEO_GET_CUR_EU_SEI_PAYLOADTYPE_CONTROL     0x8190U
#define VIDEO_GET_CUR_EU_QP_RANGE_CONTROL            0x8191U
#define VIDEO_GET_CUR_EU_PRIORITY_CONTROL            0x8192U
#define VIDEO_GET_CUR_EU_START_OR_STOP_LAYER_CONTROL 0x8193U
#define VIDEO_GET_CUR_EU_ERROR_RESILIENCY_CONTROL    0x8194U
#endif

/*! @brief Video device class-specific request GET MIN COMMAND */
#define VIDEO_GET_MIN_PU_BACKLIGHT_COMPENSATION_CONTROL    0x8221U
#define VIDEO_GET_MIN_PU_BRIGHTNESS_CONTROL                0x8222U
#define VIDEO_GET_MIN_PU_CONTRACT_CONTROL                  0x8223U
#define VIDEO_GET_MIN_PU_GAIN_CONTROL                      0x8224U
#define VIDEO_GET_MIN_PU_HUE_CONTROL                       0x8226U
#define VIDEO_GET_MIN_PU_SATURATION_CONTROL                0x8227U
#define VIDEO_GET_MIN_PU_SHARRNESS_CONTROL                 0x8228U
#define VIDEO_GET_MIN_PU_GAMMA_CONTROL                     0x8229U
#define VIDEO_GET_MIN_PU_WHITE_BALANCE_TEMPERATURE_CONTROL 0x822AU
#define VIDEO_GET_MIN_PU_WHITE_BALANCE_COMPONENT_CONTROL   0x822CU
#define VIDEO_GET_MIN_PU_DIGITAL_MULTIPLIER_CONTROL        0x822EU
#define VIDEO_GET_MIN_PU_DIGITAL_MULTIPLIER_LIMIT_CONTROL  0x822FU

#define VIDEO_GET_MIN_CT_EXPOSURE_TIME_ABSOLUTE_CONTROL 0x8244U
#define VIDEO_GET_MIN_CT_FOCUS_ABSOLUTE_CONTROL         0x8246U
#define VIDEO_GET_MIN_CT_FOCUS_RELATIVE_CONTROL         0x8247U
#define VIDEO_GET_MIN_CT_IRIS_ABSOLUTE_CONTROL          0x8249U
#define VIDEO_GET_MIN_CT_ZOOM_ABSOLUTE_CONTROL          0x824BU
#define VIDEO_GET_MIN_CT_ZOOM_RELATIVE_CONTROL          0x824CU
#define VIDEO_GET_MIN_CT_PANTILT_ABSOLUTE_CONTROL       0x824DU
#define VIDEO_GET_MIN_CT_PANTILT_RELATIVE_CONTROL       0x824EU
#define VIDEO_GET_MIN_CT_ROLL_ABSOLUTE_CONTROL          0x824FU
#define VIDEO_GET_MIN_CT_ROLL_RELATIVE_CONTROL          0x8250U
#if defined(USB_DEVICE_VIDEO_CLASS_VERSION_1_5) && USB_DEVICE_VIDEO_CLASS_VERSION_1_5
#define VIDEO_GET_MIN_CT_DIGITAL_WINDOW_CONTROL     0x8251U
#define VIDEO_GET_MIN_CT_REGION_OF_INTEREST_CONTROL 0x8252U
#endif

#define VIDEO_GET_MIN_VS_PROBE_CONTROL                0x8261U
#define VIDEO_GET_MIN_VS_STILL_PROBE_CONTROL          0x8263U
#define VIDEO_GET_MIN_VS_UPDATE_FRAME_SEGMENT_CONTROL 0x8268U
#define VIDEO_GET_MIN_VS_SYNCH_DELAY_CONTROL          0x8269U

#if defined(USB_DEVICE_VIDEO_CLASS_VERSION_1_5) && USB_DEVICE_VIDEO_CLASS_VERSION_1_5
#define VIDEO_GET_MIN_EU_VIDEO_RESOLUTION_CONTROL    0x8283U
#define VIDEO_GET_MIN_EU_MIN_FRAME_INTERVAL_CONTROL  0x8284U
#define VIDEO_GET_MIN_EU_SLICE_MODE_CONTROL          0x8285U
#define VIDEO_GET_MIN_EU_AVERAGE_BITRATE_CONTROL     0x8287U
#define VIDEO_GET_MIN_EU_CPB_SIZE_CONTROL            0x8288U
#define VIDEO_GET_MIN_EU_PEAK_BIT_RATE_CONTROL       0x8289U
#define VIDEO_GET_MIN_EU_QUANTIZATION_PARAMS_CONTROL 0x828AU
#define VIDEO_GET_MIN_EU_SYNC_REF_FRAME_CONTROL      0x828BU
#define VIDEO_GET_MIN_EU_LEVEL_IDC_LIMIT_CONTROL     0x828FU
#define VIDEO_GET_MIN_EU_SEI_PAYLOADTYPE_CONTROL     0x8290U
#define VIDEO_GET_MIN_EU_QP_RANGE_CONTROL            0x8291U
#endif

/*! @brief Video device class-specific request GET MAX COMMAND */
#define VIDEO_GET_MAX_PU_BACKLIGHT_COMPENSATION_CONTROL    0x8321U
#define VIDEO_GET_MAX_PU_BRIGHTNESS_CONTROL                0x8322U
#define VIDEO_GET_MAX_PU_CONTRACT_CONTROL                  0x8323U
#define VIDEO_GET_MAX_PU_GAIN_CONTROL                      0x8324U
#define VIDEO_GET_MAX_PU_HUE_CONTROL                       0x8326U
#define VIDEO_GET_MAX_PU_SATURATION_CONTROL                0x8327U
#define VIDEO_GET_MAX_PU_SHARRNESS_CONTROL                 0x8328U
#define VIDEO_GET_MAX_PU_GAMMA_CONTROL                     0x8329U
#define VIDEO_GET_MAX_PU_WHITE_BALANCE_TEMPERATURE_CONTROL 0x832AU
#define VIDEO_GET_MAX_PU_WHITE_BALANCE_COMPONENT_CONTROL   0x832CU
#define VIDEO_GET_MAX_PU_DIGITAL_MULTIPLIER_CONTROL        0x832EU
#define VIDEO_GET_MAX_PU_DIGITAL_MULTIPLIER_LIMIT_CONTROL  0x832FU

#define VIDEO_GET_MAX_CT_EXPOSURE_TIME_ABSOLUTE_CONTROL 0x8344U
#define VIDEO_GET_MAX_CT_FOCUS_ABSOLUTE_CONTROL         0x8346U
#define VIDEO_GET_MAX_CT_FOCUS_RELATIVE_CONTROL         0x8347U
#define VIDEO_GET_MAX_CT_IRIS_ABSOLUTE_CONTROL          0x8349U
#define VIDEO_GET_MAX_CT_ZOOM_ABSOLUTE_CONTROL          0x834BU
#define VIDEO_GET_MAX_CT_ZOOM_RELATIVE_CONTROL          0x834CU
#define VIDEO_GET_MAX_CT_PANTILT_ABSOLUTE_CONTROL       0x834DU
#define VIDEO_GET_MAX_CT_PANTILT_RELATIVE_CONTROL       0x834EU
#define VIDEO_GET_MAX_CT_ROLL_ABSOLUTE_CONTROL          0x834FU
#define VIDEO_GET_MAX_CT_ROLL_RELATIVE_CONTROL          0x8350U
#if defined(USB_DEVICE_VIDEO_CLASS_VERSION_1_5) && USB_DEVICE_VIDEO_CLASS_VERSION_1_5
#define VIDEO_GET_MAX_CT_DIGITAL_WINDOW_CONTROL     0x8351U
#define VIDEO_GET_MAX_CT_REGION_OF_INTEREST_CONTROL 0x8352U
#endif

#define VIDEO_GET_MAX_VS_PROBE_CONTROL                0x8361U
#define VIDEO_GET_MAX_VS_STILL_PROBE_CONTROL          0x8363U
#define VIDEO_GET_MAX_VS_UPDATE_FRAME_SEGMENT_CONTROL 0x8368U
#define VIDEO_GET_MAX_VS_SYNCH_DELAY_CONTROL          0x8369U

#if defined(USB_DEVICE_VIDEO_CLASS_VERSION_1_5) && USB_DEVICE_VIDEO_CLASS_VERSION_1_5
#define VIDEO_GET_MAX_EU_VIDEO_RESOLUTION_CONTROL    0x8383U
#define VIDEO_GET_MAX_EU_MIN_FRAME_INTERVAL_CONTROL  0x8384U
#define VIDEO_GET_MAX_EU_SLICE_MODE_CONTROL          0x8385U
#define VIDEO_GET_MAX_EU_AVERAGE_BITRATE_CONTROL     0x8387U
#define VIDEO_GET_MAX_EU_CPB_SIZE_CONTROL            0x8388U
#define VIDEO_GET_MAX_EU_PEAK_BIT_RATE_CONTROL       0x8389U
#define VIDEO_GET_MAX_EU_QUANTIZATION_PARAMS_CONTROL 0x838AU
#define VIDEO_GET_MAX_EU_SYNC_REF_FRAME_CONTROL      0x838BU
#define VIDEO_GET_MAX_EU_LTR_BUFFER_CONTROL          0x838CU
#define VIDEO_GET_MAX_EU_LEVEL_IDC_LIMIT_CONTROL     0x838FU
#define VIDEO_GET_MAX_EU_SEI_PAYLOADTYPE_CONTROL     0x8390U
#define VIDEO_GET_MAX_EU_QP_RANGE_CONTROL            0x8391U
#endif

/*! @brief Video device class-specific request GET RES COMMAND */
#define VIDEO_GET_RES_PU_BACKLIGHT_COMPENSATION_CONTROL    0x8421U
#define VIDEO_GET_RES_PU_BRIGHTNESS_CONTROL                0x8422U
#define VIDEO_GET_RES_PU_CONTRACT_CONTROL                  0x8423U
#define VIDEO_GET_RES_PU_GAIN_CONTROL                      0x8424U
#define VIDEO_GET_RES_PU_HUE_CONTROL                       0x8426U
#define VIDEO_GET_RES_PU_SATURATION_CONTROL                0x8427U
#define VIDEO_GET_RES_PU_SHARRNESS_CONTROL                 0x8428U
#define VIDEO_GET_RES_PU_GAMMA_CONTROL                     0x8429U
#define VIDEO_GET_RES_PU_WHITE_BALANCE_TEMPERATURE_CONTROL 0x842AU
#define VIDEO_GET_RES_PU_WHITE_BALANCE_COMPONENT_CONTROL   0x842CU
#define VIDEO_GET_RES_PU_DIGITAL_MULTIPLIER_CONTROL        0x842EU
#define VIDEO_GET_RES_PU_DIGITAL_MULTIPLIER_LIMIT_CONTROL  0x842FU

#define VIDEO_GET_RES_CT_AE_MODE_CONTROL                0x8442U
#define VIDEO_GET_RES_CT_EXPOSURE_TIME_ABSOLUTE_CONTROL 0x8444U
#define VIDEO_GET_RES_CT_FOCUS_ABSOLUTE_CONTROL         0x8446U
#define VIDEO_GET_RES_CT_FOCUS_RELATIVE_CONTROL         0x8447U
#define VIDEO_GET_RES_CT_IRIS_ABSOLUTE_CONTROL          0x8449U
#define VIDEO_GET_RES_CT_ZOOM_ABSOLUTE_CONTROL          0x844BU
#define VIDEO_GET_RES_CT_ZOOM_RELATIVE_CONTROL          0x844CU
#define VIDEO_GET_RES_CT_PANTILT_ABSOLUTE_CONTROL       0x844DU
#define VIDEO_GET_RES_CT_PANTILT_RELATIVE_CONTROL       0x844EU
#define VIDEO_GET_RES_CT_ROLL_ABSOLUTE_CONTROL          0x844FU
#define VIDEO_GET_RES_CT_ROLL_RELATIVE_CONTROL          0x8450U

#define VIDEO_GET_RES_VS_PROBE_CONTROL                0x8461U
#define VIDEO_GET_RES_VS_STILL_PROBE_CONTROL          0x8463U
#define VIDEO_GET_RES_VS_UPDATE_FRAME_SEGMENT_CONTROL 0x8468U
#define VIDEO_GET_RES_VS_SYNCH_DELAY_CONTROL          0x8469U

#if defined(USB_DEVICE_VIDEO_CLASS_VERSION_1_5) && USB_DEVICE_VIDEO_CLASS_VERSION_1_5
#define VIDEO_GET_RES_EU_AVERAGE_BITRATE_CONTROL     0x8487U
#define VIDEO_GET_RES_EU_CPB_SIZE_CONTROL            0x8488U
#define VIDEO_GET_RES_EU_PEAK_BIT_RATE_CONTROL       0x8489U
#define VIDEO_GET_RES_EU_QUANTIZATION_PARAMS_CONTROL 0x848AU
#define VIDEO_GET_RES_EU_ERROR_RESILIENCY_CONTROL    0x8494U
#endif

/*! @brief Video device class-specific request GET LEN COMMAND */

#define VIDEO_GET_LEN_VS_PROBE_CONTROL        0x8561U
#define VIDEO_GET_LEN_VS_COMMIT_CONTROL       0x8562U
#define VIDEO_GET_LEN_VS_STILL_PROBE_CONTROL  0x8563U
#define VIDEO_GET_LEN_VS_STILL_COMMIT_CONTROL 0x8564U

#if defined(USB_DEVICE_VIDEO_CLASS_VERSION_1_5) && USB_DEVICE_VIDEO_CLASS_VERSION_1_5
#define VIDEO_GET_LEN_EU_SELECT_LAYER_CONTROL        0x8581U
#define VIDEO_GET_LEN_EU_PROFILE_TOOLSET_CONTROL     0x8582U
#define VIDEO_GET_LEN_EU_VIDEO_RESOLUTION_CONTROL    0x8583U
#define VIDEO_GET_LEN_EU_MIN_FRAME_INTERVAL_CONTROL  0x8584U
#define VIDEO_GET_LEN_EU_SLICE_MODE_CONTROL          0x8585U
#define VIDEO_GET_LEN_EU_RATE_CONTROL_MODE_CONTROL   0x8586U
#define VIDEO_GET_LEN_EU_AVERAGE_BITRATE_CONTROL     0x8587U
#define VIDEO_GET_LEN_EU_CPB_SIZE_CONTROL            0x8588U
#define VIDEO_GET_LEN_EU_PEAK_BIT_RATE_CONTROL       0x8589U
#define VIDEO_GET_LEN_EU_QUANTIZATION_PARAMS_CONTROL 0x858AU
#define VIDEO_GET_LEN_EU_SYNC_REF_FRAME_CONTROL      0x858BU
#define VIDEO_GET_LEN_EU_LTR_BUFFER_CONTROL          0x858CU
#define VIDEO_GET_LEN_EU_LTR_PICTURE_CONTROL         0x858DU
#define VIDEO_GET_LEN_EU_LTR_VALIDATION_CONTROL      0x858EU
#define VIDEO_GET_LEN_EU_QP_RANGE_CONTROL            0x8591U
#define VIDEO_GET_LEN_EU_PRIORITY_CONTROL            0x8592U
#define VIDEO_GET_LEN_EU_START_OR_STOP_LAYER_CONTROL 0x8593U
#endif

/*! @brief Video device class-specific request GET INFO COMMAND */
#define VIDEO_GET_INFO_VC_POWER_MODE_CONTROL 0x8601U
#define VIDEO_GET_INFO_VC_ERROR_CODE_CONTROL 0x8602U

#define VIDEO_GET_INFO_PU_BACKLIGHT_COMPENSATION_CONTROL         0x8621U
#define VIDEO_GET_INFO_PU_BRIGHTNESS_CONTROL                     0x8622U
#define VIDEO_GET_INFO_PU_CONTRACT_CONTROL                       0x8623U
#define VIDEO_GET_INFO_PU_GAIN_CONTROL                           0x8624U
#define VIDEO_GET_INFO_PU_POWER_LINE_FREQUENCY_CONTROL           0x8625U
#define VIDEO_GET_INFO_PU_HUE_CONTROL                            0x8626U
#define VIDEO_GET_INFO_PU_SATURATION_CONTROL                     0x8627U
#define VIDEO_GET_INFO_PU_SHARRNESS_CONTROL                      0x8628U
#define VIDEO_GET_INFO_PU_GAMMA_CONTROL                          0x8629U
#define VIDEO_GET_INFO_PU_WHITE_BALANCE_TEMPERATURE_CONTROL      0x862AU
#define VIDEO_GET_INFO_PU_WHITE_BALANCE_TEMPERATURE_AUTO_CONTROL 0x862BU
#define VIDEO_GET_INFO_PU_WHITE_BALANCE_COMPONENT_CONTROL        0x862CU
#define VIDEO_GET_INFO_PU_WHITE_BALANCE_COMPONENT_AUTO_CONTROL   0x862DU
#define VIDEO_GET_INFO_PU_DIGITAL_MULTIPLIER_CONTROL             0x862EU
#define VIDEO_GET_INFO_PU_DIGITAL_MULTIPLIER_LIMIT_CONTROL       0x862FU
#define VIDEO_GET_INFO_PU_HUE_AUTO_CONTROL                       0x8630U
#define VIDEO_GET_INFO_PU_ANALOG_VIDEO_STANDARD_CONTROL          0x8631U
#define VIDEO_GET_INFO_PU_ANALOG_LOCK_STATUS_CONTROL             0x8632U
#if defined(USB_DEVICE_VIDEO_CLASS_VERSION_1_5) && USB_DEVICE_VIDEO_CLASS_VERSION_1_5
#define VIDEO_GET_INFO_PU_CONTRAST_AUTO_CONTROL 0x8633U
#endif

#define VIDEO_GET_INFO_CT_SCANNING_MODE_CONTROL          0x8641U
#define VIDEO_GET_INFO_CT_AE_MODE_CONTROL                0x8642U
#define VIDEO_GET_INFO_CT_AE_PRIORITY_CONTROL            0x8643U
#define VIDEO_GET_INFO_CT_EXPOSURE_TIME_ABSOLUTE_CONTROL 0x8644U
#define VIDEO_GET_INFO_CT_EXPOSURE_TIME_RELATIVE_CONTROL 0x8645U
#define VIDEO_GET_INFO_CT_FOCUS_ABSOLUTE_CONTROL         0x8646U
#define VIDEO_GET_INFO_CT_FOCUS_RELATIVE_CONTROL         0x8647U
#define VIDEO_GET_INFO_CT_FOCUS_AUTO_CONTROL             0x8648U
#define VIDEO_GET_INFO_CT_IRIS_ABSOLUTE_CONTROL          0x8649U
#define VIDEO_GET_INFO_CT_IRIS_RELATIVE_CONTROL          0x864AU
#define VIDEO_GET_INFO_CT_ZOOM_ABSOLUTE_CONTROL          0x864BU
#define VIDEO_GET_INFO_CT_ZOOM_RELATIVE_CONTROL          0x864CU
#define VIDEO_GET_INFO_CT_PANTILT_ABSOLUTE_CONTROL       0x864DU
#define VIDEO_GET_INFO_CT_PANTILT_RELATIVE_CONTROL       0x864EU
#define VIDEO_GET_INFO_CT_ROLL_ABSOLUTE_CONTROL          0x864FU
#define VIDEO_GET_INFO_CT_ROLL_RELATIVE_CONTROL          0x8650U
#define VIDEO_GET_INFO_CT_PRIVACY_CONTROL                0x8651U
#if defined(USB_DEVICE_VIDEO_CLASS_VERSION_1_5) && USB_DEVICE_VIDEO_CLASS_VERSION_1_5
#define VIDEO_GET_INFO_CT_FOCUS_SIMPLE_CONTROL 0x8652U
#endif

#define VIDEO_GET_INFO_VS_PROBE_CONTROL                0x8661U
#define VIDEO_GET_INFO_VS_COMMIT_CONTROL               0x8662U
#define VIDEO_GET_INFO_VS_STILL_PROBE_CONTROL          0x8663U
#define VIDEO_GET_INFO_VS_STILL_COMMIT_CONTROL         0x8664U
#define VIDEO_GET_INFO_VS_STILL_IMAGE_TRIGGER_CONTROL  0x8665U
#define VIDEO_GET_INFO_VS_STREAM_ERROR_CODE_CONTROL    0x8666U
#define VIDEO_GET_INFO_VS_GENERATE_KEY_FRAME_CONTROL   0x8667U
#define VIDEO_GET_INFO_VS_UPDATE_FRAME_SEGMENT_CONTROL 0x8668U
#define VIDEO_GET_INFO_VS_SYNCH_DELAY_CONTROL          0x8669U

#if defined(USB_DEVICE_VIDEO_CLASS_VERSION_1_5) && USB_DEVICE_VIDEO_CLASS_VERSION_1_5
#define VIDEO_GET_INFO_EU_SELECT_LAYER_CONTROL        0x8681U
#define VIDEO_GET_INFO_EU_PROFILE_TOOLSET_CONTROL     0x8682U
#define VIDEO_GET_INFO_EU_VIDEO_RESOLUTION_CONTROL    0x8683U
#define VIDEO_GET_INFO_EU_MIN_FRAME_INTERVAL_CONTROL  0x8684U
#define VIDEO_GET_INFO_EU_SLICE_MODE_CONTROL          0x8685U
#define VIDEO_GET_INFO_EU_RATE_CONTROL_MODE_CONTROL   0x8686U
#define VIDEO_GET_INFO_EU_AVERAGE_BITRATE_CONTROL     0x8687U
#define VIDEO_GET_INFO_EU_CPB_SIZE_CONTROL            0x8688U
#define VIDEO_GET_INFO_EU_PEAK_BIT_RATE_CONTROL       0x8689U
#define VIDEO_GET_INFO_EU_QUANTIZATION_PARAMS_CONTROL 0x868AU
#define VIDEO_GET_INFO_EU_SYNC_REF_FRAME_CONTROL      0x868BU
#define VIDEO_GET_INFO_EU_LTR_BUFFER_CONTROL          0x868CU
#define VIDEO_GET_INFO_EU_LTR_PICTURE_CONTROL         0x868DU
#define VIDEO_GET_INFO_EU_LTR_VALIDATION_CONTROL      0x868EU
#define VIDEO_GET_INFO_EU_SEI_PAYLOADTYPE_CONTROL     0x8690U
#define VIDEO_GET_INFO_EU_QP_RANGE_CONTROL            0x8691U
#define VIDEO_GET_INFO_EU_PRIORITY_CONTROL            0x8692U
#define VIDEO_GET_INFO_EU_START_OR_STOP_LAYER_CONTROL 0x8693U
#endif

/*! @brief Video device class-specific request GET DEF COMMAND */
#define VIDEO_GET_DEF_PU_BACKLIGHT_COMPENSATION_CONTROL         0x8721U
#define VIDEO_GET_DEF_PU_BRIGHTNESS_CONTROL                     0x8722U
#define VIDEO_GET_DEF_PU_CONTRACT_CONTROL                       0x8723U
#define VIDEO_GET_DEF_PU_GAIN_CONTROL                           0x8724U
#define VIDEO_GET_DEF_PU_POWER_LINE_FREQUENCY_CONTROL           0x8725U
#define VIDEO_GET_DEF_PU_HUE_CONTROL                            0x8726U
#define VIDEO_GET_DEF_PU_SATURATION_CONTROL                     0x8727U
#define VIDEO_GET_DEF_PU_SHARRNESS_CONTROL                      0x8728U
#define VIDEO_GET_DEF_PU_GAMMA_CONTROL                          0x8729U
#define VIDEO_GET_DEF_PU_WHITE_BALANCE_TEMPERATURE_CONTROL      0x872AU
#define VIDEO_GET_DEF_PU_WHITE_BALANCE_TEMPERATURE_AUTO_CONTROL 0x872BU
#define VIDEO_GET_DEF_PU_WHITE_BALANCE_COMPONENT_CONTROL        0x872CU
#define VIDEO_GET_DEF_PU_WHITE_BALANCE_COMPONENT_AUTO_CONTROL   0x872DU
#define VIDEO_GET_DEF_PU_DIGITAL_MULTIPLIER_CONTROL             0x872EU
#define VIDEO_GET_DEF_PU_DIGITAL_MULTIPLIER_LIMIT_CONTROL       0x872FU
#define VIDEO_GET_DEF_PU_HUE_AUTO_CONTROL                       0x8730U
#if defined(USB_DEVICE_VIDEO_CLASS_VERSION_1_5) && USB_DEVICE_VIDEO_CLASS_VERSION_1_5
#define VIDEO_GET_DEF_PU_CONTRAST_AUTO_CONTROL 0x8731U
#endif

#define VIDEO_GET_DEF_CT_AE_MODE_CONTROL                0x8742U
#define VIDEO_GET_DEF_CT_EXPOSURE_TIME_ABSOLUTE_CONTROL 0x8744U
#define VIDEO_GET_DEF_CT_FOCUS_ABSOLUTE_CONTROL         0x8746U
#define VIDEO_GET_DEF_CT_FOCUS_RELATIVE_CONTROL         0x8747U
#define VIDEO_GET_DEF_CT_FOCUS_AUTO_CONTROL             0x8748U
#define VIDEO_GET_DEF_CT_IRIS_ABSOLUTE_CONTROL          0x8749U
#define VIDEO_GET_DEF_CT_ZOOM_ABSOLUTE_CONTROL          0x874BU
#define VIDEO_GET_DEF_CT_ZOOM_RELATIVE_CONTROL          0x874CU
#define VIDEO_GET_DEF_CT_PANTILT_ABSOLUTE_CONTROL       0x874DU
#define VIDEO_GET_DEF_CT_PANTILT_RELATIVE_CONTROL       0x874EU
#define VIDEO_GET_DEF_CT_ROLL_ABSOLUTE_CONTROL          0x874FU
#define VIDEO_GET_DEF_CT_ROLL_RELATIVE_CONTROL          0x8750U
#if defined(USB_DEVICE_VIDEO_CLASS_VERSION_1_5) && USB_DEVICE_VIDEO_CLASS_VERSION_1_5
#define VIDEO_GET_DEF_CT_FOCUS_SIMPLE_CONTROL       0x8751U
#define VIDEO_GET_DEF_CT_DIGITAL_WINDOW_CONTROL     0x8752U
#define VIDEO_GET_DEF_CT_REGION_OF_INTEREST_CONTROL 0x8753U
#endif

#define VIDEO_GET_DEF_VS_PROBE_CONTROL                0x8761U
#define VIDEO_GET_DEF_VS_STILL_PROBE_CONTROL          0x8763U
#define VIDEO_GET_DEF_VS_UPDATE_FRAME_SEGMENT_CONTROL 0x8768U
#define VIDEO_GET_DEF_VS_SYNCH_DELAY_CONTROL          0x8769U

#if defined(USB_DEVICE_VIDEO_CLASS_VERSION_1_5) && USB_DEVICE_VIDEO_CLASS_VERSION_1_5
#define VIDEO_GET_DEF_EU_PROFILE_TOOLSET_CONTROL     0x8782U
#define VIDEO_GET_DEF_EU_VIDEO_RESOLUTION_CONTROL    0x8783U
#define VIDEO_GET_DEF_EU_MIN_FRAME_INTERVAL_CONTROL  0x8784U
#define VIDEO_GET_DEF_EU_SLICE_MODE_CONTROL          0x8785U
#define VIDEO_GET_DEF_EU_RATE_CONTROL_MODE_CONTROL   0x8786U
#define VIDEO_GET_DEF_EU_AVERAGE_BITRATE_CONTROL     0x8787U
#define VIDEO_GET_DEF_EU_CPB_SIZE_CONTROL            0x8788U
#define VIDEO_GET_DEF_EU_PEAK_BIT_RATE_CONTROL       0x8789U
#define VIDEO_GET_DEF_EU_QUANTIZATION_PARAMS_CONTROL 0x878AU
#define VIDEO_GET_DEF_EU_LTR_BUFFER_CONTROL          0x878CU
#define VIDEO_GET_DEF_EU_LTR_PICTURE_CONTROL         0x878DU
#define VIDEO_GET_DEF_EU_LTR_VALIDATION_CONTROL      0x878EU
#define VIDEO_GET_DEF_EU_LEVEL_IDC_LIMIT_CONTROL     0x878FU
#define VIDEO_GET_DEF_EU_SEI_PAYLOADTYPE_CONTROL     0x8790U
#define VIDEO_GET_DEF_EU_QP_RANGE_CONTROL            0x8791U
#define VIDEO_GET_DEF_EU_ERROR_RESILIENCY_CONTROL    0x8794U
#endif

/*! @brief Video device class-specific request SET CUR COMMAND */
#define VIDEO_SET_CUR_VC_POWER_MODE_CONTROL 0x0101U

#define VIDEO_SET_CUR_PU_BACKLIGHT_COMPENSATION_CONTROL         0x0121U
#define VIDEO_SET_CUR_PU_BRIGHTNESS_CONTROL                     0x0122U
#define VIDEO_SET_CUR_PU_CONTRACT_CONTROL                       0x0123U
#define VIDEO_SET_CUR_PU_GAIN_CONTROL                           0x0124U
#define VIDEO_SET_CUR_PU_POWER_LINE_FREQUENCY_CONTROL           0x0125U
#define VIDEO_SET_CUR_PU_HUE_CONTROL                            0x0126U
#define VIDEO_SET_CUR_PU_SATURATION_CONTROL                     0x0127U
#define VIDEO_SET_CUR_PU_SHARRNESS_CONTROL                      0x0128U
#define VIDEO_SET_CUR_PU_GAMMA_CONTROL                          0x0129U
#define VIDEO_SET_CUR_PU_WHITE_BALANCE_TEMPERATURE_CONTROL      0x012AU
#define VIDEO_SET_CUR_PU_WHITE_BALANCE_TEMPERATURE_AUTO_CONTROL 0x012BU
#define VIDEO_SET_CUR_PU_WHITE_BALANCE_COMPONENT_CONTROL        0x012CU
#define VIDEO_SET_CUR_PU_WHITE_BALANCE_COMPONENT_AUTO_CONTROL   0x012DU
#define VIDEO_SET_CUR_PU_DIGITAL_MULTIPLIER_CONTROL             0x012EU
#define VIDEO_SET_CUR_PU_DIGITAL_MULTIPLIER_LIMIT_CONTROL       0x012FU
#define VIDEO_SET_CUR_PU_HUE_AUTO_CONTROL                       0x0130U
#if defined(USB_DEVICE_VIDEO_CLASS_VERSION_1_5) && USB_DEVICE_VIDEO_CLASS_VERSION_1_5
#define VIDEO_SET_CUR_PU_CONTRAST_AUTO_CONTROL 0x0131U
#endif

#define VIDEO_SET_CUR_CT_SCANNING_MODE_CONTROL          0x0141U
#define VIDEO_SET_CUR_CT_AE_MODE_CONTROL                0x0142U
#define VIDEO_SET_CUR_CT_AE_PRIORITY_CONTROL            0x0143U
#define VIDEO_SET_CUR_CT_EXPOSURE_TIME_ABSOLUTE_CONTROL 0x0144U
#define VIDEO_SET_CUR_CT_EXPOSURE_TIME_RELATIVE_CONTROL 0x0145U
#define VIDEO_SET_CUR_CT_FOCUS_ABSOLUTE_CONTROL         0x0146U
#define VIDEO_SET_CUR_CT_FOCUS_RELATIVE_CONTROL         0x0147U
#define VIDEO_SET_CUR_CT_FOCUS_AUTO_CONTROL             0x0148U
#define VIDEO_SET_CUR_CT_IRIS_ABSOLUTE_CONTROL          0x0149U
#define VIDEO_SET_CUR_CT_IRIS_RELATIVE_CONTROL          0x014AU
#define VIDEO_SET_CUR_CT_ZOOM_ABSOLUTE_CONTROL          0x014BU
#define VIDEO_SET_CUR_CT_ZOOM_RELATIVE_CONTROL          0x014CU
#define VIDEO_SET_CUR_CT_PANTILT_ABSOLUTE_CONTROL       0x014DU
#define VIDEO_SET_CUR_CT_PANTILT_RELATIVE_CONTROL       0x014EU
#define VIDEO_SET_CUR_CT_ROLL_ABSOLUTE_CONTROL          0x014FU
#define VIDEO_SET_CUR_CT_ROLL_RELATIVE_CONTROL          0x0150U
#define VIDEO_SET_CUR_CT_PRIVACY_CONTROL                0x0151U
#if defined(USB_DEVICE_VIDEO_CLASS_VERSION_1_5) && USB_DEVICE_VIDEO_CLASS_VERSION_1_5
#define VIDEO_SET_CUR_CT_FOCUS_SIMPLE_CONTROL       0x0152U
#define VIDEO_SET_CUR_CT_DIGITAL_WINDOW_CONTROL     0x0153U
#define VIDEO_SET_CUR_CT_REGION_OF_INTEREST_CONTROL 0x0154U
#endif

#define VIDEO_SET_CUR_VS_PROBE_CONTROL                0x0161U
#define VIDEO_SET_CUR_VS_COMMIT_CONTROL               0x0162U
#define VIDEO_SET_CUR_VS_STILL_PROBE_CONTROL          0x0163U
#define VIDEO_SET_CUR_VS_STILL_COMMIT_CONTROL         0x0164U
#define VIDEO_SET_CUR_VS_STILL_IMAGE_TRIGGER_CONTROL  0x0165U
#define VIDEO_SET_CUR_VS_STREAM_ERROR_CODE_CONTROL    0x0166U
#define VIDEO_SET_CUR_VS_GENERATE_KEY_FRAME_CONTROL   0x0167U
#define VIDEO_SET_CUR_VS_UPDATE_FRAME_SEGMENT_CONTROL 0x0168U
#define VIDEO_SET_CUR_VS_SYNCH_DELAY_CONTROL          0x0169U

#if defined(USB_DEVICE_VIDEO_CLASS_VERSION_1_5) && USB_DEVICE_VIDEO_CLASS_VERSION_1_5
#define VIDEO_SET_CUR_EU_SELECT_LAYER_CONTROL        0x0181U
#define VIDEO_SET_CUR_EU_PROFILE_TOOLSET_CONTROL     0x0182U
#define VIDEO_SET_CUR_EU_VIDEO_RESOLUTION_CONTROL    0x0183U
#define VIDEO_SET_CUR_EU_MIN_FRAME_INTERVAL_CONTROL  0x0184U
#define VIDEO_SET_CUR_EU_SLICE_MODE_CONTROL          0x0185U
#define VIDEO_SET_CUR_EU_RATE_CONTROL_MODE_CONTROL   0x0186U
#define VIDEO_SET_CUR_EU_AVERAGE_BITRATE_CONTROL     0x0187U
#define VIDEO_SET_CUR_EU_CPB_SIZE_CONTROL            0x0188U
#define VIDEO_SET_CUR_EU_PEAK_BIT_RATE_CONTROL       0x0189U
#define VIDEO_SET_CUR_EU_QUANTIZATION_PARAMS_CONTROL 0x018AU
#define VIDEO_SET_CUR_EU_SYNC_REF_FRAME_CONTROL      0x018BU
#define VIDEO_SET_CUR_EU_LTR_BUFFER_CONTROL          0x018CU
#define VIDEO_SET_CUR_EU_LTR_PICTURE_CONTROL         0x018DU
#define VIDEO_SET_CUR_EU_LTR_VALIDATION_CONTROL      0x018EU
#define VIDEO_SET_CUR_EU_LEVEL_IDC_LIMIT_CONTROL     0x018FU
#define VIDEO_SET_CUR_EU_SEI_PAYLOADTYPE_CONTROL     0x0190U
#define VIDEO_SET_CUR_EU_QP_RANGE_CONTROL            0x0191U
#define VIDEO_SET_CUR_EU_PRIORITY_CONTROL            0x0192U
#define VIDEO_SET_CUR_EU_START_OR_STOP_LAYER_CONTROL 0x0193U
#define VIDEO_SET_CUR_EU_ERROR_RESILIENCY_CONTROL    0x0194U
#endif

/*! @brief The payload header structure. */
struct video_payload_header {
    uint8_t bHeaderLength; /*!< The payload header length. */
    union {
        uint8_t bmheaderInfo; /*!< The payload header bitmap field. */
        struct
        {
            uint8_t frameIdentifier : 1U; /*!< Frame Identifier. This bit toggles at each frame start boundary and stays
                                             constant for the rest of the frame.*/
            uint8_t endOfFrame      : 1U; /*!< End of Frame. This bit indicates the end of a video frame and is set in the
                                        last video sample that belongs to a frame.*/
            uint8_t
                presentationTimeStamp    : 1U; /*!< Presentation Time Stamp. This bit, when set, indicates the presence of
                                               a PTS field.*/
            uint8_t sourceClockReference : 1U; /*!< Source Clock Reference. This bit, when set, indicates the presence
                                                  of a SCR field.*/
            uint8_t reserved             : 1U; /*!< Reserved. Set to 0. */
            uint8_t stillImage           : 1U; /*!< Still Image. This bit, when set, identifies a video sample that belongs to a
                                         still image.*/
            uint8_t errorBit             : 1U; /*!< Error Bit. This bit, when set, indicates an error in the device streaming.*/
            uint8_t endOfHeader          : 1U; /*!< End of Header. This bit, when set, indicates the end of the BFH fields.*/
        } headerInfoBits;
        struct
        {
            uint8_t FID : 1U; /*!< Frame Identifier. This bit toggles at each frame start boundary and stays constant
                                 for the rest of the frame.*/
            uint8_t EOI : 1U; /*!< End of Frame. This bit indicates the end of a video frame and is set in the last
                                 video sample that belongs to a frame.*/
            uint8_t PTS : 1U; /*!< Presentation Time Stamp. This bit, when set, indicates the presence of a PTS field.*/
            uint8_t SCR : 1U; /*!< Source Clock Reference. This bit, when set, indicates the presence of a SCR field.*/
            uint8_t RES : 1U; /*!< Reserved. Set to 0. */
            uint8_t STI : 1U; /*!< Still Image. This bit, when set, identifies a video sample that belongs to a still
                                 image.*/
            uint8_t ERR : 1U; /*!< Error Bit. This bit, when set, indicates an error in the device streaming.*/
            uint8_t EOH : 1U; /*!< End of Header. This bit, when set, indicates the end of the BFH fields.*/
        } headerInfoBitmap;
    } headerInfoUnion;
    uint32_t dwPresentationTime;      /*!< Presentation time stamp (PTS) field.*/
    uint8_t bSourceClockReference[6]; /*!< Source clock reference (SCR) field.*/
} __PACKED;

/*! @brief The Video probe and commit controls structure.*/
struct video_probe_and_commit_controls {
    union {
        uint8_t bmHint; /*!< Bit-field control indicating to the function what fields shall be kept fixed. */
        struct
        {
            uint8_t dwFrameInterval : 1U; /*!< dwFrameInterval field.*/
            uint8_t wKeyFrameRate   : 1U; /*!< wKeyFrameRate field.*/
            uint8_t wPFrameRate     : 1U; /*!< wPFrameRate field.*/
            uint8_t wCompQuality    : 1U; /*!< wCompQuality field.*/
            uint8_t wCompWindowSize : 1U; /*!< wCompWindowSize field.*/
            uint8_t reserved        : 3U; /*!< Reserved field.*/
        } hintBitmap;
    } hintUnion;
    union {
        uint8_t bmHint; /*!< Bit-field control indicating to the function what fields shall be kept fixed. */
        struct
        {
            uint8_t reserved : 8U; /*!< Reserved field.*/
        } hintBitmap;
    } hintUnion1;
    uint8_t bFormatIndex;              /*!< Video format index from a format descriptor.*/
    uint8_t bFrameIndex;               /*!< Video frame index from a frame descriptor.*/
    uint32_t dwFrameInterval;          /*!< Frame interval in 100ns units.*/
    uint16_t wKeyFrameRate;            /*!< Key frame rate in key-frame per video-frame units.*/
    uint16_t wPFrameRate;              /*!< PFrame rate in PFrame/key frame units.*/
    uint16_t wCompQuality;             /*!< Compression quality control in abstract units 0U (lowest) to 10000U (highest).*/
    uint16_t wCompWindowSize;          /*!< Window size for average bit rate control.*/
    uint16_t wDelay;                   /*!< Internal video streaming interface latency in ms from video data capture to presentation on
                        the USB.*/
    uint32_t dwMaxVideoFrameSize;      /*!< Maximum video frame or codec-specific segment size in bytes.*/
    uint32_t dwMaxPayloadTransferSize; /*!< Specifies the maximum number of bytes that the device can transmit or
                                          receive in a single payload transfer.*/
    uint32_t dwClockFrequency;         /*!< The device clock frequency in Hz for the specified format. This specifies the
                                  units used for the time information fields in the Video Payload Headers in the data
                                  stream.*/
    uint8_t bmFramingInfo;             /*!< Bit-field control supporting the following values: D0 Frame ID, D1 EOF.*/
    uint8_t bPreferedVersion;          /*!< The preferred payload format version supported by the host or device for the
                                  specified bFormatIndex value.*/
    uint8_t bMinVersion;               /*!< The minimum payload format version supported by the device for the specified bFormatIndex
                            value.*/
    uint8_t bMaxVersion;               /*!< The maximum payload format version supported by the device for the specified bFormatIndex
                            value.*/
#if defined(USB_DEVICE_VIDEO_CLASS_VERSION_1_5) && USB_DEVICE_VIDEO_CLASS_VERSION_1_5
    uint8_t bUsage; /*!< This bitmap enables features reported by the bmUsages field of the Video Frame Descriptor.*/
    uint8_t
        bBitDepthLuma;                  /*!< Represents bit_depth_luma_minus8 + 8U, which must be the same as bit_depth_chroma_minus8 +
                           8.*/
    uint8_t bmSettings;                 /*!< A bitmap of flags that is used to discover and control specific features of a temporally
                           encoded video stream.*/
    uint8_t bMaxNumberOfRefFramesPlus1; /*!< Host indicates the maximum number of frames stored for use as references.*/
    uint16_t bmRateControlModes;        /*!< This field contains 4U sub-fields, each of which is a 4U bit number.*/
    uint64_t bmLayoutPerStream;         /*!< This field contains 4U sub-fields, each of which is a 2U byte number.*/
#endif
} __PACKED;

/*! @brief The Video still probe and still commit controls structure.*/
struct video_still_probe_and_commit_controls {
    uint8_t bFormatIndex;              /*!< Video format index from a format descriptor.*/
    uint8_t bFrameIndex;               /*!< Video frame index from a frame descriptor.*/
    uint8_t bCompressionIndex;         /*!< Compression index from a frame descriptor.*/
    uint32_t dwMaxVideoFrameSize;      /*!< Maximum still image size in bytes.*/
    uint32_t dwMaxPayloadTransferSize; /*!< Specifies the maximum number of bytes that the device can transmit or
                                          receive in a single payload transfer.*/
} __PACKED;

struct video_cs_if_vc_header_descriptor {
    uint8_t bLength;
    uint8_t bDescriptorType;
    uint8_t bDescriptorSubType;
    uint16_t bcdUVC;
    uint16_t wTotalLength;
    uint32_t dwClockFrequency;
    uint8_t bInCollection;
    uint8_t baInterfaceNr[];
} __PACKED;

#define VIDEO_SIZEOF_VC_HEADER_DESC(n) (12 + n)

struct video_cs_if_vc_input_terminal_descriptor {
    uint8_t bLength;
    uint8_t bDescriptorType;
    uint8_t bDescriptorSubType;
    uint8_t bTerminalID;
    uint16_t wTerminalType;
    uint8_t bAssocTerminal;
    uint8_t iTerminal;
    uint16_t wObjectiveFocalLenMin;
    uint16_t wObjectiveFocalLenMax;
    uint16_t wOcularFocalLength;
    uint8_t bControlSize;
    uint8_t bmaControls[];
} __PACKED;

#define VIDEO_SIZEOF_VC_INPUT_TERMINAL_DESC(n) (15 + n)

struct video_cs_if_vc_processing_unit_descriptor {
    uint8_t bLength;
    uint8_t bDescriptorType;
    uint8_t bDescriptorSubType;
    uint8_t bUnitID;
    uint8_t bSourceID;
    uint16_t wMaxMultiplier;
    uint8_t bControlSize;
    // uint8_t bmaControls[];
    uint8_t iProcessing;
    uint8_t bmVideoStandards;
} __PACKED;

#define VIDEO_SIZEOF_VC_PROCESSING_UNIT_DESC(n) (10 + n)

struct video_cs_if_vc_output_terminal_descriptor {
    uint8_t bLength;
    uint8_t bDescriptorType;
    uint8_t bDescriptorSubType;
    uint8_t bTerminalID;
    uint16_t wTerminalType;
    uint8_t bAssocTerminal;
    uint8_t bSourceID;
    uint8_t iTerminal;
} __PACKED;

#define VIDEO_SIZEOF_VC_OUTPUT_TERMINAL_DESC 9

struct video_cs_if_vc_extension_unit_descriptor {
    uint8_t bLength;
    uint8_t bDescriptorType;
    uint8_t bDescriptorSubType;
    uint8_t bUnitID;
    uint8_t guidExtensionCode[16];
    uint8_t bNumControls;
    uint8_t bNrInPins;
    // uint8_t baSourceID[];
    uint8_t bControlSize;
    // uint8_t bmControls[]
    uint8_t iExtension;
} __PACKED;

#define VIDEO_SIZEOF_VC_EXTENSION_UNIT_DESC(p, n) (24 + p + n)

struct video_cs_ep_vc_ep_descriptor {
    uint8_t bLength;
    uint8_t bDescriptorType;
    uint8_t bDescriptorSubType;
    uint16_t wMaxTransferSize;
} __PACKED;

#define VIDEO_SIZEOF_VC_EP_DESC 5

struct video_cs_if_vs_input_header_descriptor {
    uint8_t bLength;
    uint8_t bDescriptorType;
    uint8_t bDescriptorSubType;
    uint8_t bNumFormats;
    uint16_t wTotalLength;
    uint8_t bEndpointAddress;
    uint8_t bmInfo;
    uint8_t bTerminalLink;
    uint8_t bStillCaptureMethod;
    uint8_t bTriggerSupport;
    uint8_t bTriggerUsage;
    uint8_t bControlSize;
    uint8_t bmaControls[];
} __PACKED;

#define VIDEO_SIZEOF_VS_INPUT_HEADER_DESC(p, n) (13 + p * n)

struct video_cs_if_vs_output_header_descriptor {
    uint8_t bLength;
    uint8_t bDescriptorType;
    uint8_t bDescriptorSubType;
    uint8_t bNumFormats;
    uint16_t wTotalLength;
    uint8_t bEndpointAddress;
    uint8_t bTerminalLink;
    uint8_t bControlSize;
    uint8_t bmaControls[];
} __PACKED;

#define VIDEO_SIZEOF_VS_OUTPUT_TERMINAL_DESC(p, n) (9 + p * n)

struct video_cs_if_vs_format_uncompressed_descriptor {
    uint8_t bLength;
    uint8_t bDescriptorType;
    uint8_t bDescriptorSubType;
    uint8_t bFormatIndex;
    uint8_t bNumFrameDescriptors;
    uint8_t guidFormat[16];
    uint8_t bBitsPerPixel;
    uint8_t bDefaultFrameIndex;
    uint8_t bAspectRatioX;
    uint8_t bAspectRatioY;
    uint8_t bmInterlaceFlags;
    uint8_t bCopyProtect;
} __PACKED;

#define VIDEO_SIZEOF_VS_FORMAT_UNCOMPRESSED_DESC (27)

struct video_cs_if_vs_frame_uncompressed_descriptor {
    uint8_t bLength;
    uint8_t bDescriptorType;
    uint8_t bDescriptorSubType;
    uint8_t bFrameIndex;
    uint8_t bmCapabilities;
    uint16_t wWidth;
    uint16_t wHeight;
    uint32_t dwMinBitRate;
    uint32_t dwMaxBitRate;
    uint32_t dwMaxVideoFrameBufferSize;
    uint32_t dwDefaultFrameInterval;
    uint8_t bFrameIntervalType;
    uint32_t dwFrameInterval[];
} __PACKED;

#define VIDEO_SIZEOF_VS_FRAME_UNCOMPRESSED_DESC(n) (26 + 4 * (n))

struct video_cs_if_vs_format_mjpeg_descriptor {
    uint8_t bLength;
    uint8_t bDescriptorType;
    uint8_t bDescriptorSubType;
    uint8_t bFormatIndex;
    uint8_t bNumFrameDescriptors;
    uint8_t bmFlags;
    uint8_t bDefaultFrameIndex;
    uint8_t bAspectRatioX;
    uint8_t bAspectRatioY;
    uint8_t bmInterlaceFlags;
    uint8_t bCopyProtect;
} __PACKED;

#define VIDEO_SIZEOF_VS_FORMAT_MJPEG_DESC 11

struct video_cs_if_vs_frame_mjpeg_descriptor {
    uint8_t bLength;
    uint8_t bDescriptorType;
    uint8_t bDescriptorSubType;
    uint8_t bFrameIndex;
    uint8_t bmCapabilities;
    uint16_t wWidth;
    uint16_t wHeight;
    uint32_t dwMinBitRate;
    uint32_t dwMaxBitRate;
    uint32_t dwMaxVideoFrameBufferSize;
    uint32_t dwDefaultFrameInterval;
    uint8_t bFrameIntervalType;
    uint32_t dwFrameInterval[];
} __PACKED;

#define VIDEO_SIZEOF_VS_FRAME_MJPEG_DESC(n) (26 + 4 * (n))

/* H264 Payload - 3.1.1. H264 Video Format Descriptor */
struct video_cs_if_vs_format_h26x_descriptor {
    uint8_t bLength;
    uint8_t bDescriptorType;
    uint8_t bDescriptorSubType;
    uint8_t bFormatIndex;
    uint8_t bNumFrameDescriptors;
    uint8_t guidFormat[16];
    uint8_t bBitsPerPixel;
    uint8_t bDefaultFrameIndex;
    uint8_t bAspectRatioX;
    uint8_t bAspectRatioY;
    uint8_t bmInterfaceFlags;
    uint8_t bCopyProtect;
    uint8_t bVariableSize;
} __PACKED;

#define VIDEO_SIZEOF_VS_FORMAT_H264_DESC 28

/* H264 Payload - 3.1.2. H264 Video Frame Descriptor */
struct video_cs_if_vs_frame_h26x_descriptor {
    uint8_t bLength;
    uint8_t bDescriptorType;
    uint8_t bDescriptorSubType;
    uint8_t bFrameIndex;
    uint8_t bmCapabilities;
    uint16_t wWidth;
    uint16_t wHeight;
    uint32_t dwMinBitRate;
    uint32_t dwMaxBitRate;
    uint32_t dwDefaultFrameInterval;
    uint8_t bFrameIntervalType;
    uint32_t dwBytesPerLine;
    uint32_t dwFrameInterval[];
} __PACKED;

#define VIDEO_SIZEOF_VS_FRAME_H264_DESC(n) (26 + 4 * (n))

struct video_cs_if_vs_colorformat_descriptor {
    uint8_t bLength;
    uint8_t bDescriptorType;
    uint8_t bDescriptorSubType;
    uint8_t bColorPrimaries;
    uint8_t bXferCharacteristics;
    uint8_t bMatrixCoefficients;
} __PACKED;

#define VIDEO_SIZEOF_VS_COLORFORMAT_DESC 6

struct video_vc_input_terminal_bmcontrol_bitmap {
    uint32_t scanning_mode          : 1;
    uint32_t auto_exposure_mode     : 1;
    uint32_t auto_exposure_priority : 1;
    uint32_t exposure_time_absolute : 1;
    uint32_t exposure_time_relative : 1;
    uint32_t focus_absolute         : 1;
    uint32_t focus_relative         : 1;
    uint32_t iris_absolute          : 1;
    uint32_t iris_relative          : 1;
    uint32_t zoom_absolute          : 1;
    uint32_t zoom_relative          : 1;
    uint32_t pantilt_absolute       : 1;
    uint32_t pantilt_relative       : 1;
    uint32_t roll_absolute          : 1;
    uint32_t roll_relative          : 1;
    uint32_t reserved               : 2;
    uint32_t focus_auto             : 1;
    uint32_t pricvcy                : 1;
};

struct video_vc_processing_unit_bmcontrol_bitmap {
    uint16_t brightness          : 1;
    uint16_t contrast            : 1;
    uint16_t hue                 : 1;
    uint16_t saturation          : 1;
    uint16_t sharpness           : 1;
    uint16_t gamma               : 1;
    uint16_t white_bal_temp      : 1;
    uint16_t white_bal_comp      : 1;
    uint16_t backlight_comp      : 1;
    uint16_t gain                : 1;
    uint16_t power_line_freq     : 1;
    uint16_t hue_auto            : 1;
    uint16_t white_bal_temp_auto : 1;
    uint16_t white_bal_comp_auto : 1;
    uint16_t digital_mult        : 1;
    uint16_t digital_mult_limit  : 1;
};

struct video_camera_capabilities {
    uint8_t support_get_request        : 1;
    uint8_t support_set_request        : 1;
    uint8_t disabled_by_automatic_mode : 1;
    uint8_t auto_update_control        : 1;
    uint8_t async_control              : 1;
    uint8_t reserved                   : 3;
};

struct video_autoexposure_mode {
    uint8_t manual_mode            : 1;
    uint8_t auto_mode              : 1;
    uint8_t shutter_priority_mode  : 1;
    uint8_t aperture_priority_mode : 1;
    uint8_t reserved               : 4;
};

#define VIDEO_GUID_YUY2 0x59, 0x55, 0x59, 0x32, 0x00, 0x00, 0x10, 0x00, 0x80, 0x00, 0x00, 0xAA, 0x00, 0x38, 0x9B, 0x71
#define VIDEO_GUID_NV12 0x4E, 0x56, 0x31, 0x32, 0x00, 0x00, 0x10, 0x00, 0x80, 0x00, 0x00, 0xAA, 0x00, 0x38, 0x9B, 0x71
#define VIDEO_GUID_NV21 0x4E, 0x56, 0x32, 0x31, 0x00, 0x00, 0x10, 0x00, 0x80, 0x00, 0x00, 0xAA, 0x00, 0x38, 0x9B, 0x71
#define VIDEO_GUID_M420 0x4D, 0x34, 0x32, 0x30, 0x00, 0x00, 0x10, 0x00, 0x80, 0x00, 0x00, 0xAA, 0x00, 0x38, 0x9B, 0x71
#define VIDEO_GUID_I420 0x49, 0x34, 0x32, 0x30, 0x00, 0x00, 0x10, 0x00, 0x80, 0x00, 0x00, 0xAA, 0x00, 0x38, 0x9B, 0x71
#define VIDEO_GUID_H264 0x48, 0x32, 0x36, 0x34, 0x00, 0x00, 0x10, 0x00, 0x80, 0x00, 0x00, 0xAA, 0x00, 0x38, 0x9B, 0x71

#define VIDEO_VC_TERMINAL_LEN (13 + 18 + 12 + 9)

/*Length of template descriptor: 81 bytes*/
#define VIDEO_VC_DESCRIPTOR_LEN      (8 + 9 + VIDEO_VC_TERMINAL_LEN + 7 + 5)
#define VIDEO_VC_NOEP_DESCRIPTOR_LEN (8 + 9 + VIDEO_VC_TERMINAL_LEN)

// clang-format off
#define VIDEO_VC_DESCRIPTOR_INIT(bFirstInterface, bEndpointAddress, bcdUVC, wTotalLength, dwClockFrequency, stridx)                                                                                          \
    /* Interface Association Descriptor */                                                                                                                                                                   \
    0x08,                                                                                                                                                                                                    \
    USB_DESCRIPTOR_TYPE_INTERFACE_ASSOCIATION,                                                                                                                                                               \
    bFirstInterface,                                                                                                                                                                                         \
    0x02,                                                                                                                                                                                                    \
    USB_DEVICE_CLASS_VIDEO,                                                                                                                                                                                  \
    VIDEO_SC_VIDEO_INTERFACE_COLLECTION,                                                                                                                                                                     \
    0x00,                                                                                                                                                                                                    \
    0x00,                                                                                                                      /* VideoControl Interface Descriptor */                                       \
    0x09,                                                                                                                      /* bLength */                                                                 \
    USB_DESCRIPTOR_TYPE_INTERFACE,                                                                                             /* bDescriptorType */                                                         \
    0x00,                                                                                                                      /* bInterfaceNumber */                                                        \
    0x00,                                                                                                                      /* bAlternateSetting */                                                       \
    0x01,                                                                                                                         /* bNumEndpoints:1 endpoint (interrupt endpoint) */                           \
    USB_DEVICE_CLASS_VIDEO,                                                                                                    /* bInterfaceClass : CC_VIDEO */                                              \
    VIDEO_SC_VIDEOCONTROL,                                                                                                     /* bInterfaceSubClass : SC_VIDEOCONTROL */                                    \
    VIDEO_PC_PROTOCOL_UNDEFINED,                                                                                               /* bInterfaceProtocol : PC_PROTOCOL_UNDEFINED */                              \
    stridx, /* iInterface:Index to string descriptor that contains the string <Your Product Name> */                           /*Class-specific VideoControl Interface Descriptor */                         \
    0x0d,                                                                                                                      /* bLength */                                                                 \
    0x24,                                                                                                                      /* bDescriptorType : CS_INTERFACE */                                          \
    VIDEO_VC_HEADER_DESCRIPTOR_SUBTYPE,                                                                                        /* bDescriptorSubType : VC_HEADER subtype */                                  \
    WBVAL(bcdUVC),                                                                                                             /* bcdUVC : Revision of class specification that this device is based upon.*/ \
    WBVAL(wTotalLength),                                                                                                       /* wTotalLength  */                                                           \
    DBVAL(dwClockFrequency),                                                                                                   /* dwClockFrequency : 0x005b8d80 -> 6,000,000 == 6MHz*/                       \
    0x01,                                                                                                                      /* bInCollection : Number of streaming interfaces. */                         \
    (uint8_t)(bFirstInterface + 1), /* baInterfaceNr(0) : VideoStreaming interface 1 belongs to this VideoControl interface.*/ /* Input Terminal 1 -> Processing Unit 2 -> Output Terminal 3 */              \
    0x12,                                                                                                                                                                                                    \
    0x24,                                                                                                                                                                                                    \
    VIDEO_VC_INPUT_TERMINAL_DESCRIPTOR_SUBTYPE,                                                                                                                                                              \
    0x01,                    /* bTerminalID */                                                                                                                                                               \
    WBVAL(VIDEO_ITT_CAMERA), /* wTerminalType : 0x0201 Camera Sensor*/                                                                                                                                       \
    0x00,                    /* bAssocTerminal */                                                                                                                                                            \
    0x00,                    /* iTerminal */                                                                                                                                                                 \
    WBVAL(0x0000),           /* wObjectiveFocalLengthMin */                                                                                                                                                  \
    WBVAL(0x0000),           /* wObjectiveFocalLengthMax */                                                                                                                                                  \
    WBVAL(0x0000),           /* wOcularFocalLength */                                                                                                                                                        \
    0x03,                    /* bControlSize */                                                                                                                                                              \
    0x00, 0x00, 0x00,        /* bmControls */                                                                                                                                                                \
    0x0c,                                                                                                                                                                                                    \
    0x24,                                                                                                                                                                                                    \
    VIDEO_VC_PROCESSING_UNIT_DESCRIPTOR_SUBTYPE,                                                                                                                                                             \
    0x02,       /* bUnitID  */                                                                                                                                                                               \
    0x01,       /* bSourceID */                                                                                                                                                                              \
    0x00, 0x00, /* wMaxMultiplier  */                                                                                                                                                                        \
    0x02,       /* bControlSize   */                                                                                                                                                                         \
    0x00, 0x00, /* bmControls */                                                                                                                                                                             \
    0x00,       /* iProcessing */                                                                                                                                                                            \
    0x00,       /* bmVideoStandards */                                                                                                                                                                       \
    0x09,                                                                                                                                                                                                    \
    0x24,                                                                                                                                                                                                    \
    VIDEO_VC_OUTPUT_TERMINAL_DESCRIPTOR_SUBTYPE,                                                                                                                                                             \
    0x03, /* bTerminalID */                                                                                                                                                                                  \
    WBVAL(VIDEO_TT_STREAMING),                                                                                                                                                                               \
    0x00,                         /* bAssocTerminal   */                                                                                                                                                     \
    0x02,                         /* bSourceID   */                                                                                                                                                          \
    0x00,                         /* iTerminal   */                                                                                                                                                          \
    0x07,                         /* bLength */                                                                                                                                                              \
    USB_DESCRIPTOR_TYPE_ENDPOINT, /* bDescriptorType */                                                                                                                                                      \
    bEndpointAddress,             /* bEndpointAddress */                                                                                                                                                     \
    0x03,                         /* bmAttributes */                                                                                                                                                         \
    0x10, 0x00,                   /* wMaxPacketSize */                                                                                                                                                       \
    0x08,                         /* bInterval */                                                                                                                                                            \
    /* Class-specific VC Interrupt Endpoint Descriptor */                                                                                                                                                    \
    0x05, 0x25, 0x03, 0x10, 0x00

#define VIDEO_VC_NOEP_DESCRIPTOR_INIT(bFirstInterface, bEndpointAddress, bcdUVC, wTotalLength, dwClockFrequency, stridx)                                                                                          \
    /* Interface Association Descriptor */                                                                                                                                                                   \
    0x08,                                                                                                                                                                                                    \
    USB_DESCRIPTOR_TYPE_INTERFACE_ASSOCIATION,                                                                                                                                                               \
    bFirstInterface,                                                                                                                                                                                         \
    0x02,                                                                                                                                                                                                    \
    USB_DEVICE_CLASS_VIDEO,                                                                                                                                                                                  \
    VIDEO_SC_VIDEO_INTERFACE_COLLECTION,                                                                                                                                                                     \
    0x00,                                                                                                                                                                                                    \
    0x00,                                                                                                                      /* VideoControl Interface Descriptor */                                       \
    0x09,                                                                                                                      /* bLength */                                                                 \
    USB_DESCRIPTOR_TYPE_INTERFACE,                                                                                             /* bDescriptorType */                                                         \
    0x00,                                                                                                                      /* bInterfaceNumber */                                                        \
    0x00,                                                                                                                      /* bAlternateSetting */                                                       \
    0x00,                                                                                                                         /* bNumEndpoints:1 endpoint (interrupt endpoint) */                           \
    USB_DEVICE_CLASS_VIDEO,                                                                                                    /* bInterfaceClass : CC_VIDEO */                                              \
    VIDEO_SC_VIDEOCONTROL,                                                                                                     /* bInterfaceSubClass : SC_VIDEOCONTROL */                                    \
    VIDEO_PC_PROTOCOL_UNDEFINED,                                                                                               /* bInterfaceProtocol : PC_PROTOCOL_UNDEFINED */                              \
    stridx, /* iInterface:Index to string descriptor that contains the string <Your Product Name> */                           /*Class-specific VideoControl Interface Descriptor */                         \
    0x0d,                                                                                                                      /* bLength */                                                                 \
    0x24,                                                                                                                      /* bDescriptorType : CS_INTERFACE */                                          \
    VIDEO_VC_HEADER_DESCRIPTOR_SUBTYPE,                                                                                        /* bDescriptorSubType : VC_HEADER subtype */                                  \
    WBVAL(bcdUVC),                                                                                                             /* bcdUVC : Revision of class specification that this device is based upon.*/ \
    WBVAL(wTotalLength),                                                                                                       /* wTotalLength  */                                                           \
    DBVAL(dwClockFrequency),                                                                                                   /* dwClockFrequency : 0x005b8d80 -> 6,000,000 == 6MHz*/                       \
    0x01,                                                                                                                      /* bInCollection : Number of streaming interfaces. */                         \
    (uint8_t)(bFirstInterface + 1), /* baInterfaceNr(0) : VideoStreaming interface 1 belongs to this VideoControl interface.*/ /* Input Terminal 1 -> Processing Unit 2 -> Output Terminal 3 */              \
    0x12,                                                                                                                                                                                                    \
    0x24,                                                                                                                                                                                                    \
    VIDEO_VC_INPUT_TERMINAL_DESCRIPTOR_SUBTYPE,                                                                                                                                                              \
    0x01,                    /* bTerminalID */                                                                                                                                                               \
    WBVAL(VIDEO_ITT_CAMERA), /* wTerminalType : 0x0201 Camera Sensor*/                                                                                                                                       \
    0x00,                    /* bAssocTerminal */                                                                                                                                                            \
    0x00,                    /* iTerminal */                                                                                                                                                                 \
    WBVAL(0x0000),           /* wObjectiveFocalLengthMin */                                                                                                                                                  \
    WBVAL(0x0000),           /* wObjectiveFocalLengthMax */                                                                                                                                                  \
    WBVAL(0x0000),           /* wOcularFocalLength */                                                                                                                                                        \
    0x03,                    /* bControlSize */                                                                                                                                                              \
    0x00, 0x00, 0x00,        /* bmControls */                                                                                                                                                                \
    0x0c,                                                                                                                                                                                                    \
    0x24,                                                                                                                                                                                                    \
    VIDEO_VC_PROCESSING_UNIT_DESCRIPTOR_SUBTYPE,                                                                                                                                                             \
    0x02,       /* bUnitID  */                                                                                                                                                                               \
    0x01,       /* bSourceID */                                                                                                                                                                              \
    0x00, 0x00, /* wMaxMultiplier  */                                                                                                                                                                        \
    0x02,       /* bControlSize   */                                                                                                                                                                         \
    0x00, 0x00, /* bmControls */                                                                                                                                                                             \
    0x00,       /* iProcessing */                                                                                                                                                                            \
    0x00,       /* bmVideoStandards */                                                                                                                                                                       \
    0x09,                                                                                                                                                                                                    \
    0x24,                                                                                                                                                                                                    \
    VIDEO_VC_OUTPUT_TERMINAL_DESCRIPTOR_SUBTYPE,                                                                                                                                                             \
    0x03, /* bTerminalID */                                                                                                                                                                                  \
    WBVAL(VIDEO_TT_STREAMING),                                                                                                                                                                               \
    0x00,                         /* bAssocTerminal   */                                                                                                                                                     \
    0x02,                         /* bSourceID   */                                                                                                                                                          \
    0x00                          /* iTerminal   */                                                                                                                                                          \

#define VIDEO_VS_DESCRIPTOR_INIT(bInterfaceNumber, bAlternateSetting, bNumEndpoints)        \
    /* Video Streaming (VS) Interface Descriptor */                                         \
    0x09,                              /* bLength */                                        \
    USB_DESCRIPTOR_TYPE_INTERFACE, /* bDescriptorType : INTERFACE */                        \
    bInterfaceNumber,              /* bInterfaceNumber: Index of this interface */          \
    bAlternateSetting,             /* bAlternateSetting: Index of this alternate setting */ \
    bNumEndpoints,                 /* bNumEndpoints : 0 endpoints – no bandwidth used*/     \
    0x0e,                          /* bInterfaceClass : CC_VIDEO */                         \
    0x02,                          /* bInterfaceSubClass : SC_VIDEOSTREAMING */             \
    0x00,                          /* bInterfaceProtocol : PC_PROTOCOL_UNDEFINED */         \
    0x00                           /* iInterface : unused */

#define VIDEO_VS_INPUT_HEADER_DESCRIPTOR_INIT(bNumFormats, wTotalLength, bEndpointAddress, ...)                         \
    /*Class-specific VideoStream Header Descriptor (Input) */                                                           \
    0x0d + PP_NARG(__VA_ARGS__),                                                                                        \
    0x24,                                                                                                               \
    VIDEO_VS_INPUT_HEADER_DESCRIPTOR_SUBTYPE,                                                                           \
    bNumFormats, /* bNumFormats : One format descriptor follows. */                                                     \
    WBVAL(wTotalLength),                                                                                                \
    bEndpointAddress,                                                                                                   \
    0x00,                 /* bmInfo : No dynamic format change supported. */                                            \
    0x03,                 /* bTerminalLink : This VideoStreaming interface supplies terminal ID 2 (Output Terminal). */ \
    0x00,                 /* bStillCaptureMethod : Device supports still image capture method 0. */                     \
    0x00,                 /* bTriggerSupport : Hardware trigger supported for still image capture */                    \
    0x00,                 /* bTriggerUsage : Hardware trigger should initiate a still image capture. */                 \
    0x01,                 /* bControlSize : Size of the bmaControls field */                                            \
    __VA_ARGS__           /* bmaControls : No VideoStreaming specific controls are supported.*/

#define VIDEO_VS_OUTPUT_HEADER_DESCRIPTOR_INIT(bNumFormats, wTotalLength, bEndpointAddress, ...)                        \
    /*Class-specific VideoStream Header Descriptor (Input) */                                                           \
    0x0d + PP_NARG(__VA_ARGS__),                                                                                        \
    0x24,                                                                                                               \
    VIDEO_VS_OUTPUT_HEADER_DESCRIPTOR_SUBTYPE,                                                                          \
    bNumFormats, /* bNumFormats : One format descriptor follows. */                                                     \
    WBVAL(wTotalLength),                                                                                                \
    bEndpointAddress,                                                                                                   \
    0x00,                 /* bmInfo : No dynamic format change supported. */                                            \
    0x03,                 /* bTerminalLink : This VideoStreaming interface supplies terminal ID 2 (Output Terminal). */ \
    0x00,                 /* bStillCaptureMethod : Device supports still image capture method 0. */                     \
    0x00,                 /* bTriggerSupport : Hardware trigger supported for still image capture */                    \
    0x00,                 /* bTriggerUsage : Hardware trigger should initiate a still image capture. */                 \
    PP_NARG(__VA_ARGS__), /* bControlSize : Size of the bmaControls field */                                            \
    __VA_ARGS__           /* bmaControls : No VideoStreaming specific controls are supported.*/

#define VIDEO_VS_FORMAT_UNCOMPRESSED_DESCRIPTOR_INIT(bFormatIndex, bNumFrameDescriptors, GUIDFormat)                                                              \
    /*Payload Format(UNCOMPRESSED) Descriptor */                                                                                                                  \
    0x1b,                                                                                                                                                         \
    0x24,                                                                                                                                                         \
    VIDEO_VS_FORMAT_UNCOMPRESSED_DESCRIPTOR_SUBTYPE, /* bDescriptorSubType : VS_FORMAT_UNCOMPRESSED subtype */                                                    \
    bFormatIndex,                                    /* bFormatIndex : First (and only) format descriptor */                                                      \
    bNumFrameDescriptors,                            /* bNumFrameDescriptors : One frame descriptor for this format follows. */                                   \
    GUIDFormat,                                      /* GUID Format YUY2 {*************-0010-8000-00AA00389B71} */                                                \
    0x10,                                            /* bBitsPerPixel : Number of bits per pixel used to specify color in the decoded video frame - 16 for yuy2*/ \
    0x01,                                            /* bDefaultFrameIndex : Default frame index is 1. */                                                         \
    0x00,                                            /* bAspectRatioX : Non-interlaced stream not required. */                                                    \
    0x00,                                            /* bAspectRatioY : Non-interlaced stream not required. */                                                    \
    0x00,                                            /* bmInterlaceFlags : Non-interlaced stream */                                                               \
    0x00                                             /* bCopyProtect : No restrictions imposed on the duplication of this video stream. */

#define VIDEO_VS_FRAME_UNCOMPRESSED_DESCRIPTOR_INIT(bFrameIndex, wWidth, wHeight, dwMinBitRate, dwMaxBitRate,           \
                                                    dwMaxVideoFrameBufferSize, dwDefaultFrameInterval, bFrameIntervalType, ...) \
    0x1a + PP_NARG(__VA_ARGS__),                                                                                                               \
    0x24,                                                                                                           \
    VIDEO_VS_FRAME_UNCOMPRESSED_DESCRIPTOR_SUBTYPE,                                                                 \
    bFrameIndex,                                                                                                    \
    0x00,                                                                                                           \
    WBVAL(wWidth),                                                                                                  \
    WBVAL(wHeight),                                                                                                 \
    DBVAL(dwMinBitRate),                                                                                            \
    DBVAL(dwMaxBitRate),                                                                                            \
    DBVAL(dwMaxVideoFrameBufferSize),                                                                               \
    dwDefaultFrameInterval,                  /* dwDefaultFrameInterval : 1,000,000 * 100ns -> 10 FPS */                                                                                         \
    bFrameIntervalType,                      /* bFrameIntervalType : Indicates how the frame interval can be programmed. 0: Continuous frame interval 1..255: The number of discrete frame   */ \
    __VA_ARGS__

#define VIDEO_VS_FORMAT_MJPEG_DESCRIPTOR_INIT(bFormatIndex, bNumFrameDescriptors)                    \
    /*Payload Format(MJPEG) Descriptor */                                                            \
    0x0b,                     /* bLength */                                                          \
    0x24,                 /* bDescriptorType : CS_INTERFACE */                                       \
    0x06,                 /* bDescriptorSubType : VS_FORMAT_MJPEG subtype */                         \
    bFormatIndex,         /* bFormatIndex : First (and only) format descriptor */                    \
    bNumFrameDescriptors, /* bNumFrameDescriptors : One frame descriptor for this format follows. */ \
    0x00,                 /* bmFlags : Uses fixed size samples.. */                                  \
    0x01,                 /* bDefaultFrameIndex : Default frame index is 1. */                       \
    0x00,                 /* bAspectRatioX : Non-interlaced stream – not required. */                \
    0x00,                 /* bAspectRatioY : Non-interlaced stream – not required. */                \
    0x00,                 /* bmInterlaceFlags : Non-interlaced stream */                             \
    0x00                  /* bCopyProtect : No restrictions imposed on the duplication of this video stream. */

#define VIDEO_VS_FRAME_MJPEG_DESCRIPTOR_INIT(bFrameIndex, wWidth, wHeight, dwMinBitRate, dwMaxBitRate,                                                                                          \
                                             dwMaxVideoFrameBufferSize, dwDefaultFrameInterval, bFrameIntervalType, ...)                                                                        \
    0x1a + PP_NARG(__VA_ARGS__),                 /* bLength */                                                                                                                                  \
    0x24,                                    /* bDescriptorType : CS_INTERFACE */                                                                                                               \
    VIDEO_VS_FRAME_MJPEG_DESCRIPTOR_SUBTYPE, /* bDescriptorSubType : VS_FRAME_MJPEG */                                                                                                          \
    bFrameIndex,                             /* bFrameIndex : First (and only) frame descriptor */                                                                                              \
    0x00,                                    /* bmCapabilities : Still images using capture method 0 are supported at this frame setting.D1: Fixed frame-rate. */                               \
    WBVAL(wWidth),                           /* wWidth (2bytes): Width of frame is 128 pixels. */                                                                                               \
    WBVAL(wHeight),                          /* wHeight (2bytes): Height of frame is 64 pixels. */                                                                                              \
    DBVAL(dwMinBitRate),                     /* dwMinBitRate (4bytes): Min bit rate in bits/s  */                                                                                               \
    DBVAL(dwMaxBitRate),                     /* dwMaxBitRate (4bytes): Max bit rate in bits/s  */                                                                                               \
    DBVAL(dwMaxVideoFrameBufferSize),        /* dwMaxVideoFrameBufSize (4bytes): Maximum video or still frame size, in bytes. */                                                                \
    dwDefaultFrameInterval,                  /* dwDefaultFrameInterval : 1,000,000 * 100ns -> 10 FPS */                                                                                         \
    bFrameIntervalType,                      /* bFrameIntervalType : Indicates how the frame interval can be programmed. 0: Continuous frame interval 1..255: The number of discrete frame   */ \
    __VA_ARGS__

#define VIDEO_VS_FORMAT_H264_DESCRIPTOR_INIT(bFormatIndex, bNumFrameDescriptors)                     \
    /*Payload Format(H.264) Descriptor */                                                            \
    0x1c,                 /* bLength */                                                              \
    0x24,                 /* bDescriptorType : CS_INTERFACE */                                       \
    VIDEO_VS_FORMAT_FRAME_BASED_DESCRIPTOR_SUBTYPE,  /* bDescriptorSubType : VS_FORMAT_FRAME_BASED subtype */\
    bFormatIndex,         /* bFormatIndex : First (and only) format descriptor */                    \
    bNumFrameDescriptors, /* bNumFrameDescriptors : One frame descriptor for this format follows. */ \
    VIDEO_GUID_H264,                                                                                 \
    0x00,                 /* bmFlags : Uses fixed size samples.. */                                  \
    0x01,                 /* bDefaultFrameIndex : Default frame index is 1. */                       \
    0x00,                 /* bAspectRatioX : Non-interlaced stream – not required. */                \
    0x00,                 /* bAspectRatioY : Non-interlaced stream – not required. */                \
    0x00,                 /* bmInterlaceFlags : Non-interlaced stream */                             \
    0x00,                 /* bCopyProtect : No restrictions imposed on the duplication of this video stream. */ \
    0x00                  /* Variable size: False */

#define VIDEO_VS_FRAME_H264_DESCRIPTOR_INIT(bFrameIndex, wWidth, wHeight, dwMinBitRate, dwMaxBitRate,                                                                                                 \
                                            dwDefaultFrameInterval, bFrameIntervalType, ...)                                                                                                          \
    0x1a + PP_NARG(__VA_ARGS__),                       /* bLength */                                                                                                                                  \
    0x24,                                          /* bDescriptorType : CS_INTERFACE */                                                                                                               \
    VIDEO_VS_FRAME_FRAME_BASED_DESCRIPTOR_SUBTYPE, /* bDescriptorSubType : VS_FRAME_BASED */                                                                                                          \
    bFrameIndex,                                   /* bFrameIndex : First (and only) frame descriptor */                                                                                              \
    0x00,                                          /* bmCapabilities : Still images using capture method 0 are supported at this frame setting.D1: Fixed frame-rate. */                               \
    WBVAL(wWidth),                                 /* wWidth (2bytes): Width of frame is 128 pixels. */                                                                                               \
    WBVAL(wHeight),                                /* wHeight (2bytes): Height of frame is 64 pixels. */                                                                                              \
    DBVAL(dwMinBitRate),                           /* dwMinBitRate (4bytes): Min bit rate in bits/s  */                                                                                               \
    DBVAL(dwMaxBitRate),                           /* dwMaxBitRate (4bytes): Max bit rate in bits/s  */                                                                                               \
    dwDefaultFrameInterval,                        /* dwDefaultFrameInterval : 1,000,000 * 100ns -> 10 FPS */                                                                                         \
    bFrameIntervalType,                            /* bFrameIntervalType : Indicates how the frame interval can be programmed. 0: Continuous frame interval 1..255: The number of discrete frame   */ \
    DBVAL(0x00),                                   /* dwBytesPerLine (4bytes) */                                                                                                                      \
    __VA_ARGS__

#define VIDEO_VS_COLOR_MATCHING_DESCRIPTOR_INIT()                                      \
    0x06,                                    /* bLength */                             \
    0x24,                                    /* bDescriptorType : CS_INTERFACE */      \
    VIDEO_VS_COLORFORMAT_DESCRIPTOR_SUBTYPE, /* bDescriptorSubType : VS_COLORFORMAT */ \
    0x01,                                    /* bColorPrimaries */                     \
    0x01,                                    /* bTransferCharacteristics */            \
    0x04                                     /* bMatrixCoefficients */

// clang-format on
#endif /*USB_VIDEO_H */