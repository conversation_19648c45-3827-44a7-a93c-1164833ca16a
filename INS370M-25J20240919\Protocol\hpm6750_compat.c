/**
 * @file hpm6750_compat.c
 * @brief HPM6750项目升级和版本查询模块完整移植
 * <AUTHOR> from HPM6750_INS-370M-SD-OK project
 * @date 2024
 */

#include "hpm6750_compat.h"
#include "computerFrameParse.h"
#include "fpgad.h"
#include "string.h"
#include "stdio.h"

// 全局变量定义（移植自HPM6750）
uint8_t g_StartUpdateFirm = 0;      // 升级模式标志
uint8_t g_UpdateBackFlag = 1;       // 升级反馈标志
uint8_t g_UpdateSuccessful = 0;     // 升级成功标志
uint8_t g_ucSystemResetFlag = 0;    // 系统重启标志

// 升级相关地址定义（需要根据GD32F4xx调整）
#define APP_UPDATE_ADDRESS      0x08020000  // 升级固件存储地址
#define APP_UPDATE_CFG_ADDR     0x08010000  // 升级配置存储地址
#define FLASH_WRITE_SECTOR_SIZE 0x1000      // Flash扇区大小

// 移植自HPM6750的数据结构
typedef struct {
    uint8_t rxbuffer[1024];
    uint16_t rxlen;
} dmauart_t, *p_dmauart_t;

// 移植自HPM6750的升级处理函数
void ParaUpdateHandle(uint8_t *pucBuf, uint16_t usIndex, uint16_t usTotalBao, uint8_t ucLen)
{
    static uint32_t uiOffsetAddr = 0, uiLastBaoInDex = 1, flag = 0;
    uint8_t UpdateFlagBuff[5] = {0};

    if (uiLastBaoInDex == usIndex) {
        return;
    }

    if (flag == 0) {
        flag = 1;
        if (0 != usIndex) {
            g_UpdateBackFlag = 2; // 反馈标志(0x01-正常/02-异常)
            return;
        }
    }

    if (usIndex == 0) {
        uiOffsetAddr = 0;
        // 擦除Flash扇区（需要根据GD32F4xx实现）
        for (int i = 0; i < 95; i++) {
            // Drv_FlashErase(APP_UPDATE_ADDRESS + i * FLASH_WRITE_SECTOR_SIZE);
        }
        // 写入Flash（需要根据GD32F4xx实现）
        // Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS + uiOffsetAddr, ucLen);
        uiOffsetAddr += ucLen;
    } else {
        // 写入Flash（需要根据GD32F4xx实现）
        // Drv_FlashWrite(pucBuf, APP_UPDATE_ADDRESS + uiOffsetAddr, ucLen);
        uiOffsetAddr += ucLen;
    }

    // 保存升级标志
    if (usIndex >= usTotalBao - 1) {
        UpdateFlagBuff[0] = 0xC5;
        UpdateFlagBuff[4] = (uint8_t)(uiOffsetAddr >> 24);
        UpdateFlagBuff[3] = (uint8_t)(uiOffsetAddr >> 16);
        UpdateFlagBuff[2] = (uint8_t)(uiOffsetAddr >> 8);
        UpdateFlagBuff[1] = (uint8_t)(uiOffsetAddr);

        // 写入升级配置（需要根据GD32F4xx实现）
        // Drv_FlashErase(APP_UPDATE_CFG_ADDR);
        // Drv_FlashWrite(UpdateFlagBuff, APP_UPDATE_CFG_ADDR, sizeof(UpdateFlagBuff));

        uiOffsetAddr = 0;
    }

    uiLastBaoInDex = usIndex;
}

// 移植自HPM6750的协议处理函数
void UartDmaRecSetPara(p_dmauart_t pdmauart)
{
    unsigned short Type = 0; // 报文类型

    if (pdmauart == NULL || pdmauart->rxbuffer == NULL) {
        return;
    }

    // 从接收缓冲区提取类型
    memcpy(&Type, &pdmauart->rxbuffer[3], 2);

    switch (Type) {
        case SETPARA_TYPE0_UPDATE_START: // 软件升级开始命令
            SetParaUpdateStart(pdmauart);
            break;

        case SETPARA_TYPE0_UPDATE_SEND: // 发送升级包命令
            SetParaUpdateSend(pdmauart);
            break;

        case SETPARA_TYPE0_UPDATE_END: // 升级包完成命令
            SetParaUpdateEnd(pdmauart);
            break;

        case SETPARA_TYPE0_UPDATE_STOP: // 升级终止命令
            SetParaUpdateStop(pdmauart);
            break;

        case SETPARA_TYPE0_readver: // 读取版本号
            ReadVersion(pdmauart);
            break;

        default:
            // 其他命令暂不处理
            break;
    }
}

// 升级开始命令处理
void SetParaUpdateStart(p_dmauart_t pdmauart)
{
    // 设置升级模式
    g_StartUpdateFirm = 1;
    g_UpdateBackFlag = 1;

    // 发送升级开始确认响应
    uint8_t response[8];
    response[0] = 0xFA;
    response[1] = 0x55;
    response[2] = 0xAA;
    response[3] = 0xF6;
    response[4] = 0x01; // 确认进入升级模式
    response[5] = 0x00;
    response[6] = 0x00;
    response[7] = 0xFF;

    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, 8, response);
}

// 升级数据包处理
void SetParaUpdateSend(p_dmauart_t pdmauart)
{
    // 解析升级数据包
    uint16_t package_index = (pdmauart->rxbuffer[0] << 8) | pdmauart->rxbuffer[1];
    uint16_t total_packages = (pdmauart->rxbuffer[2] << 8) | pdmauart->rxbuffer[3];
    uint8_t data_len = pdmauart->rxlen - 4;

    // 调用升级处理函数
    ParaUpdateHandle(&pdmauart->rxbuffer[4], package_index, total_packages, data_len);

    // 发送升级进度响应
    uint8_t response[8];
    response[0] = 0xFA;
    response[1] = 0x55;
    response[2] = 0x80; // 升级响应命令
    response[3] = 0x01;
    response[4] = package_index >> 8;
    response[5] = package_index & 0xFF;
    response[6] = g_UpdateBackFlag; // 状态：成功/失败
    response[7] = 0xFF;

    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, 8, response);
}

// 升级完成命令处理
void SetParaUpdateEnd(p_dmauart_t pdmauart)
{
    g_StartUpdateFirm = 0;
    g_ucSystemResetFlag = 1;

    // 发送升级完成响应
    uint8_t response[8];
    response[0] = 0xFA;
    response[1] = 0x55;
    response[2] = 0xAA;
    response[3] = 0xF7;
    response[4] = 0x01; // 升级完成
    response[5] = 0x00;
    response[6] = 0x00;
    response[7] = 0xFF;

    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, 8, response);

    // 如果升级成功，重启系统
    if ((g_ucSystemResetFlag == 1) && (g_UpdateSuccessful == 1)) {
        // 系统重启（需要根据GD32F4xx实现）
        // Drv_SystemReset();
    }
}

// 升级终止命令处理
void SetParaUpdateStop(p_dmauart_t pdmauart)
{
    g_StartUpdateFirm = 0;

    // 发送升级终止响应
    uint8_t response[8];
    response[0] = 0xFA;
    response[1] = 0x55;
    response[2] = 0xAA;
    response[3] = 0xF8;
    response[4] = 0x01; // 升级终止
    response[5] = 0x00;
    response[6] = 0x00;
    response[7] = 0xFF;

    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, 8, response);
}

// 版本查询处理（移植自HPM6750）
void ReadVersion(p_dmauart_t pdmauart)
{
    uint8_t frame[100];
    uint16_t len;

    // 确保版本信息已初始化
    extern void comm_init_version_info(void);
    comm_init_version_info();

    frame[0] = 0xFA;
    frame[1] = 0x55;
    frame[2] = 0xAA;
    frame[3] = 0xF5;

    // 复制版本信息
    extern AppSettingTypeDef hSetting;
    uint16_t arm1_len = strlen((char*)hSetting.ARM1_FW_Ver);
    uint16_t arm2_len = strlen((char*)hSetting.ARM2_FW_Ver);
    uint16_t fpga_len = strlen((char*)hSetting.FPGA_FW_Ver);

    // 如果版本字符串为空，使用默认版本
    if (arm1_len == 0) {
        strcpy((char*)hSetting.ARM1_FW_Ver, "ARM1_VER: 1.1.2\r\n");
        arm1_len = strlen((char*)hSetting.ARM1_FW_Ver);
    }
    if (arm2_len == 0) {
        strcpy((char*)hSetting.ARM2_FW_Ver, "ARM2_VER: 1.0.1\r\n");
        arm2_len = strlen((char*)hSetting.ARM2_FW_Ver);
    }
    if (fpga_len == 0) {
        strcpy((char*)hSetting.FPGA_FW_Ver, "FPGA_VER: 1.0.0");
        fpga_len = strlen((char*)hSetting.FPGA_FW_Ver);
    }

    // 复制版本信息到响应帧
    memcpy((void*)&frame[4], (void*)&hSetting.ARM1_FW_Ver, arm1_len);
    len = arm1_len;
    memcpy((void*)&frame[4 + len], (void*)&hSetting.ARM2_FW_Ver, arm2_len);
    len += arm2_len;
    memcpy((void*)&frame[4 + len], (void*)&hSetting.FPGA_FW_Ver, fpga_len);
    len += fpga_len;
    len += 6;
    frame[len - 2] = 0x00;
    frame[len - 1] = 0xFF;

    Uart_SendMsg(UART_TXPORT_COMPLEX_8, 0, len, frame);
}

// 移植自HPM6750的增强版数据解析函数
void analysisRxdata_enhanced(void)
{
    int i, j, bfind2 = 0;
    int rxlenbx = grxlen, isbkexit = 0;

    for (i = 0; i < grxlen; i++) {
        if (grxbuffer[(grxst + i) % U4RX_MAXCOUNT] == 0xAF && 
            grxbuffer[(grxst + i + 1) % U4RX_MAXCOUNT] == 0x55 && 
            grxbuffer[(grxst + i + 2) % U4RX_MAXCOUNT] == 0xFA) {
            
            rxlenbx = (int)((grxbuffer[(grxst + 6) % U4RX_MAXCOUNT] << 8) + 
                           grxbuffer[(grxst + 5) % U4RX_MAXCOUNT]);
            gframeParsebuf[0] = 0xaf;
            gframeParsebuf[1] = 0x55;
            gframeParsebuf[2] = 0xfa;
            
            for (j = i + 3; j < grxlen; j++) {
                gframeParsebuf[j - i] = grxbuffer[(grxst + j) % U4RX_MAXCOUNT];
                
                if (grxbuffer[(grxst + j) % U4RX_MAXCOUNT] == 0xAF && 
                    grxbuffer[(grxst + j + 1) % U4RX_MAXCOUNT] == 0x55 && 
                    grxbuffer[(grxst + j + 2) % U4RX_MAXCOUNT] == 0xFA) {
                    bfind2 = 1;
                    break;
                }

                if (j == rxlenbx - 1 && 
                    grxbuffer[(grxst + j - 1) % U4RX_MAXCOUNT] == 0x00 && 
                    grxbuffer[(grxst + j) % U4RX_MAXCOUNT] == 0xff) {
                    bfind2 = 1;
                    j++;
                    break;
                }
            }

            if (bfind2 == 0)
                break;

            grxst = (grxst + j) % U4RX_MAXCOUNT;
            grxlen -= j;
            isbkexit = 0;
            break;
        }
    }

    if (bfind2) {
        // 移植HPM6750的处理方式：调用UartDmaRecSetPara处理协议
        dmauart_t uart_data;
        uart_data.rxlen = j - i;
        memcpy(uart_data.rxbuffer, gframeParsebuf, uart_data.rxlen);
        UartDmaRecSetPara(&uart_data);
        
        // 同时保持原有的frameParse处理（用于版本查询和升级）
        int frame_len = j - i;
        frameParse(gframeParsebuf, frame_len);
    }
}
