//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：bsp_sd_fatfs.h
// 文件标识：
// 文件摘要：SD卡FatFS文件系统操作接口
//
// 当前版本：V1.0
// 作    者：迁移自HPM6750项目
// 完成时间：2024.12.19
//---------------------------------------------------------

#ifndef __BSP_SD_FATFS_H
#define __BSP_SD_FATFS_H

#include "gd32f4xx.h"
#include <stdio.h>
#include <string.h>
#include <stdint.h>

// SD卡操作类型定义
#define SD_OPERATE_OPEN_WRITE   0x01    // 打开写文件
#define SD_OPERATE_CLOSE_WRITE  0x02    // 关闭写文件
#define SD_OPERATE_OPEN_READ    0x03    // 打开读文件
#define SD_OPERATE_CLOSE_READ   0x04    // 关闭读文件
#define SD_OPERATE_DELETE       0x05    // 删除文件

// SD卡文件类型定义
#define SD_FILE_TYPE_COM3       0x01    // COM3数据文件
#define SD_FILE_TYPE_BB00       0x02    // BB00数据文件
#define SD_FILE_TYPE_ALL        0x03    // 所有文件

// SD卡状态定义
#define SD_STATUS_OK            0x00    // 操作成功
#define SD_STATUS_ERROR         0x01    // 操作失败
#define SD_STATUS_NO_CARD       0x02    // 无SD卡
#define SD_STATUS_NOT_READY     0x03    // SD卡未就绪

// 文件名长度定义
#define SD_FILENAME_MAX_LEN     64

// SD卡数据保存结构体
typedef struct {
    uint32_t NUM_CLK;               // 时钟计数
    uint32_t millisecondofweek;     // 周内毫秒
    // 可以根据需要添加更多字段
} SdDataSave_t;

// 全局变量声明
extern uint8_t SetSdOperateType;   // SD卡操作类型
extern uint8_t SetSdFlieType;      // SD卡文件类型
extern SdDataSave_t gSdDataSave;   // SD卡数据保存结构

// SD卡初始化和基本操作
int Fatfs_Init(void);
int sd_init(void);
int sd_deinit(void);
uint8_t sd_card_status_get(void);

// SD卡文件系统操作
void WriteFileOpenFromSd(uint8_t FlieType);
void ReadFileOpenFromSd(uint8_t FlieType);
void CloseFileToSd(uint8_t FlieType);
void WriteFileToSd(uint8_t FlieType);
void ReadFileToSd(uint8_t FlieType);
void DeleteFileFromSd(uint8_t FlieType);

// SD卡具体文件操作
void WriteCom3FileOpenFromSd(void);
void WriteBB00FileOpenFromSd(void);
void WriteCom3FileToSd(void);
void WriteBB00FileToSd(void);
void ReadCom3FileFromSd(void);
void ReadBB00FileFromSd(void);

// SD卡格式化和管理
void FormatSD(void);
void SdFileOperateTypeSet(uint8_t OperateType, uint8_t FlieType);

// SD卡读写操作接口
void SdFileWriteOperate(void);
void SdFileReadOperate(void);

// SD卡测试和调试
void SdFileTest(void);

// 错误处理
const char* show_error_string(int error_code);

// 队列处理（异步写入）
typedef struct {
    uint8_t pdrv;
    const uint8_t *buff;
    uint32_t sector;
    uint32_t count;
    uint8_t valid;
} ff_queue_item_t;

typedef struct {
    ff_queue_item_t items[16];  // 队列大小
    uint8_t head;
    uint8_t tail;
    uint8_t count;
} ff_queue_t;

extern ff_queue_t g_queue;

int ff_queue_init(ff_queue_t *queue);
int ff_queue_push(ff_queue_t *queue, uint8_t pdrv, const uint8_t *buff, uint32_t sector, uint32_t count);
int ff_queue_pop(ff_queue_t *queue, ff_queue_item_t *item);
void ff_handle_poll(void);

#endif // __BSP_SD_FATFS_H
