# Contributing to Eclipse ThreadX

Thanks for your interest in this project.

## Project description

Eclipse ThreadX provides a vendor-neutral, open source, safety certified OS for
real-time applications published on under a permissive license. The Eclipse
ThreadX suite encompasses:  
* ThreadX - advanced real-time operating system (RTOS) designed specifically for deeply embedded applications
* NetX Duo - advanced, industrial-grade TCP/IP network stack designed specifically for deeply embedded real-time and IoT applications
* FileX - high-performance, FAT-compatible file system that’s fully integrated with ThreadX kernel
* GUIX - provides a complete, embedded graphical user interface (GUI) library
* USBX - high-performance USB host, device, and on-the-go (OTG) embedded stack, that is fully integrated with ThreadX kernel
* LevelX - Flash Wear Leveling for FileX and stand-alone purposes
* GuiX Studio - design environment, facilitating the creation and maintenance of all graphical elements for GUIX
* TraceX - analysis tool that provides a graphical view of real-time system events to better understand the behavior of real-time systems

Project site: https://projects.eclipse.org/projects/iot.threadx

## Terms of Use

This repository is subject to the Terms of Use of the Eclipse Foundation  
https://www.eclipse.org/legal/termsofuse.php

## Developer resources

Information regarding source code management, builds, coding standards, and more.
https://projects.eclipse.org/projects/iot.threadx/developer

The project maintains the following source code repositories

* https://github.com/eclipse-threadx/.github
* https://github.com/eclipse-threadx/cmsis-packs
* https://github.com/eclipse-threadx/filex
* https://github.com/eclipse-threadx/getting-started
* https://github.com/eclipse-threadx/guix
* https://github.com/eclipse-threadx/levelx
* https://github.com/eclipse-threadx/netxduo
* https://github.com/eclipse-threadx/rtos-docs
* https://github.com/eclipse-threadx/samples
* https://github.com/eclipse-threadx/threadx
* https://github.com/eclipse-threadx/threadx-learn-samples
* https://github.com/eclipse-threadx/tracex
* https://github.com/eclipse-threadx/usbx

## Eclipse Development Process

This Eclipse Foundation open project is governed by the Eclipse Foundation
Development Process and operates under the terms of the Eclipse IP Policy.

* https://eclipse.org/projects/dev_process
* https://www.eclipse.org/org/documents/Eclipse_IP_Policy.pdf

## Eclipse Contributor Agreement

In order to be able to contribute to Eclipse Foundation projects you must electronically sign the Eclipse Contributor Agreement (ECA).  
https://www.eclipse.org/legal/ECA.php

The ECA provides the Eclipse Foundation with a permanent record that you agree
that each of your contributions will comply with the commitments documented in
the Developer Certificate of Origin (DCO). Having an ECA on file associated with
the email address matching the "Author" field of your contribution's Git commits
fulfills the DCO's requirement that you sign-off on your contributions.

For more information, please see the Eclipse Committer Handbook:  
https://www.eclipse.org/projects/handbook/#resources-commit

## Contact

Contact the project developers via the project's "dev" list.  
https://accounts.eclipse.org/mailing-list/threadx-dev
