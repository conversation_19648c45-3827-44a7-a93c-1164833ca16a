# Copyright (c) 2023 HPMicro
# SPDX-License-Identifier: BSD-3-Clause

set(FILEX_COMMON_FILES
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_attributes_read.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_attributes_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_create.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_default_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_default_get_copy.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_default_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_delete.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_entry_read.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_entry_write.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_first_entry_find.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_first_full_entry_find.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_free_search.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_information_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_local_path_clear.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_local_path_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_local_path_get_copy.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_local_path_restore.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_local_path_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_long_name_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_long_name_get_extended.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_name_extract.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_name_test.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_next_entry_find.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_next_full_entry_find.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_rename.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_search.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_short_name_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_directory_short_name_get_extended.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_fault_tolerant_add_FAT_log.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_fault_tolerant_add_bitmap_log.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_fault_tolerant_add_checksum_log.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_fault_tolerant_add_dir_log.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_fault_tolerant_apply_logs.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_fault_tolerant_calculate_checksum.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_fault_tolerant_cleanup_FAT_chain.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_fault_tolerant_create_log_file.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_fault_tolerant_enable.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_fault_tolerant_read_FAT.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_fault_tolerant_read_directory_sector.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_fault_tolerant_read_log_file.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_fault_tolerant_recover.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_fault_tolerant_reset_log_file.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_fault_tolerant_set_FAT_chain.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_fault_tolerant_transaction_end.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_fault_tolerant_transaction_fail.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_fault_tolerant_transaction_start.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_fault_tolerant_write_log_file.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_allocate.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_attributes_read.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_attributes_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_best_effort_allocate.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_close.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_create.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_date_time_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_delete.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_extended_allocate.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_extended_best_effort_allocate.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_extended_relative_seek.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_extended_seek.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_extended_truncate.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_extended_truncate_release.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_open.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_read.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_relative_seek.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_rename.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_seek.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_truncate.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_truncate_release.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_write.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_file_write_notify_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_abort.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_boot_info_extract.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_cache_invalidate.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_check.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_check_FAT_chain_check.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_check_lost_cluster_check.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_close.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_close_notify_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_extended_space_available.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_flush.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_format.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_format_oem_name_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_format_type_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_format_volume_id_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_open.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_open_notify_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_read.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_space_available.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_volume_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_volume_get_extended.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_volume_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_media_write.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_partition_offset_calculate.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_ram_driver.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_system_date_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_system_date_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_system_initialize.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_system_time_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_system_time_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_system_timer_entry.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_trace_event_insert.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_trace_event_update.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_trace_object_register.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_trace_object_unregister.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_unicode_directory_create.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_unicode_directory_entry_change.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_unicode_directory_entry_read.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_unicode_directory_rename.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_unicode_directory_search.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_unicode_file_create.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_unicode_file_rename.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_unicode_length_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_unicode_length_get_extended.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_unicode_name_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_unicode_name_get_extended.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_unicode_short_name_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_unicode_short_name_get_extended.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_utility_16_unsigned_read.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_utility_16_unsigned_write.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_utility_32_unsigned_read.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_utility_32_unsigned_write.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_utility_64_unsigned_read.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_utility_64_unsigned_write.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_utility_FAT_entry_read.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_utility_FAT_entry_write.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_utility_FAT_flush.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_utility_FAT_map_flush.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_utility_FAT_sector_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_utility_absolute_path_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_utility_logical_sector_cache_entry_read.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_utility_logical_sector_flush.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_utility_logical_sector_read.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_utility_logical_sector_write.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_utility_memory_copy.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_utility_memory_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_utility_string_length_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fx_utility_token_length_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_attributes_read.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_attributes_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_create.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_default_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_default_get_copy.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_default_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_delete.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_first_entry_find.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_first_full_entry_find.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_information_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_local_path_clear.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_local_path_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_local_path_get_copy.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_local_path_restore.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_local_path_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_long_name_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_long_name_get_extended.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_name_test.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_next_entry_find.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_next_full_entry_find.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_rename.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_short_name_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_directory_short_name_get_extended.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_fault_tolerant_enable.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_allocate.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_attributes_read.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_attributes_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_best_effort_allocate.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_close.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_create.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_date_time_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_delete.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_extended_allocate.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_extended_best_effort_allocate.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_extended_relative_seek.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_extended_seek.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_extended_truncate.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_extended_truncate_release.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_open.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_read.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_relative_seek.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_rename.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_seek.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_truncate.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_truncate_release.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_write.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_file_write_notify_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_media_abort.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_media_cache_invalidate.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_media_check.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_media_close.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_media_close_notify_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_media_extended_space_available.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_media_flush.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_media_format.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_media_open.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_media_open_notify_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_media_read.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_media_space_available.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_media_volume_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_media_volume_get_extended.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_media_volume_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_media_write.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_system_date_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_system_date_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_system_time_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_system_time_set.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_unicode_directory_create.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_unicode_directory_rename.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_unicode_file_create.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_unicode_file_rename.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_unicode_name_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_unicode_name_get_extended.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_unicode_short_name_get.c
	${CMAKE_CURRENT_LIST_DIR}/src/fxe_unicode_short_name_get_extended.c
)

sdk_src(${FILEX_COMMON_FILES})
sdk_inc(${CMAKE_CURRENT_LIST_DIR}/inc)
