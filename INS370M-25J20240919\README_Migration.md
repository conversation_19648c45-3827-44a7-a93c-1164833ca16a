# INS370M-25J20240919 项目功能迁移说明

## 概述

本项目已成功将HPM6750_INS-370M-SD-OK项目中的应用层逻辑代码迁移到INS370M-25J20240919项目中，实现了以下主要功能：

1. Flash读写操作
2. SD卡读写操作
3. 固件升级功能
4. 参数下发和配置
5. 坐标系调整
6. 上位机通信
7. 输入输出协议处理

## 迁移的文件结构

### 新增文件

#### BSP层
- `bsp/inc/bsp_sd_fatfs.h` - SD卡FatFS文件系统操作接口
- `bsp/src/bsp_sd_fatfs.c` - SD卡FatFS文件系统操作实现

#### Protocol层
- `Protocol/SetParaBao.h` - 参数配置和固件升级处理头文件
- `Protocol/SetParaBao.c` - 参数配置和固件升级处理实现

#### Source层
- `Source/inc/INS_App_Init.h` - 应用层初始化头文件
- `Source/src/INS_App_Init.c` - 应用层初始化实现

### 修改的文件

#### BSP层
- `bsp/inc/bsp_flash.h` - 扩展了Flash操作接口，兼容HPM6750项目
- `bsp/src/bsp_flash.c` - 实现了新的Flash操作函数

#### Protocol层（修改的文件）
- `Protocol/insTestingEntry.h` - 扩展了数据输出和协议处理接口
- `Protocol/InsTestingEntry.c` - 实现了增强版本的数据处理函数

#### Source层
- `Source/src/main.c` - 集成了新的应用层功能

## 主要功能说明

### 1. Flash读写功能

#### 新增接口
```c
int norflash_init(void);
int norflash_read(uint32_t offset, void *buf, uint32_t size_bytes);
int norflash_write(uint32_t offset, const void *buf, uint32_t size_bytes);
int norflash_erase_sector(uint32_t offset);
int norflash_erase_block(uint32_t offset);
int norflash_erase_chip(void);
```

#### 固件升级接口
```c
void Drv_FlashWrite(uint8_t *pucBuff, uint32_t uiAddress, uint32_t uiLen);
void Drv_FlashRead(uint8_t *pucBuff, uint32_t uiAddress, uint32_t uiLen);
void Drv_FlashErase(uint32_t uiAddress);
void Drv_SystemReset(void);
```

### 2. SD卡读写功能

#### 主要接口
```c
int Fatfs_Init(void);
void WriteFileOpenFromSd(uint8_t FlieType);
void ReadFileOpenFromSd(uint8_t FlieType);
void WriteFileToSd(uint8_t FlieType);
void ReadFileToSd(uint8_t FlieType);
void SdFileOperateTypeSet(uint8_t OperateType, uint8_t FlieType);
```

#### 支持的文件类型
- `SD_FILE_TYPE_COM3` - COM3数据文件
- `SD_FILE_TYPE_BB00` - BB00数据文件
- `SD_FILE_TYPE_ALL` - 所有文件

### 3. 参数配置功能

#### 参数结构体
```c
typedef struct {
    uint16_t Setfre;            // 设置频率
    uint32_t Setbaud;           // 设置波特率
    uint8_t SetDataOutType;     // 设置数据输出类型
} SetPara_t;
```

#### 参数管理接口
```c
void SetParaInit(void);
void SetParaDefault(void);
void SetParaSave(void);
void SetParaLoad(void);
void ProcessSetFrequency(uint16_t freq);
void ProcessSetBaudrate(uint32_t baudrate);
void ProcessSetDataOutType(uint8_t type);
```

### 4. 固件升级功能

#### 升级处理接口
```c
void ParaUpdateHandle(uint8_t *pucBuf, uint16_t usIndex, uint16_t usTotalBao, uint8_t ucLen);
void UpdateFirmwareStart(void);
void UpdateFirmwareStop(void);
uint8_t GetUpdateStatus(void);
```

### 5. 数据输出和协议处理

#### 数据输出格式
- `11BB格式` - FPGA原始数据输出
- `00BB格式` - 预处理数据输出
- SD卡数据回放

#### 主要接口
```c
// 增强版本的接口（直接扩展现有文件）
void INS912_Output_enhanced(navoutdata_t *pnavout);
void FPGATo422_11BB_send(void);
void FPGATo422_00BB_send_enhanced(navoutdata_t *pnavout);
void SDTo422_00BB_send(navoutdata_t *pnavout);

// 增强版本的算法和数据处理
void AlgorithmDo_enhanced(void);
void get_fpgadata_enhanced(void);
void analysisRxdata_enhanced(void);
void loopDoOther_enhanced(void);
```

## 使用方法

### 1. 系统初始化

在main函数中调用应用层初始化：

```c
int main(void)
{
    // 原有系统初始化
    SysInit();
    
    // 新增应用层初始化
    if (INS_App_Init() != 0) {
        // 初始化失败处理
        while(1);
    }
    
    SysInit_Over();
    
    while(1) {
        // 使用新的应用层主循环
        INS_App_MainLoop();
        
        // 其他处理...
    }
}
```

### 2. 参数配置

```c
// 设置输出频率为100Hz
ProcessSetFrequency(100);

// 设置波特率为460800
ProcessSetBaudrate(4608);  // 4608 * 100 = 460800

// 设置数据输出类型
ProcessSetDataOutType(1);  // 1: 11BB格式, 2: 00BB格式, 3: SD卡回放
```

### 3. SD卡操作

```c
// 打开写文件
SdFileOperateTypeSet(SD_OPERATE_OPEN_WRITE, SD_FILE_TYPE_BB00);

// 写入数据
SdFileWriteOperate();

// 关闭文件
SdFileOperateTypeSet(SD_OPERATE_CLOSE_WRITE, SD_FILE_TYPE_BB00);
```

### 4. 固件升级

```c
// 开始升级
UpdateFirmwareStart();

// 处理升级数据包
ParaUpdateHandle(data_buffer, package_index, total_packages, data_length);

// 检查升级状态
uint8_t status = GetUpdateStatus();
```

## 注意事项

1. **硬件适配**：代码已从HPM6750适配到GD32F4xx，但某些硬件相关的接口可能需要进一步调整。

2. **CH378文件系统**：SD卡操作使用了现有的CH378文件系统，如果需要使用标准FatFS，需要进一步修改。

3. **FPGA接口**：`get_fpgadata()`函数中的FPGA数据读取需要根据实际硬件接口实现。

4. **串口配置**：串口初始化和数据发送函数需要根据实际硬件配置调整。

5. **中断处理**：某些功能可能需要配置相应的中断处理程序。

## 编译说明

确保在编译时包含所有新增的源文件：

- `bsp/src/bsp_sd_fatfs.c`
- `Protocol/SetParaBao.c`
- `Protocol/InsTestingEntry_Compat.c`
- `Source/src/INS_App_Init.c`

并在包含路径中添加相应的头文件目录。

## 测试建议

1. 首先测试Flash读写功能
2. 测试SD卡文件系统操作
3. 测试参数配置和保存
4. 测试数据输出格式
5. 最后测试固件升级功能

## 技术支持

如有问题，请检查：
1. 硬件连接是否正确
2. 时钟配置是否匹配
3. 中断优先级设置
4. 内存分配是否足够
