<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\arm2.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\arm2.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Mon Jun 09 17:43:50 2025
<BR><P>
<H3>Maximum Stack Usage =       2416 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; AlgorithmDo_enhanced &rArr; AlgorithmDo &rArr; INS912AlgorithmEntry &rArr; AlgorithmAct &rArr; KalCompute &rArr; ComputeKk &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[7]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[7]">SVC_Handler</a><BR>
 <LI><a href="#[9]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[9]">PendSV_Handler</a><BR>
 <LI><a href="#[21]">CAN0_EWMC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[21]">CAN0_EWMC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1d]">ADC_IRQHandler</a> from gd32f4xx_it.o(i.ADC_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5]">BusFault_Handler</a> from gd32f4xx_it.o(i.BusFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[21]">CAN0_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1f]">CAN0_RX0_IRQHandler</a> from bsp_can.o(i.CAN0_RX0_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[20]">CAN0_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1e]">CAN0_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4d]">CAN1_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4b]">CAN1_RX0_IRQHandler</a> from bsp_can.o(i.CAN1_RX0_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4c]">CAN1_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4a]">CAN1_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[59]">DCI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[16]">DMA0_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[17]">DMA0_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[18]">DMA0_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[19]">DMA0_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1a]">DMA0_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1b]">DMA0_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1c]">DMA0_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3a]">DMA0_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[43]">DMA1_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[44]">DMA1_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[45]">DMA1_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[46]">DMA1_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[47]">DMA1_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4f]">DMA1_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[50]">DMA1_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[51]">DMA1_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[8]">DebugMon_Handler</a> from gd32f4xx_it.o(i.DebugMon_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[48]">ENET_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[49]">ENET_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3b]">EXMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[11]">EXTI0_IRQHandler</a> from drv_gpio.o(i.EXTI0_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[33]">EXTI10_15_IRQHandler</a> from gd32f4xx_it.o(i.EXTI10_15_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[12]">EXTI1_IRQHandler</a> from drv_gpio.o(i.EXTI1_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[13]">EXTI2_IRQHandler</a> from drv_gpio.o(i.EXTI2_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[14]">EXTI3_IRQHandler</a> from gd32f4xx_it.o(i.EXTI3_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[15]">EXTI4_IRQHandler</a> from drv_gpio.o(i.EXTI4_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[22]">EXTI5_9_IRQHandler</a> from gd32f4xx_it.o(i.EXTI5_9_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[f]">FMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5b]">FPU_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3]">HardFault_Handler</a> from gd32f4xx_it.o(i.HardFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2b]">I2C0_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2a]">I2C0_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2d]">I2C1_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2c]">I2C1_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[54]">I2C2_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[53]">I2C2_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[63]">IPA_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[c]">LVD_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4]">MemManage_Handler</a> from gd32f4xx_it.o(i.MemManage_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2]">NMI_Handler</a> from gd32f4xx_it.o(i.NMI_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[9]">PendSV_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[10]">RCU_CTC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[34]">RTC_Alarm_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[e]">RTC_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1]">Reset_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3c]">SDIO_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2e]">SPI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2f]">SPI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3e]">SPI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5e]">SPI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5f]">SPI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[60]">SPI5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[7]">SVC_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[a]">SysTick_Handler</a> from gd32f4xx_it.o(i.SysTick_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[65]">SystemInit</a> from system_gd32f4xx.o(i.SystemInit) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[d]">TAMPER_STAMP_IRQHandler</a> from bsp_rtc.o(i.TAMPER_STAMP_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[23]">TIMER0_BRK_TIMER8_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[26]">TIMER0_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[25]">TIMER0_TRG_CMT_TIMER10_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[24]">TIMER0_UP_TIMER9_IRQHandler</a> from gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[27]">TIMER1_IRQHandler</a> from gd32f4xx_it.o(i.TIMER1_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[28]">TIMER2_IRQHandler</a> from gd32f4xx_it.o(i.TIMER2_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[29]">TIMER3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3d]">TIMER4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[41]">TIMER5_DAC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[42]">TIMER6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[36]">TIMER7_BRK_TIMER11_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[39]">TIMER7_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[38]">TIMER7_TRG_CMT_TIMER13_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[37]">TIMER7_UP_TIMER12_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[62]">TLI_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[61]">TLI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5a]">TRNG_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3f]">UART3_IRQHandler</a> from gd32f4xx_it.o(i.UART3_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[40]">UART4_IRQHandler</a> from gd32f4xx_it.o(i.UART4_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5c]">UART6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5d]">UART7_IRQHandler</a> from adxl355.o(i.UART7_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[30]">USART0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[31]">USART1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[32]">USART2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[52]">USART5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4e]">USBFS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[35]">USBFS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[56]">USBHS_EP1_In_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[55]">USBHS_EP1_Out_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[58]">USBHS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[57]">USBHS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6]">UsageFault_Handler</a> from gd32f4xx_it.o(i.UsageFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[b]">WWDGT_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[66]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[68]">_sbackspace</a> from _sgetc.o(.text) referenced from strtod.o(.text)
 <LI><a href="#[67]">_sgetc</a> from _sgetc.o(.text) referenced from strtod.o(.text)
 <LI><a href="#[6b]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[6e]">bmp2_delay_us</a> from common.o(i.bmp2_delay_us) referenced from common.o(i.bmp2_interface_selection)
 <LI><a href="#[6c]">bmp2_i2c_read</a> from common.o(i.bmp2_i2c_read) referenced from common.o(i.bmp2_interface_selection)
 <LI><a href="#[6d]">bmp2_i2c_write</a> from common.o(i.bmp2_i2c_write) referenced from common.o(i.bmp2_interface_selection)
 <LI><a href="#[6a]">fputc</a> from ins_init.o(i.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[69]">isspace</a> from isspace_o.o(.text) referenced from strtod.o(.text)
 <LI><a href="#[64]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[66]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[1f5]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[6f]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[8f]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[1f6]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[1f7]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[1f8]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[1f9]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))

<P><STRONG><a name="[1fa]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[1fb]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[1]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>CAN0_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_EWMC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_EWMC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN0_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>CAN0_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>CAN1_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>DCI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA0_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA0_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA0_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA0_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA0_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA0_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA0_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>DMA0_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>DMA1_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>ENET_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>ENET_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>EXMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C0_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>I2C0_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>IPA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>LVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>RCU_CTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TIMER0_BRK_TIMER8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIMER0_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIMER0_TRG_CMT_TIMER10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIMER3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>TIMER4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIMER5_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>TIMER6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIMER7_BRK_TIMER11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIMER7_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIMER7_TRG_CMT_TIMER13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIMER7_UP_TIMER12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>TLI_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>TLI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>TRNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>UART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>USART0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>USART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>USBFS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>USBFS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>USBHS_EP1_In_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>USBHS_EP1_Out_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>USBHS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>USBHS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>WWDGT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[101]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcusendtopcdriversdata
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata_do
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912AlgorithmEntry
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_11BB_send
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_00BB_send
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysisRxdata
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_read_ver_rsp
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_cali_ehco_rsp
</UL>

<P><STRONG><a name="[ac]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeSync
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_info_get
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
<LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_RX0_IRQHandler
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_00BB_send
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_resume_defaultPara
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_read_ver_rsp
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_read_dev_type_rsp
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_para_ehco_rsp
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_fm_update
</UL>

<P><STRONG><a name="[1fc]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[72]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[1fd]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[1fe]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[71]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0_UP_TIMER9_IRQHandler
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initializationdriversettings
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_show_timestamp
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVFileHead
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART7_IRQHandler
</UL>

<P><STRONG><a name="[db]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysInit
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generateCSVLogFileName
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_calib_param
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlign_Init
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlign_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_Init
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Navi_Init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDelSenbb
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_OutDataSet
</UL>

<P><STRONG><a name="[1ff]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[73]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[130]"></a>strstr</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, strstr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParseStrCmd
</UL>

<P><STRONG><a name="[13b]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_syn_count_do
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg_canout
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendVersionInfo
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVFileHead
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_read_ver_rsp
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_read_dev_type_rsp
</UL>

<P><STRONG><a name="[1c8]"></a>memcmp</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frameParse
</UL>

<P><STRONG><a name="[13a]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generateCSVLogFileName
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initializationdriversettings
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_syn_count_do
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendVersionInfo
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVFileHead
</UL>

<P><STRONG><a name="[1a8]"></a>strncmp</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, strncmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strncmp
</UL>
<BR>[Called By]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_test_switch
</UL>

<P><STRONG><a name="[8b]"></a>_scanf_real</STRONG> (Thumb, 0 bytes, Stack size 104 bytes, scanf_fp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = _scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_local_sscanf
</UL>

<P><STRONG><a name="[78]"></a>_scanf_really_real</STRONG> (Thumb, 556 bytes, Stack size 104 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[7b]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timediff
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeadd
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2epoch
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSib0
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSi
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrStore_1s
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectAtti
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeZk
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeXkk_1
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeXk
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePkk_1_Step2
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePk
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeKk
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFn
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFk
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVib0
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVi
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qua_Mul
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QToCnb
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWnbb
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVn
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVibn
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeRmRn
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePos
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeG
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDelSenbb
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeAttiRate
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AttiToCnb
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_gyro
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_acc
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSS_Last_TIME
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[80]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timediff
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeadd
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSi
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrStore_1s
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectVn
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeZk
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeXk
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFn
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vec_Cross
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qua_Mul
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrCorrect_1_Navi_Time
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TransHeading0to360
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QToCnb
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWnbb
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVn
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVibn
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePos
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeG
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeAttiRate
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AttiToCnb
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_gyro
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_acc
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[81]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gst2time
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Inv
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrCorrect_1_Navi_Time
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeG
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeAttiRate
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[76]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSib0
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSi
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrStore_1s
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectAtti
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeXkk_1
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePk
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFn
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFk
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Inv
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVib0
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVi
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCie
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCen
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vec_Cross
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qua_Mul
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrCorrect_1_Navi_Time
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QToCnb
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWien
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWenn
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVn
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVibn
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeRmRn
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePos
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeG
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDelSenbb
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDeg_Ex
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeAttiRate
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AttiToCnb
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_preAlgParm_370
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_gyro
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_acc
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_OutDataSet
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_00BB_send
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[77]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART7_IRQHandler
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSi
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectAtti
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFn
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFk
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Inv
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrCorrect_1_Navi_Time
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeRmRn
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePos
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeAttiRate
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_preAlgParm_370
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_00BB_send
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[82]"></a>__aeabi_i2d</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, dflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_i2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2epoch
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gst2time
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART7_IRQHandler
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_preAlgParm_370
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_gyro
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_OutDataSet
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_00BB_send
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[83]"></a>__aeabi_ui2d</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, dfltui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_ui2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timediff
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFk
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[84]"></a>__aeabi_d2iz</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, dfixi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_d2iz
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeadd
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gst2time
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[1b2]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_preAlgParm_370
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_acc
</UL>

<P><STRONG><a name="[200]"></a>__aeabi_cdcmpeq</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, cdcmple.o(.text), UNUSED)

<P><STRONG><a name="[cc]"></a>__aeabi_cdcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gst2time
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrStore_1s
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeZk
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePos
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[95]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2utc
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSSAndHeadDataTest
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrStore_1s
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectAtti
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeZk
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Inv
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TransHeading0to360
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVn
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePos
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gnss_check_bind
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSS_Lost_Time
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[7a]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pnavout_set
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_preAlgParm_370
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_OutDataSet
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_00BB_send
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[201]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[173]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[87]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[7c]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[202]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[85]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[203]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[7d]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[204]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[88]"></a>localtime</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, localtime_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = localtime &rArr; _localtime
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_localtime
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeSync
</UL>

<P><STRONG><a name="[89]"></a>_localtime</STRONG> (Thumb, 134 bytes, Stack size 8 bytes, localtime_i.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _localtime
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;localtime
</UL>

<P><STRONG><a name="[67]"></a>_sgetc</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[68]"></a>_sbackspace</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[8c]"></a>__strtod_int</STRONG> (Thumb, 90 bytes, Stack size 40 bytes, strtod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_local_sscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_strtod
</UL>

<P><STRONG><a name="[205]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[86]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[206]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[7f]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[7e]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
</UL>

<P><STRONG><a name="[16d]"></a>__ARM_scalbn</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, dscalb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[207]"></a>scalbn</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, dscalb.o(.text), UNUSED)

<P><STRONG><a name="[8d]"></a>_dsqrt</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, dsqrt.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>

<P><STRONG><a name="[75]"></a>__aeabi_ul2d</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, dfltul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[8e]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[70]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[208]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[90]"></a>__rt_ctype_table</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, ctype_o.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;isspace
</UL>

<P><STRONG><a name="[69]"></a>isspace</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, isspace_o.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = isspace
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Address Reference Count : 1]<UL><LI> strtod.o(.text)
</UL>
<P><STRONG><a name="[209]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[20a]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[91]"></a>ACC_gyroreset_r_TAFEAG16_buf</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, instestingentry.o(i.ACC_gyroreset_r_TAFEAG16_buf))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = ACC_gyroreset_r_TAFEAG16_buf &rArr; FPGATo422_00BB_send &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_00BB_send
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo
</UL>

<P><STRONG><a name="[1d]"></a>ADC_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.ADC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_interrupt_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[94]"></a>AlgorithmAct</STRONG> (Thumb, 1238 bytes, Stack size 48 bytes, instestingentry.o(i.AlgorithmAct))
<BR><BR>[Stack]<UL><LI>Max Depth = 2384<LI>Call Chain = AlgorithmAct &rArr; KalCompute &rArr; ComputeKk &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishDynamicInertialSysAlign
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlign_Init
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalPredict
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalCompute
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrStore_1s
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFn
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlign_Init
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishInertialSysAlign
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Navi_Init
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicNavi_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912AlgorithmEntry
</UL>

<P><STRONG><a name="[a3]"></a>AlgorithmDo</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, instestingentry.o(i.AlgorithmDo))
<BR><BR>[Stack]<UL><LI>Max Depth = 2408<LI>Call Chain = AlgorithmDo &rArr; INS912AlgorithmEntry &rArr; AlgorithmAct &rArr; KalCompute &rArr; ComputeKk &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Algorithm_before_otherDataDo
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912AlgorithmEntry
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmDo_enhanced
</UL>

<P><STRONG><a name="[a6]"></a>AlgorithmDo_enhanced</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, instestingentry.o(i.AlgorithmDo_enhanced))
<BR><BR>[Stack]<UL><LI>Max Depth = 2416<LI>Call Chain = AlgorithmDo_enhanced &rArr; AlgorithmDo &rArr; INS912AlgorithmEntry &rArr; AlgorithmAct &rArr; KalCompute &rArr; ComputeKk &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmDo
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a4]"></a>Algorithm_before_otherDataDo</STRONG> (Thumb, 16 bytes, Stack size 24 bytes, datado.o(i.Algorithm_before_otherDataDo))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Algorithm_before_otherDataDo
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;caninfupdate
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmDo
</UL>

<P><STRONG><a name="[a8]"></a>AttiToCnb</STRONG> (Thumb, 500 bytes, Stack size 88 bytes, navi.o(i.AttiToCnb))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = AttiToCnb &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishDynamicInertialSysAlign
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishInertialSysAlign
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicNavi_Init
</UL>

<P><STRONG><a name="[1d8]"></a>BindDefaultSet_by_GNSS</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, navi.o(i.BindDefaultSet_by_GNSS))
<BR><BR>[Called By]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gnss_check_bind
</UL>

<P><STRONG><a name="[5]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>CAN0_RX0_IRQHandler</STRONG> (Thumb, 884 bytes, Stack size 8 bytes, bsp_can.o(i.CAN0_RX0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = CAN0_RX0_IRQHandler &rArr; can_message_receive
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_message_receive
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, bsp_can.o(i.CAN1_RX0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = CAN1_RX0_IRQHandler &rArr; can_message_receive
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_message_receive
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[af]"></a>CH378ByteLocate</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, file_sys.o(i.CH378ByteLocate))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = CH378ByteLocate &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[b3]"></a>CH378ByteWrite</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, file_sys.o(i.CH378ByteWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = CH378ByteWrite &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteOfsBlock
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVFileHead
</UL>

<P><STRONG><a name="[b5]"></a>CH378FileCreate</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, file_sys.o(i.CH378FileCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = CH378FileCreate &rArr; CH378SendCmdWaitInt &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[b8]"></a>CH378FileOpen</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, file_sys.o(i.CH378FileOpen))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = CH378FileOpen &rArr; CH378SendCmdWaitInt &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[b9]"></a>CH378GetDiskStatus</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, file_sys.o(i.CH378GetDiskStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = CH378GetDiskStatus &rArr; CH378ReadVar8 &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ReadVar8
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[bb]"></a>CH378GetICVer</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, file_sys.o(i.CH378GetICVer))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = CH378GetICVer &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xReadCH378Data
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mInitCH378Host
</UL>

<P><STRONG><a name="[bd]"></a>CH378GetIntStatus</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, file_sys.o(i.CH378GetIntStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xReadCH378Data
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
</UL>

<P><STRONG><a name="[ba]"></a>CH378ReadVar8</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, file_sys.o(i.CH378ReadVar8))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = CH378ReadVar8 &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xReadCH378Data
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetDiskStatus
</UL>

<P><STRONG><a name="[b7]"></a>CH378SendCmdWaitInt</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, file_sys.o(i.CH378SendCmdWaitInt))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = CH378SendCmdWaitInt &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378FileOpen
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378FileCreate
</UL>

<P><STRONG><a name="[b6]"></a>CH378SetFileName</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, file_sys.o(i.CH378SetFileName))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = CH378SetFileName &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378FileOpen
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378FileCreate
</UL>

<P><STRONG><a name="[b4]"></a>CH378WriteOfsBlock</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, file_sys.o(i.CH378WriteOfsBlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = CH378WriteOfsBlock &rArr; CH378_mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_mDelayuS
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
</UL>

<P><STRONG><a name="[bf]"></a>CH378_Port_Init</STRONG> (Thumb, 230 bytes, Stack size 32 bytes, ch378_spi_hw.o(i.CH378_Port_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = CH378_Port_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_crc_off
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mInitCH378Host
</UL>

<P><STRONG><a name="[c7]"></a>CH378_mDelaymS</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, ch378_hal.o(i.CH378_mDelaymS))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CH378_mDelaymS
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[be]"></a>CH378_mDelayuS</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, ch378_hal.o(i.CH378_mDelayuS))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = CH378_mDelayuS &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteOfsBlock
</UL>

<P><STRONG><a name="[ca]"></a>CnbToAtti</STRONG> (Thumb, 616 bytes, Stack size 32 bytes, navi.o(i.CnbToAtti))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = CnbToAtti &rArr; __hardfp_asin &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishDynamicInertialSysAlign
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishInertialSysAlign
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Navi_Init
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicNavi_Init
</UL>

<P><STRONG><a name="[cf]"></a>CnbToQ</STRONG> (Thumb, 1072 bytes, Stack size 48 bytes, navi.o(i.CnbToQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = CnbToQ &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishDynamicInertialSysAlign
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishInertialSysAlign
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicNavi_Init
</UL>

<P><STRONG><a name="[d1]"></a>ComputeAttiRate</STRONG> (Thumb, 480 bytes, Stack size 112 bytes, navi.o(i.ComputeAttiRate))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = ComputeAttiRate &rArr; __hardfp_tan &rArr; __kernel_tan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
</UL>

<P><STRONG><a name="[d3]"></a>ComputeCen</STRONG> (Thumb, 246 bytes, Stack size 64 bytes, align.o(i.ComputeCen))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = ComputeCen &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlign_Init
</UL>

<P><STRONG><a name="[d4]"></a>ComputeCib0i</STRONG> (Thumb, 236 bytes, Stack size 344 bytes, align.o(i.ComputeCib0i))
<BR><BR>[Stack]<UL><LI>Max Depth = 888<LI>Call Chain = ComputeCib0i &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Inv
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vec_Cross
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishDynamicInertialSysAlign
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishInertialSysAlign
</UL>

<P><STRONG><a name="[d8]"></a>ComputeCie</STRONG> (Thumb, 230 bytes, Stack size 32 bytes, align.o(i.ComputeCie))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = ComputeCie &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
</UL>

<P><STRONG><a name="[d9]"></a>ComputeDeg_Ex</STRONG> (Thumb, 182 bytes, Stack size 8 bytes, navi.o(i.ComputeDeg_Ex))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = ComputeDeg_Ex &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Navi_Init
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicNavi_Init
</UL>

<P><STRONG><a name="[da]"></a>ComputeDelSenbb</STRONG> (Thumb, 238 bytes, Stack size 56 bytes, navi.o(i.ComputeDelSenbb))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = ComputeDelSenbb &rArr; Vec_Cross &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vec_Cross
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
</UL>

<P><STRONG><a name="[dc]"></a>ComputeFk</STRONG> (Thumb, 528 bytes, Stack size 88 bytes, kalman.o(i.ComputeFk))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = ComputeFk &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Virtual_PPS_insert_5hz
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSS_Valid_PPSStart
</UL>

<P><STRONG><a name="[9f]"></a>ComputeFn</STRONG> (Thumb, 5182 bytes, Stack size 104 bytes, kalman.o(i.ComputeFn))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = ComputeFn &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[dd]"></a>ComputeG</STRONG> (Thumb, 346 bytes, Stack size 96 bytes, navi.o(i.ComputeG))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = ComputeG &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
</UL>

<P><STRONG><a name="[de]"></a>ComputeKk</STRONG> (Thumb, 234 bytes, Stack size 1776 bytes, kalman.o(i.ComputeKk))
<BR><BR>[Stack]<UL><LI>Max Depth = 2320<LI>Call Chain = ComputeKk &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Inv
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalCompute
</UL>

<P><STRONG><a name="[e0]"></a>ComputeLeverArmSn</STRONG> (Thumb, 46 bytes, Stack size 96 bytes, navi.o(i.ComputeLeverArmSn))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = ComputeLeverArmSn &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
</UL>

<P><STRONG><a name="[e1]"></a>ComputeLeverArmVn</STRONG> (Thumb, 58 bytes, Stack size 128 bytes, navi.o(i.ComputeLeverArmVn))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = ComputeLeverArmVn &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vec_Cross
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
</UL>

<P><STRONG><a name="[e2]"></a>ComputePk</STRONG> (Thumb, 206 bytes, Stack size 1192 bytes, kalman.o(i.ComputePk))
<BR><BR>[Stack]<UL><LI>Max Depth = 1344<LI>Call Chain = ComputePk &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalCompute
</UL>

<P><STRONG><a name="[e3]"></a>ComputePkk_1_Step1</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, kalman.o(i.ComputePkk_1_Step1))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = ComputePkk_1_Step1 &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalCompute
</UL>

<P><STRONG><a name="[e4]"></a>ComputePkk_1_Step2</STRONG> (Thumb, 150 bytes, Stack size 24 bytes, kalman.o(i.ComputePkk_1_Step2))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = ComputePkk_1_Step2 &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalCompute
</UL>

<P><STRONG><a name="[e5]"></a>ComputePos</STRONG> (Thumb, 498 bytes, Stack size 96 bytes, navi.o(i.ComputePos))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = ComputePos &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
</UL>

<P><STRONG><a name="[e6]"></a>ComputeQ</STRONG> (Thumb, 838 bytes, Stack size 176 bytes, navi.o(i.ComputeQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = ComputeQ &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qua_Mul
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
</UL>

<P><STRONG><a name="[e8]"></a>ComputeRmRn</STRONG> (Thumb, 236 bytes, Stack size 72 bytes, navi.o(i.ComputeRmRn))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = ComputeRmRn &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
</UL>

<P><STRONG><a name="[e9]"></a>ComputeSi</STRONG> (Thumb, 566 bytes, Stack size 320 bytes, dynamic_align.o(i.ComputeSi))
<BR><BR>[Stack]<UL><LI>Max Depth = 472<LI>Call Chain = ComputeSi &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vec_Cross
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
</UL>

<P><STRONG><a name="[ea]"></a>ComputeSib0</STRONG> (Thumb, 214 bytes, Stack size 56 bytes, dynamic_align.o(i.ComputeSib0))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = ComputeSib0 &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
</UL>

<P><STRONG><a name="[eb]"></a>ComputeVi</STRONG> (Thumb, 196 bytes, Stack size 264 bytes, align.o(i.ComputeVi))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = ComputeVi &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
</UL>

<P><STRONG><a name="[ec]"></a>ComputeVib0</STRONG> (Thumb, 124 bytes, Stack size 64 bytes, align.o(i.ComputeVib0))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = ComputeVib0 &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
</UL>

<P><STRONG><a name="[ed]"></a>ComputeVibn</STRONG> (Thumb, 644 bytes, Stack size 248 bytes, navi.o(i.ComputeVibn))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = ComputeVibn &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Vec_Cross
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
</UL>

<P><STRONG><a name="[ee]"></a>ComputeVn</STRONG> (Thumb, 746 bytes, Stack size 96 bytes, navi.o(i.ComputeVn))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = ComputeVn &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
</UL>

<P><STRONG><a name="[ef]"></a>ComputeWenn</STRONG> (Thumb, 152 bytes, Stack size 48 bytes, navi.o(i.ComputeWenn))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = ComputeWenn &rArr; __hardfp_tan &rArr; __kernel_tan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
</UL>

<P><STRONG><a name="[f0]"></a>ComputeWien</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, navi.o(i.ComputeWien))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = ComputeWien &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
</UL>

<P><STRONG><a name="[f1]"></a>ComputeWnbb</STRONG> (Thumb, 198 bytes, Stack size 88 bytes, navi.o(i.ComputeWnbb))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = ComputeWnbb &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
</UL>

<P><STRONG><a name="[f2]"></a>ComputeXk</STRONG> (Thumb, 198 bytes, Stack size 248 bytes, kalman.o(i.ComputeXk))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = ComputeXk &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalCompute
</UL>

<P><STRONG><a name="[f3]"></a>ComputeXkk_1</STRONG> (Thumb, 146 bytes, Stack size 32 bytes, kalman.o(i.ComputeXkk_1))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = ComputeXkk_1 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalPredict
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Virtual_PPS_insert_5hz
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSS_Valid_PPSStart
</UL>

<P><STRONG><a name="[f4]"></a>ComputeZk</STRONG> (Thumb, 2426 bytes, Stack size 16 bytes, kalman.o(i.ComputeZk))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = ComputeZk &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalCompute
</UL>

<P><STRONG><a name="[f5]"></a>CorrectAtti</STRONG> (Thumb, 840 bytes, Stack size 176 bytes, kalman.o(i.CorrectAtti))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = CorrectAtti &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qua_Mul
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrCorrect_1_Navi_Time
</UL>

<P><STRONG><a name="[f6]"></a>CorrectVn</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, kalman.o(i.CorrectVn))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = CorrectVn &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrCorrect_1_Navi_Time
</UL>

<P><STRONG><a name="[fd]"></a>DRam_Read</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, bsp_fmc.o(i.DRam_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DRam_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
</UL>

<P><STRONG><a name="[8]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[9b]"></a>DynamicInertialSysAlignCompute</STRONG> (Thumb, 576 bytes, Stack size 48 bytes, dynamic_align.o(i.DynamicInertialSysAlignCompute))
<BR><BR>[Stack]<UL><LI>Max Depth = 520<LI>Call Chain = DynamicInertialSysAlignCompute &rArr; ComputeSi &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UpdateAlignPosAndVn
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSib0
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSi
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCie
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCen
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QToCnb
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWien
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWenn
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeRmRn
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeG
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDelSenbb
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[97]"></a>DynamicInertialSysAlign_Init</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, dynamic_align.o(i.DynamicInertialSysAlign_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DynamicInertialSysAlign_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[9d]"></a>DynamicNavi_Init</STRONG> (Thumb, 202 bytes, Stack size 24 bytes, navi.o(i.DynamicNavi_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = DynamicNavi_Init &rArr; AttiToCnb &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TransHeading0to360
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDeg_Ex
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AttiToCnb
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[11]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, drv_gpio.o(i.EXTI0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = EXTI0_IRQHandler &rArr; GD32_GPIO_EXTI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD32_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>EXTI10_15_IRQHandler</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.EXTI10_15_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI10_15_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_get
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, drv_gpio.o(i.EXTI1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = EXTI1_IRQHandler &rArr; GD32_GPIO_EXTI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD32_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, drv_gpio.o(i.EXTI2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = EXTI2_IRQHandler &rArr; GD32_GPIO_EXTI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD32_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.EXTI3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI3_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_get
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, drv_gpio.o(i.EXTI4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = EXTI4_IRQHandler &rArr; GD32_GPIO_EXTI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD32_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>EXTI5_9_IRQHandler</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.EXTI5_9_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 992<LI>Call Chain = EXTI5_9_IRQHandler &rArr; synthesisLogBuf &rArr; time2str &rArr; __hardfp_pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_get
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRam_Read
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[100]"></a>ErrCorrect_1_Navi_Time</STRONG> (Thumb, 1852 bytes, Stack size 120 bytes, kalman.o(i.ErrCorrect_1_Navi_Time))
<BR><BR>[Stack]<UL><LI>Max Depth = 536<LI>Call Chain = ErrCorrect_1_Navi_Time &rArr; CorrectAtti &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectVn
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectAtti
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
</UL>

<P><STRONG><a name="[a2]"></a>ErrStore_1s</STRONG> (Thumb, 2562 bytes, Stack size 56 bytes, kalman.o(i.ErrStore_1s))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = ErrStore_1s &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[92]"></a>FPGATo422_00BB_send</STRONG> (Thumb, 462 bytes, Stack size 32 bytes, instestingentry.o(i.FPGATo422_00BB_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = FPGATo422_00BB_send &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_accum_verify_8bit
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_00BB_send_enhanced
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ACC_gyroreset_r_TAFEAG16_buf
</UL>

<P><STRONG><a name="[104]"></a>FPGATo422_00BB_send_enhanced</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, instestingentry.o(i.FPGATo422_00BB_send_enhanced))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = FPGATo422_00BB_send_enhanced &rArr; FPGATo422_00BB_send &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_00BB_send
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912_Output_enhanced
</UL>

<P><STRONG><a name="[105]"></a>FPGATo422_11BB_send</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, instestingentry.o(i.FPGATo422_11BB_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = FPGATo422_11BB_send &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_accum_verify_8bit
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_11BB_send_enhanced
</UL>

<P><STRONG><a name="[106]"></a>FPGATo422_11BB_send_enhanced</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, instestingentry.o(i.FPGATo422_11BB_send_enhanced))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = FPGATo422_11BB_send_enhanced &rArr; FPGATo422_11BB_send &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_11BB_send
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912_Output_enhanced
</UL>

<P><STRONG><a name="[9c]"></a>FinishDynamicInertialSysAlign</STRONG> (Thumb, 152 bytes, Stack size 232 bytes, dynamic_align.o(i.FinishDynamicInertialSysAlign))
<BR><BR>[Stack]<UL><LI>Max Depth = 1120<LI>Call Chain = FinishDynamicInertialSysAlign &rArr; ComputeCib0i &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCib0i
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AttiToCnb
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[99]"></a>FinishInertialSysAlign</STRONG> (Thumb, 152 bytes, Stack size 232 bytes, align.o(i.FinishInertialSysAlign))
<BR><BR>[Stack]<UL><LI>Max Depth = 1120<LI>Call Chain = FinishInertialSysAlign &rArr; ComputeCib0i &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCib0i
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Mul
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AttiToCnb
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[fa]"></a>GD32_GPIO_EXTI_IRQHandler</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, drv_gpio.o(i.GD32_GPIO_EXTI_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = GD32_GPIO_EXTI_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_get
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI4_IRQHandler
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI2_IRQHandler
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI1_IRQHandler
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI0_IRQHandler
</UL>

<P><STRONG><a name="[107]"></a>GNSSAndHeadDataTest</STRONG> (Thumb, 316 bytes, Stack size 24 bytes, read_and_check_gnss_data.o(i.GNSSAndHeadDataTest))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = GNSSAndHeadDataTest &rArr; __hardfp_fabs
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_And_Check_GNSS_Data
</UL>

<P><STRONG><a name="[108]"></a>GNSS_Last_TIME</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, instestingentry.o(i.GNSS_Last_TIME))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = GNSS_Last_TIME &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo
</UL>

<P><STRONG><a name="[109]"></a>GNSS_Lost_Time</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, instestingentry.o(i.GNSS_Lost_Time))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = GNSS_Lost_Time
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo
</UL>

<P><STRONG><a name="[10a]"></a>GNSS_Valid_PPSStart</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, instestingentry.o(i.GNSS_Valid_PPSStart))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = GNSS_Valid_PPSStart &rArr; ComputeFk &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveINSData
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeXkk_1
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFk
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo
</UL>

<P><STRONG><a name="[3]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[129]"></a>Hk_Init</STRONG> (Thumb, 280 bytes, Stack size 0 bytes, kalman.o(i.Hk_Init))
<BR><BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalCompute
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_Init
</UL>

<P><STRONG><a name="[a5]"></a>INS912AlgorithmEntry</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, instestingentry.o(i.INS912AlgorithmEntry))
<BR><BR>[Stack]<UL><LI>Max Depth = 2400<LI>Call Chain = INS912AlgorithmEntry &rArr; AlgorithmAct &rArr; KalCompute &rArr; ComputeKk &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pnavout_set
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmDo
</UL>

<P><STRONG><a name="[10f]"></a>INS912_Output</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ins_output.o(i.INS912_Output))
<BR><BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912_Output_enhanced
</UL>

<P><STRONG><a name="[10e]"></a>INS912_Output_enhanced</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, instestingentry.o(i.INS912_Output_enhanced))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = INS912_Output_enhanced &rArr; FPGATo422_00BB_send_enhanced &rArr; FPGATo422_00BB_send &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912_Output
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_11BB_send_enhanced
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_00BB_send_enhanced
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[110]"></a>INS_Init</STRONG> (Thumb, 366 bytes, Stack size 16 bytes, ins_init.o(i.INS_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = INS_Init &rArr; comm_store_init &rArr; comm_resume_defaultPara &rArr; comm_set_customPara &rArr; fmc_write_8bit_data &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitFlashAddr
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_enable
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_all_reset_flag_clear
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_vector_table_set
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mInitCH378Host
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mDelaymS
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_send_end_frame
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gpio_init
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_init
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_can_init
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp280_init
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_TxInit
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_RxInit
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_configuration
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;initializationdriversettings
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysInit_Over
</UL>

<P><STRONG><a name="[98]"></a>InertialSysAlignCompute</STRONG> (Thumb, 450 bytes, Stack size 40 bytes, align.o(i.InertialSysAlignCompute))
<BR><BR>[Stack]<UL><LI>Max Depth = 456<LI>Call Chain = InertialSysAlignCompute &rArr; ComputeVi &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVib0
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVi
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCie
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Tr
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QToCnb
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeG
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDelSenbb
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[96]"></a>InertialSysAlign_Init</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, align.o(i.InertialSysAlign_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = InertialSysAlign_Init &rArr; ComputeCen &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCen
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[118]"></a>InitFlashAddr</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, bsp_flash.o(i.InitFlashAddr))
<BR><BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[a0]"></a>KalCompute</STRONG> (Thumb, 194 bytes, Stack size 16 bytes, kalman.o(i.KalCompute))
<BR><BR>[Stack]<UL><LI>Max Depth = 2336<LI>Call Chain = KalCompute &rArr; ComputeKk &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rk_Init
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Hk_Init
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeZk
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeXk
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePkk_1_Step2
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePkk_1_Step1
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePk
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeKk
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[a1]"></a>KalPredict</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, kalman.o(i.KalPredict))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = KalPredict &rArr; ComputeXkk_1 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeXkk_1
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[12a]"></a>Kalman_Init</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, kalman.o(i.Kalman_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Kalman_Init &rArr; Rk_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rk_Init
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Qk_Init
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Pk_Init
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Hk_Init
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysInit
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
</UL>

<P><STRONG><a name="[12d]"></a>Kalman_StartUp</STRONG> (Thumb, 1168 bytes, Stack size 40 bytes, kalman.o(i.Kalman_StartUp))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = Kalman_StartUp &rArr; AttiToCnb &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rk_Init
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Hk_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_Init
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AttiToCnb
</UL>
<BR>[Called By]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gnss_check_bind
</UL>

<P><STRONG><a name="[12e]"></a>LEDIndicator</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, ins_init.o(i.LEDIndicator))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LEDIndicator
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;loopDoOther
</UL>

<P><STRONG><a name="[d6]"></a>Mat_Inv</STRONG> (Thumb, 712 bytes, Stack size 456 bytes, matvecmath.o(i.Mat_Inv))
<BR><BR>[Stack]<UL><LI>Max Depth = 544<LI>Call Chain = Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeKk
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCib0i
</UL>

<P><STRONG><a name="[d7]"></a>Mat_Mul</STRONG> (Thumb, 176 bytes, Stack size 64 bytes, matvecmath.o(i.Mat_Mul))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishDynamicInertialSysAlign
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSib0
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSi
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeXk
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePkk_1_Step2
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePkk_1_Step1
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePk
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeKk
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishInertialSysAlign
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVib0
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVi
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCib0i
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrCorrect_1_Navi_Time
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWnbb
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVibn
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeLeverArmVn
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeLeverArmSn
</UL>

<P><STRONG><a name="[df]"></a>Mat_Tr</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, matvecmath.o(i.Mat_Tr))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Mat_Tr
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishDynamicInertialSysAlign
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSi
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePkk_1_Step1
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeKk
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FinishInertialSysAlign
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVi
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVibn
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeLeverArmVn
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeLeverArmSn
</UL>

<P><STRONG><a name="[4]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[9e]"></a>NaviCompute</STRONG> (Thumb, 682 bytes, Stack size 48 bytes, navi.o(i.NaviCompute))
<BR><BR>[Stack]<UL><LI>Max Depth = 584<LI>Call Chain = NaviCompute &rArr; ErrCorrect_1_Navi_Time &rArr; CorrectAtti &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrCorrect_1_Navi_Time
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TransHeading0to360
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QToCnb
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWnbb
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWien
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWenn
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVn
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVibn
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeRmRn
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePos
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeG
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDelSenbb
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDeg_Ex
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeAttiRate
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[9a]"></a>Navi_Init</STRONG> (Thumb, 174 bytes, Stack size 24 bytes, navi.o(i.Navi_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = Navi_Init &rArr; CnbToAtti &rArr; __hardfp_asin &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TransHeading0to360
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDeg_Ex
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmAct
</UL>

<P><STRONG><a name="[12f]"></a>ParseStrCmd</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, protocol.o(i.ParseStrCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = ParseStrCmd &rArr; strstr
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysisRxdata
</UL>

<P><STRONG><a name="[12b]"></a>Pk_Init</STRONG> (Thumb, 102 bytes, Stack size 0 bytes, kalman.o(i.Pk_Init))
<BR><BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_Init
</UL>

<P><STRONG><a name="[f8]"></a>QToCnb</STRONG> (Thumb, 1252 bytes, Stack size 64 bytes, navi.o(i.QToCnb))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = QToCnb &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InertialSysAlignCompute
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
</UL>

<P><STRONG><a name="[12c]"></a>Qk_Init</STRONG> (Thumb, 102 bytes, Stack size 0 bytes, kalman.o(i.Qk_Init))
<BR><BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_Init
</UL>

<P><STRONG><a name="[e7]"></a>Qua_Mul</STRONG> (Thumb, 802 bytes, Stack size 64 bytes, matvecmath.o(i.Qua_Mul))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = Qua_Mul &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectAtti
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
</UL>

<P><STRONG><a name="[131]"></a>Query378Interrupt</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ch378_spi_hw.o(i.Query378Interrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Query378Interrupt
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wait378Interrupt
</UL>

<P><STRONG><a name="[133]"></a>Read_And_Check_GNSS_Data</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, read_and_check_gnss_data.o(i.Read_And_Check_GNSS_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = Read_And_Check_GNSS_Data &rArr; SaveGNSSData &rArr; ComputeLeverArmVn &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSSAndHeadDataTest
</UL>
<BR>[Called By]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gnss_check_bind
</UL>

<P><STRONG><a name="[128]"></a>Rk_Init</STRONG> (Thumb, 666 bytes, Stack size 8 bytes, kalman.o(i.Rk_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Rk_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KalCompute
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_Init
</UL>

<P><STRONG><a name="[135]"></a>SPI_Exchange</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, ch378_spi_hw.o(i.SPI_Exchange))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI_Exchange
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xReadCH378Data
</UL>

<P><STRONG><a name="[134]"></a>SaveGNSSData</STRONG> (Thumb, 1184 bytes, Stack size 72 bytes, read_and_check_gnss_data.o(i.SaveGNSSData))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = SaveGNSSData &rArr; ComputeLeverArmVn &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeLeverArmVn
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeLeverArmSn
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_And_Check_GNSS_Data
</UL>

<P><STRONG><a name="[10b]"></a>SaveINSData</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, kalman.o(i.SaveINSData))
<BR><BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Virtual_PPS_insert_5hz
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSS_Valid_PPSStart
</UL>

<P><STRONG><a name="[139]"></a>SendVersionInfo</STRONG> (Thumb, 48 bytes, Stack size 40 bytes, datado.o(i.SendVersionInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = SendVersionInfo &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysInit_Over
</UL>

<P><STRONG><a name="[13c]"></a>Soft_I2C_Ack</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, bsp_soft_i2c_master.o(i.Soft_I2C_Ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Soft_I2C_Ack &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Output
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Read_Byte
</UL>

<P><STRONG><a name="[13e]"></a>Soft_I2C_Master_Init</STRONG> (Thumb, 260 bytes, Stack size 8 bytes, bsp_soft_i2c_master.o(i.Soft_I2C_Master_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Soft_I2C_Master_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
</UL>
<BR>[Called By]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_interface_selection
</UL>

<P><STRONG><a name="[13f]"></a>Soft_I2C_NAck</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, bsp_soft_i2c_master.o(i.Soft_I2C_NAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Soft_I2C_NAck &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Output
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Read_Byte
</UL>

<P><STRONG><a name="[140]"></a>Soft_I2C_Read_Byte</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, bsp_soft_i2c_master.o(i.Soft_I2C_Read_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = Soft_I2C_Read_Byte &rArr; Soft_I2C_NAck &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Input
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_NAck
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Ack
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read
</UL>

<P><STRONG><a name="[141]"></a>Soft_I2C_SDA_Input</STRONG> (Thumb, 184 bytes, Stack size 8 bytes, bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Input))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Soft_I2C_SDA_Input &rArr; gpio_mode_set
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Wait_Ack
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Read_Byte
</UL>

<P><STRONG><a name="[13d]"></a>Soft_I2C_SDA_Output</STRONG> (Thumb, 196 bytes, Stack size 8 bytes, bsp_soft_i2c_master.o(i.Soft_I2C_SDA_Output))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_NAck
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Ack
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Stop
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Start
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Send_Byte
</UL>

<P><STRONG><a name="[142]"></a>Soft_I2C_Send_Byte</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, bsp_soft_i2c_master.o(i.Soft_I2C_Send_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = Soft_I2C_Send_Byte &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Output
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read
</UL>

<P><STRONG><a name="[143]"></a>Soft_I2C_Start</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, bsp_soft_i2c_master.o(i.Soft_I2C_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Soft_I2C_Start &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Output
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read
</UL>

<P><STRONG><a name="[144]"></a>Soft_I2C_Stop</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, bsp_soft_i2c_master.o(i.Soft_I2C_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Soft_I2C_Stop &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Output
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Wait_Ack
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read
</UL>

<P><STRONG><a name="[145]"></a>Soft_I2C_Wait_Ack</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, bsp_soft_i2c_master.o(i.Soft_I2C_Wait_Ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = Soft_I2C_Wait_Ack &rArr; Soft_I2C_Stop &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Input
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Stop
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read
</UL>

<P><STRONG><a name="[146]"></a>SysInit</STRONG> (Thumb, 178 bytes, Stack size 8 bytes, navi.o(i.SysInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SysInit &rArr; Kalman_Init &rArr; Rk_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_Init
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysVarDefaultSet
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[148]"></a>SysInit_Over</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, datado.o(i.SysInit_Over))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = SysInit_Over &rArr; INS_Init &rArr; comm_store_init &rArr; comm_resume_defaultPara &rArr; comm_set_customPara &rArr; fmc_write_8bit_data &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendVersionInfo
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a]"></a>SysTick_Handler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_decrement
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[147]"></a>SysVarDefaultSet</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, navi.o(i.SysVarDefaultSet))
<BR><BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysInit
</UL>

<P><STRONG><a name="[65]"></a>SystemInit</STRONG> (Thumb, 184 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SystemInit &rArr; system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[d]"></a>TAMPER_STAMP_IRQHandler</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, bsp_rtc.o(i.TAMPER_STAMP_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = TAMPER_STAMP_IRQHandler &rArr; rtc_show_timestamp &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_flag_get
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_flag_clear
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_show_timestamp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIMER0_UP_TIMER9_IRQHandler</STRONG> (Thumb, 122 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.TIMER0_UP_TIMER9_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 944<LI>Call Chain = TIMER0_UP_TIMER9_IRQHandler &rArr; generateCSVLogFileName &rArr; sow2Date &rArr; gst2time &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeSync
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generateCSVLogFileName
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.TIMER1_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 156 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.TIMER2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIMER2_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f9]"></a>TransHeading0to360</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, navi.o(i.TransHeading0to360))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = TransHeading0to360 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Navi_Init
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicNavi_Init
</UL>

<P><STRONG><a name="[3f]"></a>UART3_IRQHandler</STRONG> (Thumb, 130 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.UART3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = UART3_IRQHandler &rArr; usart_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_disable
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>UART4_IRQHandler</STRONG> (Thumb, 164 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.UART4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = UART4_IRQHandler &rArr; usart_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_disable
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>UART7_IRQHandler</STRONG> (Thumb, 852 bytes, Stack size 16 bytes, adxl355.o(i.UART7_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = UART7_IRQHandler &rArr; __aeabi_i2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculateCRC
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[11b]"></a>Uart_RxInit</STRONG> (Thumb, 250 bytes, Stack size 24 bytes, bsp_fmc.o(i.Uart_RxInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Uart_RxInit
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[157]"></a>Uart_SendMsg</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, bsp_fmc.o(i.Uart_SendMsg))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_send_end_frame
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_write_rsp
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_read_ver_rsp
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_read_dev_type_rsp
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_para_ehco_rsp
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_cali_ehco_rsp
</UL>

<P><STRONG><a name="[11a]"></a>Uart_TxInit</STRONG> (Thumb, 238 bytes, Stack size 24 bytes, bsp_fmc.o(i.Uart_TxInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Uart_TxInit
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[f7]"></a>UpdateAlignPosAndVn</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, dynamic_align.o(i.UpdateAlignPosAndVn))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DynamicInertialSysAlignCompute
</UL>

<P><STRONG><a name="[6]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[d5]"></a>Vec_Cross</STRONG> (Thumb, 294 bytes, Stack size 32 bytes, matvecmath.o(i.Vec_Cross))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = Vec_Cross &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeSi
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCib0i
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVibn
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeLeverArmVn
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeDelSenbb
</UL>

<P><STRONG><a name="[158]"></a>Virtual_PPS_insert_5hz</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, instestingentry.o(i.Virtual_PPS_insert_5hz))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = Virtual_PPS_insert_5hz &rArr; ComputeFk &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveINSData
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeXkk_1
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFk
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo
</UL>

<P><STRONG><a name="[b2]"></a>Wait378Interrupt</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, file_sys.o(i.Wait378Interrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_mDelayuS
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Query378Interrupt
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetIntStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteLocate
</UL>

<P><STRONG><a name="[159]"></a>__0printf</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[20b]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[1f4]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[20c]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[20d]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[15b]"></a>__0sprintf</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[20e]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[1d2]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generateCSVLogFileName
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sow2Date
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_show_timestamp
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg_canout
</UL>

<P><STRONG><a name="[20f]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[210]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[15f]"></a>__ARM_fpclassify</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_fpclassify
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
</UL>

<P><STRONG><a name="[ce]"></a>__hardfp_asin</STRONG> (Thumb, 770 bytes, Stack size 88 bytes, asin.o(i.__hardfp_asin))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = __hardfp_asin &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
</UL>

<P><STRONG><a name="[cd]"></a>__hardfp_atan</STRONG> (Thumb, 622 bytes, Stack size 48 bytes, atan.o(i.__hardfp_atan))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
</UL>

<P><STRONG><a name="[aa]"></a>__hardfp_cos</STRONG> (Thumb, 180 bytes, Stack size 32 bytes, cos.o(i.__hardfp_cos))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrStore_1s
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectAtti
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFn
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCie
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCen
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWien
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputePos
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeAttiRate
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AttiToCnb
</UL>

<P><STRONG><a name="[167]"></a>__hardfp_exp</STRONG> (Thumb, 714 bytes, Stack size 72 bytes, exp.o(i.__hardfp_exp))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = __hardfp_exp &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
</UL>

<P><STRONG><a name="[cb]"></a>__hardfp_fabs</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, fabs.o(i.__hardfp_fabs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSSAndHeadDataTest
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ErrStore_1s
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectAtti
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mat_Inv
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NaviCompute
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeVn
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToAtti
</UL>

<P><STRONG><a name="[169]"></a>__hardfp_floor</STRONG> (Thumb, 252 bytes, Stack size 40 bytes, floor.o(i.__hardfp_floor))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = __hardfp_floor &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeadd
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
</UL>

<P><STRONG><a name="[16a]"></a>__hardfp_pow</STRONG> (Thumb, 3072 bytes, Stack size 192 bytes, pow.o(i.__hardfp_pow))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = __hardfp_pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
</UL>
<BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
</UL>

<P><STRONG><a name="[a9]"></a>__hardfp_sin</STRONG> (Thumb, 180 bytes, Stack size 32 bytes, sin.o(i.__hardfp_sin))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectAtti
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFn
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCie
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeCen
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWien
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeRmRn
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeG
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeAttiRate
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AttiToCnb
</UL>

<P><STRONG><a name="[d0]"></a>__hardfp_sqrt</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, sqrt.o(i.__hardfp_sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = __hardfp_sqrt &rArr; _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveGNSSData
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CorrectAtti
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeFk
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeQ
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CnbToQ
</UL>

<P><STRONG><a name="[16e]"></a>__hardfp_strtod</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, strtod.o(i.__hardfp_strtod))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = __hardfp_strtod &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtod_int
</UL>
<BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
</UL>

<P><STRONG><a name="[d2]"></a>__hardfp_tan</STRONG> (Thumb, 108 bytes, Stack size 32 bytes, tan.o(i.__hardfp_tan))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = __hardfp_tan &rArr; __kernel_tan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeWenn
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ComputeAttiRate
</UL>

<P><STRONG><a name="[164]"></a>__ieee754_rem_pio2</STRONG> (Thumb, 938 bytes, Stack size 120 bytes, rred.o(i.__ieee754_rem_pio2))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = __ieee754_rem_pio2 &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>

<P><STRONG><a name="[166]"></a>__kernel_cos</STRONG> (Thumb, 322 bytes, Stack size 64 bytes, cos_i.o(i.__kernel_cos))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = __kernel_cos &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>

<P><STRONG><a name="[161]"></a>__kernel_poly</STRONG> (Thumb, 248 bytes, Stack size 24 bytes, poly.o(i.__kernel_poly))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[165]"></a>__kernel_sin</STRONG> (Thumb, 280 bytes, Stack size 72 bytes, sin_i.o(i.__kernel_sin))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = __kernel_sin &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>

<P><STRONG><a name="[16f]"></a>__kernel_tan</STRONG> (Thumb, 764 bytes, Stack size 128 bytes, tan_i.o(i.__kernel_tan))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = __kernel_tan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
</UL>

<P><STRONG><a name="[16c]"></a>__mathlib_dbl_divzero</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_divzero))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __mathlib_dbl_divzero &rArr; __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[15c]"></a>__mathlib_dbl_infnan</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_infnan))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_infnan &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
</UL>

<P><STRONG><a name="[16b]"></a>__mathlib_dbl_infnan2</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_infnan2))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_infnan2 &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[15e]"></a>__mathlib_dbl_invalid</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_invalid))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __mathlib_dbl_invalid &rArr; __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
</UL>

<P><STRONG><a name="[168]"></a>__mathlib_dbl_overflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_overflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_overflow &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[160]"></a>__mathlib_dbl_underflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_underflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_underflow &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
</UL>

<P><STRONG><a name="[211]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[212]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[213]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[15d]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_tan
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
</UL>

<P><STRONG><a name="[79]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, scanf_fp.o(i._is_digit), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[93]"></a>adc_interrupt_flag_clear</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>

<P><STRONG><a name="[1a6]"></a>adj_paraSyn</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, computerframeparse.o(i.adj_paraSyn))
<BR><BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
</UL>

<P><STRONG><a name="[174]"></a>analysisRxdata</STRONG> (Thumb, 1212 bytes, Stack size 104 bytes, protocol.o(i.analysisRxdata))
<BR><BR>[Stack]<UL><LI>Max Depth = 432<LI>Call Chain = analysisRxdata &rArr; mcusendtopcdriversdata &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcusendtopcdriversdata
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ParseStrCmd
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frameParse
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysisRxdata_enhanced
</UL>

<P><STRONG><a name="[177]"></a>analysisRxdata_enhanced</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, instestingentry.o(i.analysisRxdata_enhanced))
<BR><BR>[Stack]<UL><LI>Max Depth = 440<LI>Call Chain = analysisRxdata_enhanced &rArr; analysisRxdata &rArr; mcusendtopcdriversdata &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysisRxdata
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[102]"></a>app_accum_verify_8bit</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, app_tool.o(i.app_accum_verify_8bit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = app_accum_verify_8bit
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_11BB_send
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_00BB_send
</UL>

<P><STRONG><a name="[121]"></a>bmp280_init</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, bmp280.o(i.bmp280_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = bmp280_init &rArr; bmp2_init &rArr; get_calib_param &rArr; bmp2_get_regs
</UL>
<BR>[Calls]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_interface_selection
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_init
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_get_config
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[6e]"></a>bmp2_delay_us</STRONG> (Thumb, 14 bytes, Stack size 16 bytes, common.o(i.bmp2_delay_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = bmp2_delay_us &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Address Reference Count : 1]<UL><LI> common.o(i.bmp2_interface_selection)
</UL>
<P><STRONG><a name="[17a]"></a>bmp2_get_config</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, bmp2.o(i.bmp2_get_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = bmp2_get_config &rArr; bmp2_get_regs
</UL>
<BR>[Calls]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_get_regs
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp280_init
</UL>

<P><STRONG><a name="[17b]"></a>bmp2_get_regs</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, bmp2.o(i.bmp2_get_regs))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = bmp2_get_regs
</UL>
<BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;null_ptr_check
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_init
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_get_config
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_calib_param
</UL>

<P><STRONG><a name="[6c]"></a>bmp2_i2c_read</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, common.o(i.bmp2_i2c_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = bmp2_i2c_read &rArr; i2c_read &rArr; Soft_I2C_Read_Byte &rArr; Soft_I2C_NAck &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> common.o(i.bmp2_interface_selection)
</UL>
<P><STRONG><a name="[6d]"></a>bmp2_i2c_write</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, common.o(i.bmp2_i2c_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = bmp2_i2c_write &rArr; i2c_write &rArr; Soft_I2C_Wait_Ack &rArr; Soft_I2C_Stop &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_write
</UL>
<BR>[Address Reference Count : 1]<UL><LI> common.o(i.bmp2_interface_selection)
</UL>
<P><STRONG><a name="[179]"></a>bmp2_init</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, bmp2.o(i.bmp2_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = bmp2_init &rArr; get_calib_param &rArr; bmp2_get_regs
</UL>
<BR>[Calls]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_get_regs
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;null_ptr_check
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_calib_param
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp280_init
</UL>

<P><STRONG><a name="[178]"></a>bmp2_interface_selection</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, common.o(i.bmp2_interface_selection))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = bmp2_interface_selection &rArr; Soft_I2C_Master_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Master_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp280_init
</UL>

<P><STRONG><a name="[11e]"></a>bsp_can_init</STRONG> (Thumb, 222 bytes, Stack size 8 bytes, bsp_can.o(i.bsp_can_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = bsp_can_init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_interrupt_enable
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_init
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_filter_init
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_deinit
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[184]"></a>bsp_exti_config</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, bsp_exti.o(i.bsp_exti_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = bsp_exti_config &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;syscfg_exti_line_config
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_init
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_init
</UL>

<P><STRONG><a name="[120]"></a>bsp_exti_init</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, bsp_exti.o(i.bsp_exti_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = bsp_exti_init &rArr; bsp_exti_config &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[116]"></a>bsp_gpio_init</STRONG> (Thumb, 446 bytes, Stack size 8 bytes, bsp_gpio.o(i.bsp_gpio_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = bsp_gpio_init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[11d]"></a>bsp_rtc_init</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, bsp_rtc.o(i.bsp_rtc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = bsp_rtc_init &rArr; rtc_setup &rArr; rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_all_reset_flag_clear
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_timestamp_enable
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_interrupt_enable
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_flag_clear
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pmu_backup_write_enable
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_setup
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_pre_config
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[117]"></a>bsp_tim_init</STRONG> (Thumb, 344 bytes, Stack size 32 bytes, bsp_tim.o(i.bsp_tim_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = bsp_tim_init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_enable
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_shadow_config
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_pulse_value_config
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_mode_config
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_config
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_auto_reload_shadow_enable
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_timer_clock_prescaler_config
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[156]"></a>calculateCRC</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, adxl355.o(i.calculateCRC))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = calculateCRC
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART7_IRQHandler
</UL>

<P><STRONG><a name="[180]"></a>can_deinit</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, gd32f4xx_can.o(i.can_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = can_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_can_init
</UL>

<P><STRONG><a name="[182]"></a>can_filter_init</STRONG> (Thumb, 262 bytes, Stack size 8 bytes, gd32f4xx_can.o(i.can_filter_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = can_filter_init
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_can_init
</UL>

<P><STRONG><a name="[181]"></a>can_init</STRONG> (Thumb, 286 bytes, Stack size 16 bytes, gd32f4xx_can.o(i.can_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = can_init
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_can_init
</UL>

<P><STRONG><a name="[183]"></a>can_interrupt_enable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_can.o(i.can_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_can_init
</UL>

<P><STRONG><a name="[ab]"></a>can_message_receive</STRONG> (Thumb, 228 bytes, Stack size 8 bytes, gd32f4xx_can.o(i.can_message_receive))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = can_message_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
<LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_RX0_IRQHandler
</UL>

<P><STRONG><a name="[a7]"></a>caninfupdate</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, ins_data.o(i.caninfupdate))
<BR><BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Algorithm_before_otherDataDo
</UL>

<P><STRONG><a name="[199]"></a>comm_cali_ehco_rsp</STRONG> (Thumb, 106 bytes, Stack size 112 bytes, computerframeparse.o(i.comm_cali_ehco_rsp))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = comm_cali_ehco_rsp &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_SendMsg
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frameParse
</UL>

<P><STRONG><a name="[19a]"></a>comm_fm_update</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, computerframeparse.o(i.comm_fm_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = comm_fm_update
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
</UL>

<P><STRONG><a name="[1a5]"></a>comm_nav_para_syn</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, computerframeparse.o(i.comm_nav_para_syn))
<BR><BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frameParse
</UL>

<P><STRONG><a name="[1c9]"></a>comm_output_disable</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, computerframeparse.o(i.comm_output_disable))
<BR><BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frameParse
</UL>

<P><STRONG><a name="[19b]"></a>comm_para_ehco_rsp</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, computerframeparse.o(i.comm_para_ehco_rsp))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = comm_para_ehco_rsp &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_SendMsg
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frameParse
</UL>

<P><STRONG><a name="[1cb]"></a>comm_param_setbits</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, computerframeparse.o(i.comm_param_setbits))
<BR><BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frameParse
</UL>

<P><STRONG><a name="[19c]"></a>comm_read_dev_type_rsp</STRONG> (Thumb, 90 bytes, Stack size 80 bytes, computerframeparse.o(i.comm_read_dev_type_rsp))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = comm_read_dev_type_rsp &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_SendMsg
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frameParse
</UL>

<P><STRONG><a name="[19d]"></a>comm_read_ver_rsp</STRONG> (Thumb, 166 bytes, Stack size 120 bytes, computerframeparse.o(i.comm_read_ver_rsp))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = comm_read_ver_rsp &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_SendMsg
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frameParse
</UL>

<P><STRONG><a name="[19e]"></a>comm_resume_defaultPara</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, computerframeparse.o(i.comm_resume_defaultPara))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = comm_resume_defaultPara &rArr; comm_set_customPara &rArr; fmc_write_8bit_data &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_set_customPara
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frameParse
</UL>

<P><STRONG><a name="[1a0]"></a>comm_saveCaliData</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, computerframeparse.o(i.comm_saveCaliData))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = comm_saveCaliData &rArr; fmc_write_8bit_data &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hash32
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frameParse
</UL>

<P><STRONG><a name="[127]"></a>comm_send_end_frame</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, computerframeparse.o(i.comm_send_end_frame))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = comm_send_end_frame &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_SendMsg
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frameParse
</UL>

<P><STRONG><a name="[19f]"></a>comm_set_customPara</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, computerframeparse.o(i.comm_set_customPara))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = comm_set_customPara &rArr; fmc_write_8bit_data &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hash32
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frameParse
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_resume_defaultPara
</UL>

<P><STRONG><a name="[125]"></a>comm_store_init</STRONG> (Thumb, 266 bytes, Stack size 24 bytes, computerframeparse.o(i.comm_store_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = comm_store_init &rArr; comm_resume_defaultPara &rArr; comm_set_customPara &rArr; fmc_write_8bit_data &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_TxInit
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_RxInit
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_read_8bit_data
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hash32
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_saveCaliData
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_resume_defaultPara
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_nav_para_syn
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_fm_update
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adj_paraSyn
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[1a7]"></a>comm_test_switch</STRONG> (Thumb, 82 bytes, Stack size 8 bytes, computerframeparse.o(i.comm_test_switch))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = comm_test_switch &rArr; strncmp
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frameParse
</UL>

<P><STRONG><a name="[1a9]"></a>comm_write_rsp</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, computerframeparse.o(i.comm_write_rsp))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = comm_write_rsp &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_SendMsg
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frameParse
</UL>

<P><STRONG><a name="[149]"></a>delay_decrement</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, systick.o(i.delay_decrement))
<BR><BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[114]"></a>delay_init</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, systick.o(i.delay_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = delay_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_clksource_set
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[c8]"></a>delay_ms</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, systick.o(i.delay_ms))
<BR><BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mInitCH378Host
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mDelaymS
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_mDelaymS
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_interface_selection
</UL>

<P><STRONG><a name="[c9]"></a>delay_us</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, systick.o(i.delay_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mInitCH378Host
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_mDelayuS
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_NAck
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Ack
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Wait_Ack
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Stop
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Start
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Send_Byte
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Read_Byte
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_delay_us
</UL>

<P><STRONG><a name="[1ab]"></a>epoch2time</STRONG> (Thumb, 354 bytes, Stack size 112 bytes, time_unify.o(i.epoch2time))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = epoch2time &rArr; __hardfp_floor &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gst2time
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2utc
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
</UL>

<P><STRONG><a name="[119]"></a>exmc_asynchronous_sram_init</STRONG> (Thumb, 364 bytes, Stack size 120 bytes, bsp_fmc.o(i.exmc_asynchronous_sram_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = exmc_asynchronous_sram_init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_norsram_init
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_norsram_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[1ad]"></a>exmc_norsram_enable</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(i.exmc_norsram_enable))
<BR><BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
</UL>

<P><STRONG><a name="[1ac]"></a>exmc_norsram_init</STRONG> (Thumb, 224 bytes, Stack size 12 bytes, gd32f4xx_exmc.o(i.exmc_norsram_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = exmc_norsram_init
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
</UL>

<P><STRONG><a name="[186]"></a>exti_init</STRONG> (Thumb, 184 bytes, Stack size 8 bytes, gd32f4xx_exti.o(i.exti_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = exti_init
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
</UL>

<P><STRONG><a name="[fc]"></a>exti_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_exti.o(i.exti_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI3_IRQHandler
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI10_15_IRQHandler
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD32_GPIO_EXTI_IRQHandler
</UL>

<P><STRONG><a name="[fb]"></a>exti_interrupt_flag_get</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_exti.o(i.exti_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI3_IRQHandler
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI10_15_IRQHandler
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD32_GPIO_EXTI_IRQHandler
</UL>

<P><STRONG><a name="[162]"></a>fabs</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, fabs.o(i.fabs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_tan
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[1ae]"></a>fmc2sinsraw</STRONG> (Thumb, 2986 bytes, Stack size 144 bytes, readpaoche.o(i.fmc2sinsraw))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = fmc2sinsraw &rArr; __hardfp_strtod &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myget_8bit_I16
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myget_16bit_I32
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myget_16bit_D64
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myget_16bit_D32
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_strtod
</UL>
<BR>[Called By]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_OutDataSet
</UL>

<P><STRONG><a name="[1b4]"></a>fmc_byte_program</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, gd32f4xx_fmc.o(i.fmc_byte_program))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fmc_byte_program &rArr; fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_ready_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
</UL>

<P><STRONG><a name="[1a1]"></a>fmc_erase_sector_by_address</STRONG> (Thumb, 68 bytes, Stack size 48 bytes, bsp_fmc.o(i.fmc_erase_sector_by_address))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = fmc_erase_sector_by_address &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_info_get
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_unlock
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_erase
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_lock
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_flag_clear
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_set_customPara
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_saveCaliData
</UL>

<P><STRONG><a name="[1b8]"></a>fmc_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
</UL>

<P><STRONG><a name="[1ba]"></a>fmc_lock</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_lock))
<BR><BR>[Called By]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
</UL>

<P><STRONG><a name="[1a4]"></a>fmc_read_8bit_data</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, bsp_fmc.o(i.fmc_read_8bit_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fmc_read_8bit_data
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
</UL>

<P><STRONG><a name="[1b5]"></a>fmc_ready_wait</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_fmc.o(i.fmc_ready_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_state_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_erase
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_byte_program
</UL>

<P><STRONG><a name="[1b9]"></a>fmc_sector_erase</STRONG> (Thumb, 90 bytes, Stack size 12 bytes, gd32f4xx_fmc.o(i.fmc_sector_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = fmc_sector_erase &rArr; fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_ready_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
</UL>

<P><STRONG><a name="[1b6]"></a>fmc_sector_info_get</STRONG> (Thumb, 366 bytes, Stack size 40 bytes, bsp_fmc.o(i.fmc_sector_info_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
</UL>

<P><STRONG><a name="[1bb]"></a>fmc_state_get</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_state_get))
<BR><BR>[Called By]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_ready_wait
</UL>

<P><STRONG><a name="[1b7]"></a>fmc_unlock</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_unlock))
<BR><BR>[Called By]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_erase_sector_by_address
</UL>

<P><STRONG><a name="[1a3]"></a>fmc_write_8bit_data</STRONG> (Thumb, 132 bytes, Stack size 88 bytes, bsp_fmc.o(i.fmc_write_8bit_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = fmc_write_8bit_data &rArr; fmc_sector_info_get
</UL>
<BR>[Calls]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sector_name_to_number
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_info_get
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_unlock
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_erase
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_lock
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_flag_clear
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_byte_program
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_set_customPara
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_saveCaliData
</UL>

<P><STRONG><a name="[10c]"></a>fpgadata_Predo</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, instestingentry.o(i.fpgadata_Predo))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = fpgadata_Predo &rArr; fpgadata_Predo_chen &rArr; fpgadata_Predo_chen_OutDataSet &rArr; fmc2sinsraw &rArr; __hardfp_strtod &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gnss_check_bind
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Virtual_PPS_insert_5hz
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSS_Valid_PPSStart
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSS_Lost_Time
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GNSS_Last_TIME
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ACC_gyroreset_r_TAFEAG16_buf
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912AlgorithmEntry
</UL>

<P><STRONG><a name="[1bd]"></a>fpgadata_Predo_chen</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, instestingentry.o(i.fpgadata_Predo_chen))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = fpgadata_Predo_chen &rArr; fpgadata_Predo_chen_OutDataSet &rArr; fmc2sinsraw &rArr; __hardfp_strtod &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_preAlgParm_370
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_algParmCash
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_setRParm_gyro
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_setRParm_acc
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_gyro
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_SetAlgParm_acc
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen_OutDataSet
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo
</UL>

<P><STRONG><a name="[1c0]"></a>fpgadata_Predo_chen_OutDataSet</STRONG> (Thumb, 234 bytes, Stack size 24 bytes, instestingentry.o(i.fpgadata_Predo_chen_OutDataSet))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = fpgadata_Predo_chen_OutDataSet &rArr; fmc2sinsraw &rArr; __hardfp_strtod &rArr; __strtod_int &rArr; _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen
</UL>

<P><STRONG><a name="[1c5]"></a>fpgadata_Predo_chen_SetAlgParm_acc</STRONG> (Thumb, 912 bytes, Stack size 48 bytes, instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_acc))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = fpgadata_Predo_chen_SetAlgParm_acc &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen
</UL>

<P><STRONG><a name="[1c3]"></a>fpgadata_Predo_chen_SetAlgParm_gyro</STRONG> (Thumb, 948 bytes, Stack size 48 bytes, instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_gyro))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = fpgadata_Predo_chen_SetAlgParm_gyro &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen
</UL>

<P><STRONG><a name="[1c4]"></a>fpgadata_Predo_chen_SetAlgParm_setRParm_acc</STRONG> (Thumb, 102 bytes, Stack size 0 bytes, instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_setRParm_acc))
<BR><BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen
</UL>

<P><STRONG><a name="[1c2]"></a>fpgadata_Predo_chen_SetAlgParm_setRParm_gyro</STRONG> (Thumb, 102 bytes, Stack size 0 bytes, instestingentry.o(i.fpgadata_Predo_chen_SetAlgParm_setRParm_gyro))
<BR><BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen
</UL>

<P><STRONG><a name="[1c1]"></a>fpgadata_Predo_chen_algParmCash</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, instestingentry.o(i.fpgadata_Predo_chen_algParmCash))
<BR><BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen
</UL>

<P><STRONG><a name="[1bf]"></a>fpgadata_Predo_chen_preAlgParm_370</STRONG> (Thumb, 272 bytes, Stack size 32 bytes, instestingentry.o(i.fpgadata_Predo_chen_preAlgParm_370))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = fpgadata_Predo_chen_preAlgParm_370 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo_chen
</UL>

<P><STRONG><a name="[1c6]"></a>fpgadata_syn_count_do</STRONG> (Thumb, 172 bytes, Stack size 8 bytes, fpgad.o(i.fpgadata_syn_count_do))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = fpgadata_syn_count_do &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
</UL>

<P><STRONG><a name="[6a]"></a>fputc</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, ins_init.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fputc &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[176]"></a>frameParse</STRONG> (Thumb, 1426 bytes, Stack size 48 bytes, computerframeparse.o(i.frameParse))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = frameParse &rArr; comm_read_ver_rsp &rArr; Uart_SendMsg &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_send_end_frame
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hex2Float
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_write_rsp
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_test_switch
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_set_customPara
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_saveCaliData
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_resume_defaultPara
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_read_ver_rsp
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_read_dev_type_rsp
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_param_setbits
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_para_ehco_rsp
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_output_disable
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_nav_para_syn
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_cali_ehco_rsp
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysisRxdata
</UL>

<P><STRONG><a name="[123]"></a>gd_eval_com_init</STRONG> (Thumb, 266 bytes, Stack size 16 bytes, ins_init.o(i.gd_eval_com_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = gd_eval_com_init &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receive_config
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[14f]"></a>generateCSVLogFileName</STRONG> (Thumb, 86 bytes, Stack size 544 bytes, logger.o(i.generateCSVLogFileName))
<BR><BR>[Stack]<UL><LI>Max Depth = 936<LI>Call Chain = generateCSVLogFileName &rArr; sow2Date &rArr; gst2time &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sow2Date
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0_UP_TIMER9_IRQHandler
</UL>

<P><STRONG><a name="[1d3]"></a>get_fpgadata</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, fpgad.o(i.get_fpgadata))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = get_fpgadata &rArr; fpgadata_syn_count_do &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata_do
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata_before
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata_after_otherDataDo
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_syn_count_do
</UL>
<BR>[Called By]<UL><LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata_enhanced
</UL>

<P><STRONG><a name="[1d6]"></a>get_fpgadata_after_otherDataDo</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, fpgad.o(i.get_fpgadata_after_otherDataDo))
<BR><BR>[Called By]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
</UL>

<P><STRONG><a name="[1d4]"></a>get_fpgadata_before</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, fpgad.o(i.get_fpgadata_before))
<BR><BR>[Called By]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
</UL>

<P><STRONG><a name="[1d5]"></a>get_fpgadata_do</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, fpgad.o(i.get_fpgadata_do))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = get_fpgadata_do
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
</UL>

<P><STRONG><a name="[1d7]"></a>get_fpgadata_enhanced</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, instestingentry.o(i.get_fpgadata_enhanced))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = get_fpgadata_enhanced &rArr; get_fpgadata &rArr; fpgadata_syn_count_do &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1be]"></a>gnss_check_bind</STRONG> (Thumb, 170 bytes, Stack size 8 bytes, instestingentry.o(i.gnss_check_bind))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = gnss_check_bind &rArr; Read_And_Check_GNSS_Data &rArr; SaveGNSSData &rArr; ComputeLeverArmVn &rArr; Mat_Mul &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_And_Check_GNSS_Data
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Kalman_StartUp
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BindDefaultSet_by_GNSS
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_Predo
</UL>

<P><STRONG><a name="[c1]"></a>gpio_af_set</STRONG> (Thumb, 94 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_af_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gpio_init
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_can_init
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_Port_Init
</UL>

<P><STRONG><a name="[ad]"></a>gpio_bit_reset</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_reset))
<BR><BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mInitCH378Host
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LEDIndicator
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_RX0_IRQHandler
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_NAck
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Ack
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetIntStatus
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetICVer
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Wait_Ack
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Stop
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Start
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Send_Byte
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Read_Byte
</UL>

<P><STRONG><a name="[ae]"></a>gpio_bit_set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mInitCH378Host
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LEDIndicator
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN0_RX0_IRQHandler
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_NAck
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Ack
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_Port_Init
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteOfsBlock
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ReadVar8
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetIntStatus
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetICVer
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteLocate
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Wait_Ack
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Stop
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Start
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Send_Byte
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Read_Byte
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Master_Init
</UL>

<P><STRONG><a name="[132]"></a>gpio_input_bit_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_input_bit_get))
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Query378Interrupt
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Wait_Ack
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Read_Byte
</UL>

<P><STRONG><a name="[c2]"></a>gpio_mode_set</STRONG> (Thumb, 78 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_mode_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gpio_init
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_can_init
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Output
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Input
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_Port_Init
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Master_Init
</UL>

<P><STRONG><a name="[c3]"></a>gpio_output_options_set</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_output_options_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_output_options_set
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gpio_init
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_can_init
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Output
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_Port_Init
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Master_Init
</UL>

<P><STRONG><a name="[1d9]"></a>gpst2time</STRONG> (Thumb, 142 bytes, Stack size 48 bytes, time_unify.o(i.gpst2time))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = gpst2time &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeSync
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
</UL>

<P><STRONG><a name="[1da]"></a>gpst2utc</STRONG> (Thumb, 164 bytes, Stack size 88 bytes, time_unify.o(i.gpst2utc))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = gpst2utc &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timediff
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeadd
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeSync
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
</UL>

<P><STRONG><a name="[1dd]"></a>gst2time</STRONG> (Thumb, 142 bytes, Stack size 48 bytes, time_unify.o(i.gst2time))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = gst2time &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;epoch2time
</UL>
<BR>[Called By]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sow2Date
</UL>

<P><STRONG><a name="[1a2]"></a>hash32</STRONG> (Thumb, 60 bytes, Stack size 20 bytes, computerframeparse.o(i.hash32))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = hash32
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_store_init
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_set_customPara
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;comm_saveCaliData
</UL>

<P><STRONG><a name="[1ca]"></a>hex2Float</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, data_convert.o(i.hex2Float))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = hex2Float
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frameParse
</UL>

<P><STRONG><a name="[17d]"></a>i2c_read</STRONG> (Thumb, 118 bytes, Stack size 24 bytes, common.o(i.i2c_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = i2c_read &rArr; Soft_I2C_Read_Byte &rArr; Soft_I2C_NAck &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Wait_Ack
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Stop
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Start
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Send_Byte
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Read_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_i2c_read
</UL>

<P><STRONG><a name="[17e]"></a>i2c_write</STRONG> (Thumb, 72 bytes, Stack size 24 bytes, common.o(i.i2c_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = i2c_write &rArr; Soft_I2C_Wait_Ack &rArr; Soft_I2C_Stop &rArr; Soft_I2C_SDA_Output &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Wait_Ack
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Stop
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Start
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_i2c_write
</UL>

<P><STRONG><a name="[115]"></a>initializationdriversettings</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, gdwatch.o(i.initializationdriversettings))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = initializationdriversettings
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[1de]"></a>loopDoOther</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, datado.o(i.loopDoOther))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = loopDoOther &rArr; uart4sendmsg_canout &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LEDIndicator
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg_canout
</UL>
<BR>[Called By]<UL><LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;loopDoOther_enhanced
</UL>

<P><STRONG><a name="[1e0]"></a>loopDoOther_enhanced</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, instestingentry.o(i.loopDoOther_enhanced))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = loopDoOther_enhanced &rArr; loopDoOther &rArr; uart4sendmsg_canout &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;loopDoOther
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[126]"></a>mDelaymS</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, ch395spi.o(i.mDelaymS))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = mDelaymS
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[11c]"></a>mInitCH378Host</STRONG> (Thumb, 122 bytes, Stack size 8 bytes, ch378_spi_hw.o(i.mInitCH378Host))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = mInitCH378Host &rArr; CH378_Port_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_Port_Init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Data
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xWriteCH378Cmd
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xReadCH378Data
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetICVer
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[64]"></a>main</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 2416<LI>Call Chain = main &rArr; AlgorithmDo_enhanced &rArr; AlgorithmDo &rArr; INS912AlgorithmEntry &rArr; AlgorithmAct &rArr; KalCompute &rArr; ComputeKk &rArr; Mat_Inv &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;loopDoOther_enhanced
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_fpgadata_enhanced
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysisRxdata_enhanced
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysInit_Over
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysInit
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912_Output_enhanced
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AlgorithmDo_enhanced
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[175]"></a>mcusendtopcdriversdata</STRONG> (Thumb, 292 bytes, Stack size 304 bytes, gdwatch.o(i.mcusendtopcdriversdata))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = mcusendtopcdriversdata &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analysisRxdata
</UL>

<P><STRONG><a name="[1b1]"></a>myget_16bit_D32</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, readpaoche.o(i.myget_16bit_D32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = myget_16bit_D32
</UL>
<BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
</UL>

<P><STRONG><a name="[1b3]"></a>myget_16bit_D64</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, readpaoche.o(i.myget_16bit_D64))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = myget_16bit_D64
</UL>
<BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
</UL>

<P><STRONG><a name="[1af]"></a>myget_16bit_I32</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, readpaoche.o(i.myget_16bit_I32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = myget_16bit_I32
</UL>
<BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
</UL>

<P><STRONG><a name="[1b0]"></a>myget_8bit_I16</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, readpaoche.o(i.myget_8bit_I16))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = myget_8bit_I16
</UL>
<BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc2sinsraw
</UL>

<P><STRONG><a name="[122]"></a>nvic_irq_enable</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, gd32f4xx_misc.o(i.nvic_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_can_init
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
</UL>

<P><STRONG><a name="[191]"></a>nvic_priority_group_set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_priority_group_set))
<BR><BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[111]"></a>nvic_vector_table_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_vector_table_set))
<BR><BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[1e1]"></a>parseFPGABuff</STRONG> (Thumb, 1538 bytes, Stack size 64 bytes, logger.o(i.parseFPGABuff))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = parseFPGABuff &rArr; __hardfp_exp &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
</UL>

<P><STRONG><a name="[187]"></a>pmu_backup_write_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_pmu.o(i.pmu_backup_write_enable))
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
</UL>

<P><STRONG><a name="[10d]"></a>pnavout_set</STRONG> (Thumb, 176 bytes, Stack size 8 bytes, instestingentry.o(i.pnavout_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = pnavout_set &rArr; __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS912AlgorithmEntry
</UL>

<P><STRONG><a name="[113]"></a>rcu_all_reset_flag_clear</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[1f2]"></a>rcu_clock_freq_get</STRONG> (Thumb, 264 bytes, Stack size 84 bytes, gd32f4xx_rcu.o(i.rcu_clock_freq_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>

<P><STRONG><a name="[112]"></a>rcu_flag_get</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
</UL>

<P><STRONG><a name="[1e7]"></a>rcu_osci_on</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_osci_on))
<BR><BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_pre_config
</UL>

<P><STRONG><a name="[1e2]"></a>rcu_osci_stab_wait</STRONG> (Thumb, 342 bytes, Stack size 20 bytes, gd32f4xx_rcu.o(i.rcu_osci_stab_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = rcu_osci_stab_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_pre_config
</UL>

<P><STRONG><a name="[c0]"></a>rcu_periph_clock_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_asynchronous_sram_init
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_gpio_init
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_can_init
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_configuration
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_pre_config
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Output
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_SDA_Input
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_Port_Init
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_I2C_Master_Init
</UL>

<P><STRONG><a name="[198]"></a>rcu_periph_reset_disable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_disable))
<BR><BR>[Called By]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_deinit
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_deinit
</UL>

<P><STRONG><a name="[197]"></a>rcu_periph_reset_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_enable))
<BR><BR>[Called By]<UL><LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_deinit
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;can_deinit
</UL>

<P><STRONG><a name="[1e8]"></a>rcu_rtc_clock_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_rtc_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_pre_config
</UL>

<P><STRONG><a name="[18c]"></a>rcu_timer_clock_prescaler_config</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config))
<BR><BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[14d]"></a>rtc_flag_clear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
<LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TAMPER_STAMP_IRQHandler
</UL>

<P><STRONG><a name="[14b]"></a>rtc_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TAMPER_STAMP_IRQHandler
</UL>

<P><STRONG><a name="[1e3]"></a>rtc_init</STRONG> (Thumb, 190 bytes, Stack size 20 bytes, gd32f4xx_rtc.o(i.rtc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_sync_wait
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init_mode_exit
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init_mode_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_setup
</UL>

<P><STRONG><a name="[1e4]"></a>rtc_init_mode_enter</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, gd32f4xx_rtc.o(i.rtc_init_mode_enter))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rtc_init_mode_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[1e5]"></a>rtc_init_mode_exit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_init_mode_exit))
<BR><BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[18b]"></a>rtc_interrupt_enable</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
</UL>

<P><STRONG><a name="[188]"></a>rtc_pre_config</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, bsp_rtc.o(i.rtc_pre_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = rtc_pre_config &rArr; rcu_osci_stab_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_sync_wait
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_rtc_clock_config
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_on
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
</UL>

<P><STRONG><a name="[1e6]"></a>rtc_register_sync_wait</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, gd32f4xx_rtc.o(i.rtc_register_sync_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rtc_register_sync_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_pre_config
</UL>

<P><STRONG><a name="[189]"></a>rtc_setup</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, bsp_rtc.o(i.rtc_setup))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = rtc_setup &rArr; rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timeSync
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
</UL>

<P><STRONG><a name="[14c]"></a>rtc_show_timestamp</STRONG> (Thumb, 388 bytes, Stack size 32 bytes, bsp_rtc.o(i.rtc_show_timestamp))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = rtc_show_timestamp &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_timestamp_subsecond_get
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_timestamp_get
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TAMPER_STAMP_IRQHandler
</UL>

<P><STRONG><a name="[18a]"></a>rtc_timestamp_enable</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_timestamp_enable))
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_rtc_init
</UL>

<P><STRONG><a name="[1e9]"></a>rtc_timestamp_get</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_timestamp_get))
<BR><BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_show_timestamp
</UL>

<P><STRONG><a name="[1ea]"></a>rtc_timestamp_subsecond_get</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get))
<BR><BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_show_timestamp
</UL>

<P><STRONG><a name="[1bc]"></a>sector_name_to_number</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, bsp_fmc.o(i.sector_name_to_number))
<BR><BR>[Called By]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_write_8bit_data
</UL>

<P><STRONG><a name="[1d1]"></a>sow2Date</STRONG> (Thumb, 140 bytes, Stack size 104 bytes, time_unify.o(i.sow2Date))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = sow2Date &rArr; gst2time &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2epoch
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gst2time
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;generateCSVLogFileName
</UL>

<P><STRONG><a name="[c5]"></a>spi_crc_off</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_crc_off))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_Port_Init
</UL>

<P><STRONG><a name="[c6]"></a>spi_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_enable))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_Port_Init
</UL>

<P><STRONG><a name="[138]"></a>spi_i2s_data_receive</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Exchange
</UL>

<P><STRONG><a name="[137]"></a>spi_i2s_data_transmit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Exchange
</UL>

<P><STRONG><a name="[136]"></a>spi_i2s_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Exchange
</UL>

<P><STRONG><a name="[c4]"></a>spi_init</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_init))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_Port_Init
</UL>

<P><STRONG><a name="[163]"></a>sqrt</STRONG> (Thumb, 110 bytes, Stack size 32 bytes, sqrt.o(i.sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = sqrt &rArr; _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_asin
</UL>

<P><STRONG><a name="[fe]"></a>synthesisLogBuf</STRONG> (Thumb, 644 bytes, Stack size 520 bytes, logger.o(i.synthesisLogBuf))
<BR><BR>[Stack]<UL><LI>Max Depth = 984<LI>Call Chain = synthesisLogBuf &rArr; time2str &rArr; __hardfp_pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2utc
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseFPGABuff
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
</UL>

<P><STRONG><a name="[185]"></a>syscfg_exti_line_config</STRONG> (Thumb, 166 bytes, Stack size 16 bytes, gd32f4xx_syscfg.o(i.syscfg_exti_line_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = syscfg_exti_line_config
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_exti_config
</UL>

<P><STRONG><a name="[1aa]"></a>systick_clksource_set</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.systick_clksource_set))
<BR><BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
</UL>

<P><STRONG><a name="[1eb]"></a>time2epoch</STRONG> (Thumb, 248 bytes, Stack size 56 bytes, time_unify.o(i.time2epoch))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = time2epoch &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2str
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sow2Date
</UL>

<P><STRONG><a name="[1ec]"></a>time2str</STRONG> (Thumb, 244 bytes, Stack size 160 bytes, time_unify.o(i.time2str))
<BR><BR>[Stack]<UL><LI>Max Depth = 464<LI>Call Chain = time2str &rArr; __hardfp_pow &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;time2epoch
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;synthesisLogBuf
</UL>

<P><STRONG><a name="[150]"></a>timeSync</STRONG> (Thumb, 290 bytes, Stack size 128 bytes, bsp_rtc.o(i.timeSync))
<BR><BR>[Stack]<UL><LI>Max Depth = 456<LI>Call Chain = timeSync &rArr; gpst2utc &rArr; epoch2time &rArr; __hardfp_floor &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2utc
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2time
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_setup
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;localtime
</UL>
<BR>[Called By]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0_UP_TIMER9_IRQHandler
</UL>

<P><STRONG><a name="[1db]"></a>timeadd</STRONG> (Thumb, 134 bytes, Stack size 48 bytes, time_unify.o(i.timeadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = timeadd &rArr; __hardfp_floor &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_floor
</UL>
<BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2utc
</UL>

<P><STRONG><a name="[1dc]"></a>timediff</STRONG> (Thumb, 78 bytes, Stack size 40 bytes, time_unify.o(i.timediff))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = timediff &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpst2utc
</UL>

<P><STRONG><a name="[196]"></a>timer_auto_reload_shadow_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable))
<BR><BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[192]"></a>timer_channel_output_config</STRONG> (Thumb, 484 bytes, Stack size 8 bytes, gd32f4xx_timer.o(i.timer_channel_output_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = timer_channel_output_config
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[194]"></a>timer_channel_output_mode_config</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_mode_config))
<BR><BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[193]"></a>timer_channel_output_pulse_value_config</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config))
<BR><BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[195]"></a>timer_channel_output_shadow_config</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_shadow_config))
<BR><BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[18d]"></a>timer_deinit</STRONG> (Thumb, 374 bytes, Stack size 8 bytes, gd32f4xx_timer.o(i.timer_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = timer_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[190]"></a>timer_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_enable))
<BR><BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[18e]"></a>timer_init</STRONG> (Thumb, 122 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_init))
<BR><BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[18f]"></a>timer_interrupt_enable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_tim_init
</UL>

<P><STRONG><a name="[151]"></a>timer_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER2_IRQHandler
</UL>

<P><STRONG><a name="[14e]"></a>timer_interrupt_flag_get</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER2_IRQHandler
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER0_UP_TIMER9_IRQHandler
</UL>

<P><STRONG><a name="[11f]"></a>trng_configuration</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, ins_init.o(i.trng_configuration))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = trng_configuration &rArr; trng_ready_check
</UL>
<BR>[Calls]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_enable
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_deinit
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_ready_check
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[1ee]"></a>trng_deinit</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_trng.o(i.trng_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = trng_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_configuration
</UL>

<P><STRONG><a name="[1ef]"></a>trng_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_trng.o(i.trng_enable))
<BR><BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_configuration
</UL>

<P><STRONG><a name="[1f1]"></a>trng_flag_get</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_trng.o(i.trng_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_ready_check
</UL>

<P><STRONG><a name="[1f0]"></a>trng_ready_check</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, ins_init.o(i.trng_ready_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = trng_ready_check
</UL>
<BR>[Calls]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_configuration
</UL>

<P><STRONG><a name="[103]"></a>uart4sendmsg</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, gd32f4xx_it.o(i.uart4sendmsg))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_enable
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mcusendtopcdriversdata
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fpgadata_syn_count_do
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart_SendMsg
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg_canout
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendVersionInfo
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_11BB_send
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGATo422_00BB_send
</UL>

<P><STRONG><a name="[1df]"></a>uart4sendmsg_canout</STRONG> (Thumb, 118 bytes, Stack size 264 bytes, bsp_can.o(i.uart4sendmsg_canout))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = uart4sendmsg_canout &rArr; uart4sendmsg &rArr; usart_interrupt_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;loopDoOther
</UL>

<P><STRONG><a name="[1cd]"></a>usart_baudrate_set</STRONG> (Thumb, 224 bytes, Stack size 32 bytes, gd32f4xx_usart.o(i.usart_baudrate_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[153]"></a>usart_data_receive</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_IRQHandler
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_IRQHandler
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART7_IRQHandler
</UL>

<P><STRONG><a name="[154]"></a>usart_data_transmit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_IRQHandler
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_IRQHandler
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[1cc]"></a>usart_deinit</STRONG> (Thumb, 210 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[1d0]"></a>usart_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_enable))
<BR><BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[1c7]"></a>usart_flag_get</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[155]"></a>usart_interrupt_disable</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_interrupt_disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_interrupt_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_IRQHandler
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_IRQHandler
</UL>

<P><STRONG><a name="[124]"></a>usart_interrupt_enable</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_interrupt_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_interrupt_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart4sendmsg
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;INS_Init
</UL>

<P><STRONG><a name="[152]"></a>usart_interrupt_flag_get</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, gd32f4xx_usart.o(i.usart_interrupt_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usart_interrupt_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_IRQHandler
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_IRQHandler
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART7_IRQHandler
</UL>

<P><STRONG><a name="[1ce]"></a>usart_receive_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_receive_config))
<BR><BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[1cf]"></a>usart_transmit_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_transmit_config))
<BR><BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_com_init
</UL>

<P><STRONG><a name="[1f3]"></a>writeCSVFileHead</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, logger.o(i.writeCSVFileHead))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = writeCSVFileHead &rArr; CH378ByteWrite &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVLog
</UL>

<P><STRONG><a name="[ff]"></a>writeCSVLog</STRONG> (Thumb, 330 bytes, Stack size 40 bytes, logger.o(i.writeCSVLog))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = writeCSVLog &rArr; writeCSVFileHead &rArr; CH378ByteWrite &rArr; Wait378Interrupt &rArr; CH378GetIntStatus &rArr; xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetDiskStatus
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378_mDelaymS
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeCSVFileHead
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378FileOpen
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378FileCreate
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteLocate
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI5_9_IRQHandler
</UL>

<P><STRONG><a name="[bc]"></a>xReadCH378Data</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, ch378_spi_hw.o(i.xReadCH378Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xReadCH378Data &rArr; SPI_Exchange
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Exchange
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mInitCH378Host
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ReadVar8
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetIntStatus
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetICVer
</UL>

<P><STRONG><a name="[b0]"></a>xWriteCH378Cmd</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, ch378_spi_hw.o(i.xWriteCH378Cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = xWriteCH378Cmd &rArr; delay_us
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Exchange
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mInitCH378Host
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteOfsBlock
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SendCmdWaitInt
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ReadVar8
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetIntStatus
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378GetICVer
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteLocate
</UL>

<P><STRONG><a name="[b1]"></a>xWriteCH378Data</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, ch378_spi_hw.o(i.xWriteCH378Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = xWriteCH378Data &rArr; SPI_Exchange
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Exchange
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mInitCH378Host
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378WriteOfsBlock
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378SetFileName
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ReadVar8
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteWrite
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CH378ByteLocate
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[17f]"></a>get_calib_param</STRONG> (Thumb, 236 bytes, Stack size 40 bytes, bmp2.o(i.get_calib_param))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = get_calib_param &rArr; bmp2_get_regs
</UL>
<BR>[Calls]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_get_regs
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_init
</UL>

<P><STRONG><a name="[17c]"></a>null_ptr_check</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, bmp2.o(i.null_ptr_check))
<BR><BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_init
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bmp2_get_regs
</UL>

<P><STRONG><a name="[1ed]"></a>system_clock_240m_25m_hxtal</STRONG> (Thumb, 240 bytes, Stack size 0 bytes, system_gd32f4xx.o(i.system_clock_240m_25m_hxtal))
<BR><BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[14a]"></a>system_clock_config</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.system_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_240m_25m_hxtal
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[170]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[15a]"></a>_printf_core</STRONG> (Thumb, 1704 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[172]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[171]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[6b]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL>
<P><STRONG><a name="[74]"></a>_fp_value</STRONG> (Thumb, 296 bytes, Stack size 64 bytes, scanf_fp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2d
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[8a]"></a>_local_sscanf</STRONG> (Thumb, 54 bytes, Stack size 56 bytes, strtod.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = _local_sscanf &rArr; _scanf_real
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strtod_int
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
