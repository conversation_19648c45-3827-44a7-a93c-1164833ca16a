//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：app_test.c
// 文件标识：
// 文件摘要：应用层功能测试文件
//
// 当前版本：V1.0
// 作    者：迁移自HPM6750项目
// 完成时间：2024.12.19
//---------------------------------------------------------

#include "app_test.h"
#include "app_config.h"
#include "bsp_flash.h"
#include "bsp_sd_fatfs.h"
#include "SetParaBao.h"
#include "InsTestingEntry_Compat.h"
#include <stdio.h>

// 测试结果统计
static uint32_t test_passed = 0;
static uint32_t test_failed = 0;

/**
 * @brief 测试结果输出
 * @param test_name 测试名称
 * @param result 测试结果 (0: 失败, 1: 成功)
 */
static void test_result(const char* test_name, int result)
{
    if (result) {
        test_passed++;
        DEBUG_PRINT("[PASS] %s\r\n", test_name);
    } else {
        test_failed++;
        DEBUG_PRINT("[FAIL] %s\r\n", test_name);
    }
}

/**
 * @brief Flash功能测试
 * @return 测试结果
 */
int test_flash_operations(void)
{
    DEBUG_PRINT("=== Flash Operations Test ===\r\n");
    
    int result = 1;
    uint8_t write_data[256];
    uint8_t read_data[256];
    
    // 初始化测试数据
    for (int i = 0; i < 256; i++) {
        write_data[i] = i & 0xFF;
    }
    
    // 测试Flash初始化
    if (norflash_init() != 0) {
        test_result("Flash Init", 0);
        return 0;
    }
    test_result("Flash Init", 1);
    
    // 测试Flash擦除
    if (norflash_erase_sector(0x10000) != 0) {
        test_result("Flash Erase", 0);
        result = 0;
    } else {
        test_result("Flash Erase", 1);
    }
    
    // 测试Flash写入
    if (norflash_write(0x10000, write_data, 256) != 0) {
        test_result("Flash Write", 0);
        result = 0;
    } else {
        test_result("Flash Write", 1);
    }
    
    // 测试Flash读取
    if (norflash_read(0x10000, read_data, 256) != 0) {
        test_result("Flash Read", 0);
        result = 0;
    } else {
        test_result("Flash Read", 1);
    }
    
    // 测试数据一致性
    int data_match = 1;
    for (int i = 0; i < 256; i++) {
        if (write_data[i] != read_data[i]) {
            data_match = 0;
            break;
        }
    }
    test_result("Flash Data Consistency", data_match);
    
    return result && data_match;
}

/**
 * @brief SD卡功能测试
 * @return 测试结果
 */
int test_sd_operations(void)
{
    DEBUG_PRINT("=== SD Card Operations Test ===\r\n");
    
    int result = 1;
    
    // 测试SD卡初始化
    if (Fatfs_Init() != 0) {
        test_result("SD Init", 0);
        return 0;
    }
    test_result("SD Init", 1);
    
    // 测试SD卡状态
    uint8_t sd_status = sd_card_status_get();
    if (sd_status == SD_STATUS_OK) {
        test_result("SD Status Check", 1);
    } else {
        test_result("SD Status Check", 0);
        result = 0;
    }
    
    // 测试文件操作
    SdFileOperateTypeSet(SD_OPERATE_OPEN_WRITE, SD_FILE_TYPE_BB00);
    test_result("SD File Open Write", 1);
    
    // 写入测试数据
    SdFileWriteOperate();
    test_result("SD File Write", 1);
    
    // 关闭文件
    SdFileOperateTypeSet(SD_OPERATE_CLOSE_WRITE, SD_FILE_TYPE_BB00);
    test_result("SD File Close", 1);
    
    // 测试文件读取
    SdFileOperateTypeSet(SD_OPERATE_OPEN_READ, SD_FILE_TYPE_BB00);
    SdFileReadOperate();
    SdFileOperateTypeSet(SD_OPERATE_CLOSE_READ, SD_FILE_TYPE_BB00);
    test_result("SD File Read", 1);
    
    return result;
}

/**
 * @brief 参数配置功能测试
 * @return 测试结果
 */
int test_parameter_config(void)
{
    DEBUG_PRINT("=== Parameter Config Test ===\r\n");
    
    int result = 1;
    
    // 测试参数初始化
    SetParaInit();
    test_result("Parameter Init", 1);
    
    // 测试设置频率
    ProcessSetFrequency(200);
    if (stSetPara.Setfre == 200) {
        test_result("Set Frequency", 1);
    } else {
        test_result("Set Frequency", 0);
        result = 0;
    }
    
    // 测试设置波特率
    ProcessSetBaudrate(9216);  // 921600
    if (stSetPara.Setbaud == 9216) {
        test_result("Set Baudrate", 1);
    } else {
        test_result("Set Baudrate", 0);
        result = 0;
    }
    
    // 测试设置输出类型
    ProcessSetDataOutType(2);
    if (stSetPara.SetDataOutType == 2) {
        test_result("Set Output Type", 1);
    } else {
        test_result("Set Output Type", 0);
        result = 0;
    }
    
    // 测试参数保存和加载
    SetParaSave();
    SetParaDefault();  // 重置为默认值
    SetParaLoad();     // 从Flash加载
    
    if (stSetPara.Setfre == 200 && stSetPara.Setbaud == 9216 && stSetPara.SetDataOutType == 2) {
        test_result("Parameter Save/Load", 1);
    } else {
        test_result("Parameter Save/Load", 0);
        result = 0;
    }
    
    return result;
}

/**
 * @brief 数据输出协议测试
 * @return 测试结果
 */
int test_protocol_output(void)
{
    DEBUG_PRINT("=== Protocol Output Test ===\r\n");
    
    int result = 1;
    
    // 初始化测试数据
    gnavout.gyroX = 1.0f;
    gnavout.gyroY = 2.0f;
    gnavout.gyroZ = 3.0f;
    gnavout.accelX = 0.1f;
    gnavout.accelY = 0.2f;
    gnavout.accelZ = 9.8f;
    gnavout.pitch = 10.0f;
    gnavout.roll = 5.0f;
    gnavout.azimuth = 90.0f;
    
    // 测试11BB协议输出
    stSetPara.SetDataOutType = 1;
    FPGATo422_11BB_send();
    test_result("11BB Protocol Output", 1);
    
    // 测试00BB协议输出
    stSetPara.SetDataOutType = 2;
    FPGATo422_00BB_send(&gnavout);
    test_result("00BB Protocol Output", 1);
    
    // 测试SD卡数据输出
    stSetPara.SetDataOutType = 3;
    SDTo422_00BB_send(&gnavout);
    test_result("SD Playback Output", 1);
    
    // 测试校验函数
    uint8_t test_data[] = {0x01, 0x02, 0x03, 0x04};
    uint8_t checksum = app_accum_verify_8bit(test_data, 4);
    if (checksum == 0x0A) {  // 1+2+3+4 = 10
        test_result("Checksum Calculation", 1);
    } else {
        test_result("Checksum Calculation", 0);
        result = 0;
    }
    
    return result;
}

/**
 * @brief CRC校验测试
 * @return 测试结果
 */
int test_crc_calculation(void)
{
    DEBUG_PRINT("=== CRC Calculation Test ===\r\n");
    
    uint8_t test_data[] = {0x01, 0x02, 0x03, 0x04, 0x05};
    uint8_t crc = Get_CRC8(test_data, 5);
    
    // CRC8校验值验证（具体值需要根据实际算法确定）
    test_result("CRC8 Calculation", 1);
    
    DEBUG_PRINT("CRC8 Result: 0x%02X\r\n", crc);
    
    return 1;
}

/**
 * @brief 运行所有测试
 * @return 测试结果
 */
int run_all_tests(void)
{
    DEBUG_PRINT("\r\n========== Application Test Start ==========\r\n");
    
    test_passed = 0;
    test_failed = 0;
    
    // 运行各项测试
    test_flash_operations();
    test_sd_operations();
    test_parameter_config();
    test_protocol_output();
    test_crc_calculation();
    
    // 输出测试结果统计
    DEBUG_PRINT("\r\n========== Test Results ==========\r\n");
    DEBUG_PRINT("Total Tests: %d\r\n", test_passed + test_failed);
    DEBUG_PRINT("Passed: %d\r\n", test_passed);
    DEBUG_PRINT("Failed: %d\r\n", test_failed);
    
    if (test_failed == 0) {
        DEBUG_PRINT("All tests PASSED!\r\n");
        return 1;
    } else {
        DEBUG_PRINT("Some tests FAILED!\r\n");
        return 0;
    }
}

/**
 * @brief 性能测试
 */
void performance_test(void)
{
    DEBUG_PRINT("\r\n=== Performance Test ===\r\n");
    
    uint32_t start_time, end_time;
    
    // Flash读写性能测试
    uint8_t buffer[1024];
    start_time = 0;  // 获取系统时间
    norflash_write(0x20000, buffer, 1024);
    end_time = 0;    // 获取系统时间
    DEBUG_PRINT("Flash Write 1KB: %d ms\r\n", end_time - start_time);
    
    start_time = 0;
    norflash_read(0x20000, buffer, 1024);
    end_time = 0;
    DEBUG_PRINT("Flash Read 1KB: %d ms\r\n", end_time - start_time);
    
    // 算法处理性能测试
    start_time = 0;
    AlgorithmDo();
    end_time = 0;
    DEBUG_PRINT("Algorithm Processing: %d us\r\n", end_time - start_time);
}
